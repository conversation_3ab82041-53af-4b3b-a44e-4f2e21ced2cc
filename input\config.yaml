# IX浏览器配置
ix_browser:
  # 创建浏览器配置
  create_profile:
    basic:
      color: "#CC9966"           # 窗口颜色
      name: "注册中"            #窗口名称
      group_id: 190011           # 分组id 默认分组为1
      tag: ["自动注册"]          # 标签名称 (多个请传数组格式,分隔)
      site_id: "22"             # 默认打开页面（21 其他平台 22	空白页）
      site_url: ""              # 指定平台地址  site_id为21时必填
    proxy:
      proxy_type: "socks5"      # 代理类型 默认：direct (direct/http/https/socks5/ssh)
      proxy_ip: "na.proxys5.net"                    # 代理ip (proxy_type非direct时必填)
      proxy_port: "6200"        # 代理端口 (proxy_type非direct时必填)
      proxy_user: "max970790-zone-custom"           # 代理用户名基础部分
      proxy_password: "max7758258"                  # 代理密码
      enable_bypass: "0"        # 直连白名单 0:关闭 1:开启 默认：0
      bypass_list: ""           # 不走代理的域名，多个通过换行间隔

  # 打开浏览器配置
  open_profile:
    profile_id: 3195            # 窗口序号 (运行时指定)

# 注册设置
registration_settings:
  interval_min: 60              # 最小间隔时间（秒）
  interval_max: 300             # 最大间隔时间（秒）
  target_count: 8               # 目标注册数量

# Cloudflare API配置
cloudflare:
  api_tokens:
    "<EMAIL>": "****************************************"
    "<EMAIL>": "1RFFIGESGPHN0kfXszPwofTUDwzYzecjzt2K55n8"

# Capsolver人机验证解决配置
capsolver:
  api_key: "CAP-CE2A3DE7A3F83A04839B991AE859957C663379F212F991F527EBDD88C9BBE3C2"                    # Capsolver API密钥（请在扩展中配置）

# 邮件获取配置
email_retrieval:
  headless: false                   # 无头模式开关 (true: 无头模式, false: 显示窗口)
