"use strict";(()=>{var Z=Object.create;var _=Object.defineProperty;var ee=Object.getOwnPropertyDescriptor;var te=Object.getOwnPropertyNames;var ne=Object.getPrototypeOf,re=Object.prototype.hasOwnProperty;var oe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var ie=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of te(t))!re.call(e,o)&&o!==r&&_(e,o,{get:()=>t[o],enumerable:!(n=ee(t,o))||n.enumerable});return e};var ae=(e,t,r)=>(r=e!=null?Z(ne(e)):{},ie(t||!e||!e.__esModule?_(r,"default",{value:e,enumerable:!0}):r,e));var O=oe((Ce,C)=>{"use strict";var h=typeof Reflect=="object"?Reflect:null,I=h&&typeof h.apply=="function"?h.apply:function(t,r,n){return Function.prototype.apply.call(t,r,n)},y;h&&typeof h.ownKeys=="function"?y=h.ownKeys:Object.getOwnPropertySymbols?y=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:y=function(t){return Object.getOwnPropertyNames(t)};function se(e){console&&console.warn&&console.warn(e)}var k=Number.isNaN||function(t){return t!==t};function l(){l.init.call(this)}C.exports=l;C.exports.once=fe;l.EventEmitter=l;l.prototype._events=void 0;l.prototype._eventsCount=0;l.prototype._maxListeners=void 0;var S=10;function v(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(l,"defaultMaxListeners",{enumerable:!0,get:function(){return S},set:function(e){if(typeof e!="number"||e<0||k(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");S=e}});l.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};l.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||k(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function B(e){return e._maxListeners===void 0?l.defaultMaxListeners:e._maxListeners}l.prototype.getMaxListeners=function(){return B(this)};l.prototype.emit=function(t){for(var r=[],n=1;n<arguments.length;n++)r.push(arguments[n]);var o=t==="error",i=this._events;if(i!==void 0)o=o&&i.error===void 0;else if(!o)return!1;if(o){var a;if(r.length>0&&(a=r[0]),a instanceof Error)throw a;var f=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw f.context=a,f}var c=i[t];if(c===void 0)return!1;if(typeof c=="function")I(c,this,r);else for(var s=c.length,u=A(c,s),n=0;n<s;++n)I(u[n],this,r);return!0};function R(e,t,r,n){var o,i,a;if(v(r),i=e._events,i===void 0?(i=e._events=Object.create(null),e._eventsCount=0):(i.newListener!==void 0&&(e.emit("newListener",t,r.listener?r.listener:r),i=e._events),a=i[t]),a===void 0)a=i[t]=r,++e._eventsCount;else if(typeof a=="function"?a=i[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),o=B(e),o>0&&a.length>o&&!a.warned){a.warned=!0;var f=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");f.name="MaxListenersExceededWarning",f.emitter=e,f.type=t,f.count=a.length,se(f)}return e}l.prototype.addListener=function(t,r){return R(this,t,r,!1)};l.prototype.on=l.prototype.addListener;l.prototype.prependListener=function(t,r){return R(this,t,r,!0)};function ce(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function P(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},o=ce.bind(n);return o.listener=r,n.wrapFn=o,o}l.prototype.once=function(t,r){return v(r),this.on(t,P(this,t,r)),this};l.prototype.prependOnceListener=function(t,r){return v(r),this.prependListener(t,P(this,t,r)),this};l.prototype.removeListener=function(t,r){var n,o,i,a,f;if(v(r),o=this._events,o===void 0)return this;if(n=o[t],n===void 0)return this;if(n===r||n.listener===r)--this._eventsCount===0?this._events=Object.create(null):(delete o[t],o.removeListener&&this.emit("removeListener",t,n.listener||r));else if(typeof n!="function"){for(i=-1,a=n.length-1;a>=0;a--)if(n[a]===r||n[a].listener===r){f=n[a].listener,i=a;break}if(i<0)return this;i===0?n.shift():le(n,i),n.length===1&&(o[t]=n[0]),o.removeListener!==void 0&&this.emit("removeListener",t,f||r)}return this};l.prototype.off=l.prototype.removeListener;l.prototype.removeAllListeners=function(t){var r,n,o;if(n=this._events,n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):n[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[t]),this;if(arguments.length===0){var i=Object.keys(n),a;for(o=0;o<i.length;++o)a=i[o],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(r=n[t],typeof r=="function")this.removeListener(t,r);else if(r!==void 0)for(o=r.length-1;o>=0;o--)this.removeListener(t,r[o]);return this};function D(e,t,r){var n=e._events;if(n===void 0)return[];var o=n[t];return o===void 0?[]:typeof o=="function"?r?[o.listener||o]:[o]:r?ue(o):A(o,o.length)}l.prototype.listeners=function(t){return D(this,t,!0)};l.prototype.rawListeners=function(t){return D(this,t,!1)};l.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):W.call(e,t)};l.prototype.listenerCount=W;function W(e){var t=this._events;if(t!==void 0){var r=t[e];if(typeof r=="function")return 1;if(r!==void 0)return r.length}return 0}l.prototype.eventNames=function(){return this._eventsCount>0?y(this._events):[]};function A(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function le(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function ue(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}function fe(e,t){return new Promise(function(r,n){function o(a){e.removeListener(t,i),n(a)}function i(){typeof e.removeListener=="function"&&e.removeListener("error",o),r([].slice.call(arguments))}H(e,t,i,{once:!0}),t!=="error"&&pe(e,o,{once:!0})})}function pe(e,t,r){typeof e.on=="function"&&H(e,"error",t,r)}function H(e,t,r,n){if(typeof e.on=="function")n.once?e.once(t,r):e.on(t,r);else if(typeof e.addEventListener=="function")e.addEventListener(t,function o(i){n.once&&e.removeEventListener(t,o),r(i)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var N=ae(O());var w,b=0,d=new Array(256);for(let e=0;e<256;e++)d[e]=(e+256).toString(16).substring(1);var de=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let r=new Uint8Array(t);return e.getRandomValues(r),r}}return t=>{let r=[];for(let n=t;n>0;n--)r.push(Math.floor(Math.random()*256));return r}})(),F=4096;function q(){(w===void 0||b+16>F)&&(b=0,w=de(F));let e=Array.prototype.slice.call(w,b,b+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,d[e[0]]+d[e[1]]+d[e[2]]+d[e[3]]+"-"+d[e[4]]+d[e[5]]+"-"+d[e[6]]+d[e[7]]+"-"+d[e[8]]+d[e[9]]+"-"+d[e[10]]+d[e[11]]+d[e[12]]+d[e[13]]+d[e[14]]+d[e[15]]}var me={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,r)=>L(r)+L(e[r])+t,0):0},L=e=>me[typeof e](e),g=class extends N.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(r=>{Array.isArray(r)?r.forEach(n=>this._emit(n)):this._emit(r)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,r){return this._send([{event:t,payload:r}])}getEvents(){return this._events}on(t,r){return super.on(t,n=>{r({...n,respond:o=>this.send(n.eventResponseKey,o)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),r=t[0],n=`${r.event}.${q()}`,o=n+".result";return new Promise((i,a)=>{let f=[],c=s=>{if(s!==void 0&&s._chunkSplit){let u=s._chunkSplit;f=[...f,...s.data],u.lastChunk&&(this.off(o,c),i(f))}else this.off(o,c),i(s)};this.on(o,c);try{let s=t.map(u=>({...u,payload:{data:u.payload,eventResponseKey:o}}));this.wall.send(s)}catch(s){let u="Message length exceeded maximum allowed length.";if(s.message===u&&Array.isArray(r.payload)){let p=L(r);if(p>this._maxMessageSize){let m=Math.ceil(p/this._maxMessageSize),G=Math.ceil(r.payload.length/m),T=r.payload;for(let x=0;x<m;x++){let J=Math.min(T.length,G);this.wall.send([{event:r.event,payload:{_chunkSplit:{count:m,lastChunk:x===m-1},data:T.splice(0,J)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var j=(e,t)=>{window.addEventListener("message",r=>{if(r.source===window&&r.data.from!==void 0&&r.data.from===t){let n=r.data[0],o=e.getEvents();for(let i in o)i===n.event&&o[i](n.payload)}},!1)};function K(e){setInterval(function(){let t=document.querySelector("textarea[id=c-b-t]");if(t){let r=t.getAttribute("data-function"),n=t.value;window[r]&&window[r](n),t.remove()}},1e3),setInterval(function(){let t=document.querySelector("textarea[id=t-a-c]");if(t){let r=t.value.trim().split(`
`);t.remove();let n=null;for(let o=0;o<r.length;o++){let i=JSON.parse(r[o]);i.type==="source"?(i.value==="window"&&(n=window),i.value==="document"&&(n=document)):i.type==="property"?n=n[i.value]:i.type==="method"?i.args&&i.args.length?n=n[i.value](...i.args):n=n[i.value]():i.type==="index"&&(n=n[i.value])}}},1e3)}var ge="hCaptcha";function U(e){let t=function(n){var f;let o={captchaType:"reCaptcha",widgetId:n.id,version:"v2",sitekey:null,action:null,s:null,callback:null,enterprise:!!((f=window==null?void 0:window.grecaptcha)!=null&&f.enterprise),containerId:null,bindedButtonId:null,status:"ready",invisible:!1},i=!1;e:for(let c in n)if(typeof n[c]=="object"){for(let s in n[c])if(n[c][s]&&n[c][s].classList&&n[c][s].classList.contains("grecaptcha-badge")){i=!0;break e}}if(i){o.version="v3",o.captchaType="reCaptcha3";for(let c in n){let s=n[c];if(typeof s=="object")for(let u in s)typeof s[u]=="string"&&s[u]=="fullscreen"&&(o.version="v2",o.captchaType="reCaptcha")}}let a;for(let c in n)if(n[c]&&n[c].nodeType)if(n[c].id)o.containerId=n[c].id;else if(n[c].dataset.sitekey)n[c].id="recaptcha-container-"+Date.now(),o.containerId=n[c].id;else{if(!a){a=n[c];continue}if(n[c].isSameNode(a)){n[c].id="recaptcha-container-"+Date.now(),o.containerId=n[c].id;break}}for(let c in n){let s=n[c];if(typeof s=="object"){for(let u in s)if(s[u]!==null&&typeof s[u]=="object"&&s[u].sitekey!==void 0&&s[u].action!==void 0)for(let p in s[u]){if(p==="sitekey"&&(o.sitekey=s[u][p]),p==="action"&&(o.action=s[u][p]),p==="s"&&(o.s=s[u][p]),["callback","promise-callback"].includes(p)){let m=s[u][p];o.callback=m}if(p==="bind"&&s[u][p])if(typeof s[u][p]=="string")o.bindedButtonId=s[u][p];else{let m=s[u][p];m.id===void 0&&(m.id="recaptchaBindedElement"+n.id),o.bindedButtonId=m.id}p==="size"&&s[u][p]==="invisible"&&(o.invisible=!0)}}}if(typeof o.callback=="function"){let c="reCaptchaWidgetCallback"+n.id;window[c]=o.callback,o.callback=c}if(o.captchaType==="reCaptcha"){let c=r();o.action=c}return o};function r(){let n=document.querySelector('iframe[title="reCAPTCHA"]'),o=n==null?void 0:n.getAttribute("src");return o?new URL(o).searchParams.get("sa"):null}return setInterval(()=>{if(window.___grecaptcha_cfg!==void 0&&window.___grecaptcha_cfg.clients!==void 0)for(let n in window.___grecaptcha_cfg.clients){let o=window.___grecaptcha_cfg.clients[n],i=t(o);e.send("registerCaptchaWidget",i).then()}},1e3)}var ye="funCaptcha";var ve="cloudflare";var z="awsCaptcha",$="0";function X(e){setInterval(()=>{let t=document.querySelector("#captcha-container");if(!t||!t.querySelector(".amzn-captcha-lang-selector"))return;let n={captchaType:z,widgetId:$,containerId:t.id,status:"ready"};e.send("registerCaptchaWidget",n).then()},1e3)}var Y=async e=>{be(e).then(),K(e)};async function be(e){let{data:t}=await e.send("config");!t.useCapsolver||((t.enabledForRecaptcha||t.enabledForRecaptchaV3)&&U(e),t.enabledForAwsCaptcha&&X(e))}var Q=new g({listen(e){},send(e){let t={...e,from:"bex-dom"};window.postMessage(t,"*")}});j(Q,"bex-content-script");Y(Q);})();
