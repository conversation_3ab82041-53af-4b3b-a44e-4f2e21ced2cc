{"manifest_version": 3, "default_locale": "en", "icons": {"16": "icons/icon-16x16.png", "48": "icons/icon-48x48.png", "128": "icons/icon-128x128.png"}, "version": "1.16.0", "permissions": ["storage", "contextMenus", "webRequest"], "host_permissions": ["*://*/*"], "action": {"default_icon": "icons/icon-48x48.png", "default_popup": "www/index.html#/popup", "default_title": "Captcha Solver: Auto captcha solving service"}, "devtools_page": "www/index.html#/devtools", "background": {"service_worker": "background.js"}, "content_scripts": [{"all_frames": true, "run_at": "document_start", "matches": ["http://*/*", "https://*/*"], "css": ["assets/content.css"], "js": ["my-content-script.js", "image-to-text.js"]}, {"matches": ["*://*.hcaptcha.com/captcha/*"], "js": ["hcaptcha-recognition.js"], "run_at": "document_end", "all_frames": true, "match_about_blank": true}, {"matches": ["*://*.google.com/recaptcha/*", "*://*.recaptcha.net/recaptcha/*", "*://recaptcha.net/recaptcha/*"], "js": ["recaptcha-recognition.js"], "run_at": "document_end", "all_frames": true, "match_about_blank": true}, {"matches": ["http://*/*", "https://*/*"], "js": ["aws-recognition.js"], "run_at": "document_end", "all_frames": true, "match_about_blank": true}, {"all_frames": true, "run_at": "document_start", "matches": ["http://*/*", "https://*/*"], "js": ["cloudflare-content.js"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self';"}, "web_accessible_resources": [{"resources": ["*"], "matches": ["<all_urls>"]}], "name": "Captcha Solver: Auto captcha solving service", "short_name": "Captcha Solver: Auto captcha solving service", "description": "AI-powered CAPTCHA solver solution designed for Web Scraping."}