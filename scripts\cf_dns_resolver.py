#!/usr/bin/env python3
"""
Cloudflare DNS解析模块 - 为Resend域名自动配置DNS记录
读取add_domains_list.txt文件，为每个成功的域名添加邮件相关的DNS解析记录
"""

import json
import requests
import os
import re
import time
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from typing import List, Dict, Optional, Tuple

class DomainInfo:
    """域名信息类"""
    def __init__(self, domain: str, cf_account: str, dkim_key: str):
        self.domain = domain
        self.cf_account = cf_account
        self.dkim_key = dkim_key
        # 提取子域名前缀 (如 hr.maxupp.com -> hr)
        self.subdomain_prefix = domain.split('.')[0]
        # 提取主域名 (如 hr.maxupp.com -> maxupp.com)
        parts = domain.split('.')
        self.main_domain = '.'.join(parts[1:]) if len(parts) > 1 else domain
    
    def __str__(self):
        return f"{self.domain} ({self.cf_account})"


class DNSRecord:
    """DNS记录类"""
    def __init__(self, cf_account: str, main_domain: str, name: str, record_type: str, value: str, priority: Optional[int] = None):
        self.cf_account = cf_account
        self.main_domain = main_domain
        self.name = name
        self.record_type = record_type
        self.value = value
        self.priority = priority
    
    def __str__(self):
        priority_str = f" (优先级:{self.priority})" if self.priority else ""
        return f"{self.name} [{self.record_type}] -> {self.value}{priority_str}"


class CloudflareDNSResolver:
    """Cloudflare DNS解析器"""
    
    def __init__(self, config_file: str = "input/config.yaml"):
        self.config_file = config_file
        self.api_tokens = self._load_api_tokens()
        self.session_pool = defaultdict(requests.Session)
        self.request_delay = 0.1  # 请求间隔，避免API限制
        
        # DNS记录模板配置
        self.dns_templates = {
            "mx_value": "feedback-smtp.ap-northeast-1.amazonses.com",
            "mx_priority": 10,
            "spf_value": "v=spf1 include:amazonses.com ~all",
            "dmarc_value": "v=DMARC1; p=reject;"
        }
    
    def _load_api_tokens(self) -> Dict[str, Dict[str, str]]:
        """从config.yaml加载Cloudflare API tokens"""
        if not os.path.exists(self.config_file):
            print(f"❌ 未找到配置文件: {self.config_file}")
            return {}
        
        try:
            with open(self.config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            
            cloudflare_config = config.get("cloudflare", {})
            api_tokens = cloudflare_config.get("api_tokens", {})
            
            if not api_tokens:
                print("❌ 配置文件中未找到Cloudflare API tokens")
                print("请在config.yaml中添加以下配置：")
                print("cloudflare:")
                print("  api_tokens:")
                print('    "<EMAIL>": "your_api_token_here"')
                return {}
            
            # 转换格式以兼容现有代码
            formatted_tokens = {}
            for email, token in api_tokens.items():
                formatted_tokens[email.strip()] = {"api_token": token}
            
            return formatted_tokens
            
        except yaml.YAMLError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return {}
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return {}
    
    def parse_add_domains_list(self, file_path: str = "output/add_domains_list.txt") -> List[DomainInfo]:
        """解析add_domains_list.txt文件"""
        domains = []
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return domains
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式提取域名信息
            domain_pattern = r'域名 \d+:\s*\n\s*域名: ([^\n]+)\s*\n\s*CF账号: ([^\n]+)\s*\n.*?\n.*?\n.*?\n\s*DKIM记录: ([^\n]+)\s*\n\s*状态: ✅ 完整成功'
            
            matches = re.findall(domain_pattern, content, re.MULTILINE | re.DOTALL)
            
            for match in matches:
                domain_name = match[0].strip()
                cf_account = match[1].strip()
                dkim_key = match[2].strip()
                
                if domain_name and cf_account and dkim_key:
                    domains.append(DomainInfo(domain_name, cf_account, dkim_key))
            
            print(f"✅ 成功解析 {len(domains)} 个域名")
            return domains
            
        except Exception as e:
            print(f"❌ 解析文件失败: {e}")
            return domains
    
    def generate_dns_records(self, domains: List[DomainInfo]) -> Dict[str, List[DNSRecord]]:
        """生成DNS记录，按类型分组"""
        records_by_type = {
            "MX": [],
            "TXT_SPF": [],
            "TXT_DKIM": [],
            "TXT_DMARC": []
        }
        
        processed_main_domains = set()  # 用于DMARC记录去重
        
        for domain_info in domains:
            # 1. MX记录: send.{subdomain_prefix}
            mx_record = DNSRecord(
                cf_account=domain_info.cf_account,
                main_domain=domain_info.main_domain,
                name=f"send.{domain_info.subdomain_prefix}",
                record_type="MX",
                value=self.dns_templates["mx_value"],
                priority=self.dns_templates["mx_priority"]
            )
            records_by_type["MX"].append(mx_record)
            
            # 2. SPF记录: send.{subdomain_prefix}
            spf_record = DNSRecord(
                cf_account=domain_info.cf_account,
                main_domain=domain_info.main_domain,
                name=f"send.{domain_info.subdomain_prefix}",
                record_type="TXT",
                value=self.dns_templates["spf_value"]
            )
            records_by_type["TXT_SPF"].append(spf_record)
            
            # 3. DKIM记录: resend._domainkey.{subdomain_prefix}
            dkim_record = DNSRecord(
                cf_account=domain_info.cf_account,
                main_domain=domain_info.main_domain,
                name=f"resend._domainkey.{domain_info.subdomain_prefix}",
                record_type="TXT",
                value=domain_info.dkim_key
            )
            records_by_type["TXT_DKIM"].append(dkim_record)
            
            # 4. DMARC记录: _dmarc (每个主域名只添加一次)
            domain_key = f"{domain_info.cf_account}:{domain_info.main_domain}"
            if domain_key not in processed_main_domains:
                dmarc_record = DNSRecord(
                    cf_account=domain_info.cf_account,
                    main_domain=domain_info.main_domain,
                    name="_dmarc",
                    record_type="TXT",
                    value=self.dns_templates["dmarc_value"]
                )
                records_by_type["TXT_DMARC"].append(dmarc_record)
                processed_main_domains.add(domain_key)
        
        return records_by_type
    
    def get_zone_id(self, cf_account: str, domain: str) -> Optional[str]:
        """获取域名的Zone ID"""
        token_info = self.api_tokens.get(cf_account)
        if not token_info:
            print(f"❌ 找不到账户 {cf_account} 的 API Token")
            return None
        
        headers = {
            "Authorization": f"Bearer {token_info['api_token']}",
            "Content-Type": "application/json"
        }
        
        session = self.session_pool[cf_account]
        
        try:
            resp = session.get(f"https://api.cloudflare.com/client/v4/zones?name={domain}", headers=headers)
            if resp.status_code == 200:
                result = resp.json()
                zones = result.get("result", [])
                if zones:
                    return zones[0]["id"]
            return None
        except Exception as e:
            print(f"❌ 获取Zone ID失败 {domain}: {e}")
            return None
    
    def delete_existing_record(self, session: requests.Session, zone_id: str, headers: Dict, record_type: str, name: str) -> bool:
        """删除已存在的DNS记录"""
        try:
            url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records?type={record_type}&name={name}"
            resp = session.get(url, headers=headers)
            
            if resp.status_code == 200:
                result = resp.json()
                records = result.get("result", [])
                
                for record in records:
                    delete_url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records/{record['id']}"
                    session.delete(delete_url, headers=headers)
                
                return len(records) > 0
            return False
        except Exception:
            return False
    
    def create_dns_record(self, record: DNSRecord) -> bool:
        """创建单个DNS记录"""
        token_info = self.api_tokens.get(record.cf_account)
        if not token_info:
            print(f"❌ 找不到账户 {record.cf_account} 的 API Token")
            return False
        
        # 获取Zone ID
        zone_id = self.get_zone_id(record.cf_account, record.main_domain)
        if not zone_id:
            print(f"❌ 找不到域名 {record.main_domain} 的Zone ID")
            return False
        
        headers = {
            "Authorization": f"Bearer {token_info['api_token']}",
            "Content-Type": "application/json"
        }
        
        session = self.session_pool[record.cf_account]
        
        try:
            # 删除已存在的同类型同名记录
            self.delete_existing_record(session, zone_id, headers, record.record_type, record.name)
            
            # 准备请求数据
            value = record.value
            if record.record_type == "TXT" and not (value.startswith('"') and value.endswith('"')):
                value = f'"{value}"'
            
            payload = {
                "type": record.record_type,
                "name": record.name,
                "content": value,
                "ttl": 1,
                "proxied": False
            }
            
            if record.record_type == "MX" and record.priority is not None:
                payload["priority"] = record.priority
            
            # 创建DNS记录
            url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records"
            resp = session.post(url, headers=headers, json=payload)
            
            if resp.status_code == 200 and resp.json().get("success"):
                print(f"✅ 成功添加: {record.main_domain} -> {record}")
                return True
            else:
                error_msg = resp.json().get("errors", [{}])[0].get("message", "未知错误")
                print(f"❌ 添加失败: {record.main_domain} -> {record} - {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ 创建DNS记录异常: {record} - {e}")
            return False
    
    def batch_create_records(self, records: List[DNSRecord], record_type_name: str, max_batch_size: int = 100) -> Tuple[int, int]:
        """批量创建DNS记录"""
        if not records:
            return 0, 0
        
        print(f"\n🚀 开始处理 {record_type_name} 记录 ({len(records)} 条)")
        print("=" * 60)
        
        # 分批处理，每批不超过max_batch_size条
        total_success = 0
        total_processed = 0
        
        for i in range(0, len(records), max_batch_size):
            batch = records[i:i + max_batch_size]
            batch_num = i // max_batch_size + 1
            total_batches = (len(records) + max_batch_size - 1) // max_batch_size
            
            print(f"\n📋 处理第 {batch_num}/{total_batches} 批 ({len(batch)} 条记录)")
            
            # 使用线程池处理当前批次
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(self.create_dns_record, record) for record in batch]
                
                batch_success = 0
                for j, future in enumerate(as_completed(futures), 1):
                    result = future.result()
                    if result:
                        batch_success += 1
                    
                    # 显示进度
                    if j % 10 == 0 or j == len(batch):
                        print(f"   进度: {j}/{len(batch)} 完成")
                    
                    # API限制延迟
                    time.sleep(self.request_delay)
            
            total_success += batch_success
            total_processed += len(batch)
            
            print(f"📊 第 {batch_num} 批完成: 成功 {batch_success}/{len(batch)}")
            
            # 批次间延迟
            if i + max_batch_size < len(records):
                print("⏳ 等待 2 秒后处理下一批...")
                time.sleep(2)
        
        print(f"\n📊 {record_type_name} 记录处理完成: 成功 {total_success}/{total_processed}")
        return total_success, total_processed
    
    def run_dns_resolution(self, max_batch_size: int = 100):
        """运行DNS解析主流程"""
        print("🚀 Cloudflare DNS解析器启动")
        print("💡 专门为Resend域名配置邮件相关DNS记录")
        print("=" * 60)
        
        # 检查API tokens
        if not self.api_tokens:
            print("❌ 没有可用的Cloudflare API tokens，程序退出")
            return
        
        print(f"✅ 已加载 {len(self.api_tokens)} 个Cloudflare账户")
        for email in self.api_tokens.keys():
            print(f"   - {email}")
        
        # 解析域名列表
        domains = self.parse_add_domains_list()
        if not domains:
            print("❌ 没有找到可处理的域名，程序退出")
            return
        
        print(f"\n📋 待处理域名列表:")
        for i, domain in enumerate(domains, 1):
            print(f"   {i:2d}. {domain}")
        
        # 生成DNS记录
        print(f"\n🔧 生成DNS记录...")
        records_by_type = self.generate_dns_records(domains)
        
        print(f"📊 DNS记录统计:")
        for record_type, records in records_by_type.items():
            print(f"   {record_type}: {len(records)} 条")
        
        # 自动开始DNS解析（去除确认提示）
        print(f"\n🚀 自动开始DNS解析，每批最多处理 {max_batch_size} 条记录")
        
        # 按类型顺序处理DNS记录
        processing_order = [
            ("MX", "MX记录"),
            ("TXT_SPF", "SPF记录"),
            ("TXT_DKIM", "DKIM记录"),
            ("TXT_DMARC", "DMARC记录")
        ]
        
        total_success = 0
        total_processed = 0
        
        for record_type, type_name in processing_order:
            records = records_by_type[record_type]
            if records:
                success, processed = self.batch_create_records(records, type_name, max_batch_size)
                total_success += success
                total_processed += processed
                
                # 类型间延迟
                if record_type != processing_order[-1][0]:  # 不是最后一个类型
                    print("⏳ 等待 3 秒后处理下一类型...")
                    time.sleep(3)
        
        # 最终统计
        print("\n" + "=" * 60)
        print("🎉 DNS解析任务完成！")
        print(f"📊 最终统计:")
        print(f"   总记录数: {total_processed}")
        print(f"   成功记录: {total_success}")
        print(f"   失败记录: {total_processed - total_success}")
        print(f"   成功率: {(total_success/total_processed*100):.1f}%" if total_processed > 0 else "0%")


def main():
    """主函数"""
    resolver = CloudflareDNSResolver()
    resolver.run_dns_resolution()


if __name__ == "__main__":
    main()
