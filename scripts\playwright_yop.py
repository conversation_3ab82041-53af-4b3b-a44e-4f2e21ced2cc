"""
基于Playwright和CapSolver扩展的Yopmail邮件获取模块
优化版本：去除扩展检测功能，由主程序负责扩展管理
"""

import asyncio
import logging
import os
import random
import time
import yaml
from typing import Optional, Dict, Any

from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page

# 尝试导入playwright-stealth，如果失败则使用备用方案
try:
    from playwright_stealth import Stealth
    STEALTH_AVAILABLE = True
    stealth_instance = Stealth()
except ImportError:
    STEALTH_AVAILABLE = False
    stealth_instance = None


class CaptchaError(Exception):
    """人机验证错误异常"""
    pass


class HumanBehaviorSimulator:
    """人类行为模拟器"""

    @staticmethod
    async def random_delay(min_ms: int = 1000, max_ms: int = 3000) -> None:
        """随机延迟"""
        delay = random.randint(min_ms, max_ms)
        await asyncio.sleep(delay / 1000)

    @staticmethod
    async def simulate_mouse_movement(page: Page) -> None:
        """模拟鼠标移动"""
        try:
            viewport = page.viewport_size
            if viewport:
                x = random.randint(100, viewport['width'] - 100)
                y = random.randint(100, viewport['height'] - 100)
                await page.mouse.move(x, y, steps=random.randint(5, 15))
                await HumanBehaviorSimulator.random_delay(200, 800)
        except Exception:
            pass  # 忽略鼠标移动错误

    @staticmethod
    async def simulate_reading_time(page: Page) -> None:
        """模拟阅读时间"""
        await HumanBehaviorSimulator.random_delay(1000, 2000)

    @staticmethod
    async def simulate_thinking_time(page: Page) -> None:
        """模拟思考时间"""
        await HumanBehaviorSimulator.random_delay(1500, 3000)


class IntelligentCaptchaHandler:
    """智能CAPTCHA处理器"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    async def handle_captcha_intelligently(self, page: Page) -> bool:
        """
        智能处理CAPTCHA - 只在需要时触发，模拟人类行为

        Args:
            page: Playwright页面对象

        Returns:
            bool: 是否处理了CAPTCHA
        """
        try:
            # 检测CAPTCHA的多种选择器
            captcha_selectors = [
                'iframe[src*="captcha"]',
                'iframe[src*="hcaptcha"]',
                'iframe[src*="turnstile"]',
                '.captcha-container',
                '#captcha',
                '[data-captcha]'
            ]

            captcha_found = False
            found_selector = None

            for selector in captcha_selectors:
                if await page.locator(selector).count() > 0:
                    captcha_found = True
                    found_selector = selector
                    self.logger.info(f"🔍 检测到CAPTCHA: {selector}")
                    break

            if not captcha_found:
                return False  # 没有CAPTCHA，直接返回

            self.logger.info("🤖 开始智能CAPTCHA处理流程...")

            # 1. 模拟人类发现CAPTCHA的反应时间 (1-3秒)
            discovery_time = random.randint(1000, 3000)
            self.logger.info(f"⏱️ 模拟发现反应时间: {discovery_time/1000:.1f}秒")
            await asyncio.sleep(discovery_time / 1000)

            # 2. 模拟鼠标移动到CAPTCHA区域
            await HumanBehaviorSimulator.simulate_mouse_movement(page)

            # 3. 模拟思考决策时间 (1-3秒)
            thinking_time = random.randint(1000, 3000)
            self.logger.info(f"🤔 模拟思考时间: {thinking_time/1000:.1f}秒")
            await asyncio.sleep(thinking_time / 1000)

            # 4. 手动触发CapSolver (保持原有API不变)
            self.logger.info("🚀 触发CAPTCHA解决...")
            await page.evaluate("""
                () => {
                    // 发送消息给CapSolver扩展
                    window.postMessage({ type: 'capsolverSolve' }, '*');
                }
            """)

            # 5. 等待CAPTCHA解决完成 (最多30秒)
            self.logger.info("⏳ 等待CAPTCHA解决完成...")
            try:
                await page.wait_for_function("""
                    () => {
                        const captchaFrames = document.querySelectorAll('iframe[src*="captcha"], iframe[src*="hcaptcha"], iframe[src*="turnstile"]');
                        return captchaFrames.length === 0;
                    }
                """, timeout=30000)

                self.logger.info("✅ CAPTCHA已成功解决")
                return True

            except Exception as wait_error:
                self.logger.warning(f"⚠️ CAPTCHA解决超时或失败: {wait_error}")
                return False

        except Exception as error:
            self.logger.error(f"❌ 智能CAPTCHA处理失败: {error}")
            return False


class ExtensionError(Exception):
    """扩展相关错误异常"""
    pass


def load_email_config() -> dict:
    """加载邮件获取相关配置"""
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "input", "config.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 获取邮件获取配置
        email_config = config.get('email_retrieval', {})
        capsolver_config = config.get('capsolver', {})

        return {
            'headless': email_config.get('headless', False),  # 默认显示窗口
            'api_key': capsolver_config.get('api_key', '')    # API密钥
        }
    except Exception as e:
        logging.getLogger(__name__).warning(f"⚠️ 加载配置文件失败，使用默认配置: {e}")
        return {
            'headless': False,
            'api_key': ''
        }


def load_headless_config() -> bool:
    """加载无头模式配置（向后兼容）"""
    return load_email_config()['headless']




class PlaywrightYopManager:
    """基于Playwright和Capsolver扩展的Yopmail管理器"""
    
    # Yopmail相关常量
    YOPMAIL_BASE_URL = "https://yopmail.com"
    INBOX_SELECTOR = 'iframe[name="ifinbox"]'  # 收件箱iframe
    EMAIL_CONTENT_SELECTOR = 'iframe[name="ifmail"]'  # 邮件内容iframe
    
    # 超时设置
    DEFAULT_TIMEOUT = 30000  # 30秒
    EMAIL_WAIT_TIMEOUT = 300000  # 5分钟
    CAPTCHA_TIMEOUT = 120000  # 2分钟（固定等待时间）
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, enable_debug: bool = False):
        """
        初始化Yopmail管理器
        
        Args:
            config: 配置字典（保留兼容性，实际不使用）
            enable_debug: 是否启用调试模式
        """
        self.config = config or {}
        self.enable_debug = enable_debug
        self.logger = logging.getLogger(__name__)
        
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None

        # 状态管理 - 用于智能导航和页面复用
        self.current_username = None  # 当前打开的账户
        self.current_page = None      # 当前收件箱页面
        self.last_access_time = None  # 上次访问时间

        # 扩展状态
        self.extension_loaded = False
        self.extension_path = None

        # 智能CAPTCHA处理器
        self.captcha_handler = IntelligentCaptchaHandler(self.logger)

        # 验证扩展配置（在初始化extension_path之后）
        self._validate_extension_config()
    
    def _validate_extension_config(self):
        """验证扩展配置（现在由主程序负责扩展安装检测）"""
        # 使用相对于脚本根目录的扩展路径
        script_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        extension_path = os.path.join(script_root, "extensions", "capsolver")

        if os.path.exists(extension_path):
            self.extension_path = extension_path
            self.logger.info(f"✅ 扩展路径设置: {extension_path}")
        else:
            self.logger.warning(f"⚠️ 扩展路径不存在: {extension_path}")
            self.extension_path = None

        self.logger.info("💡 扩展安装和配置由主程序负责")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 不关闭浏览器，保持复用
        # 参数用于异常处理，但这里不需要使用
        pass
    
    async def start_browser(self):
        """启动浏览器和扩展"""
        if self.browser and not self.browser.is_connected():
            self.browser = None
            self.context = None

        if not self.browser:
            # 加载无头模式配置
            headless_mode = load_headless_config()
            mode_text = "无头模式" if headless_mode else "有头模式"
            self.logger.info(f"🚀 启动浏览器（{mode_text}）...")

            playwright = await async_playwright().start()

            # 修复Windows路径问题：将反斜杠转换为正斜杠
            extension_path_fixed = self.extension_path.replace('\\', '/') if self.extension_path else None
            if not extension_path_fixed:
                raise ValueError("扩展路径未设置")

            self.logger.info(f"📁 扩展路径（修复后）: {extension_path_fixed}")

            # 使用官方推荐的扩展加载方式 + 反检测配置
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="playwright_yop_")

            # 反检测浏览器参数（恢复）
            anti_detection_args = [
                f"--disable-extensions-except={extension_path_fixed}",
                f"--load-extension={extension_path_fixed}",
                "--disable-blink-features=AutomationControlled",  # 恢复
                "--disable-dev-shm-usage",                        # 恢复
                "--no-first-run",                                 # 恢复
                "--disable-default-apps",                         # 恢复
                "--disable-popup-blocking"                        # 恢复
            ]

            self.context = await playwright.chromium.launch_persistent_context(
                temp_dir,  # 使用临时目录作为用户数据目录
                channel='chromium',  # 官方推荐：允许headless模式运行扩展
                headless=headless_mode,  # 根据配置决定是否无头模式
                args=anti_detection_args
                # 完全让playwright-stealth处理所有反检测配置
            )

            self.browser = self.context.browser
            self.extension_loaded = True



            # 关闭默认的空白标签页（保留至少一个页面）
            try:
                pages = self.context.pages
                if len(pages) > 1:  # 只有在有多个页面时才关闭空白页
                    for page in pages[:-1]:  # 保留最后一个页面
                        if page.url == "about:blank" or page.url == "chrome://newtab/":
                            await page.close()
                            self.logger.info("🗑️ 已关闭空白标签页")
            except Exception as e:
                self.logger.warning(f"⚠️ 关闭空白标签页时出错: {e}")

            self.logger.info(f"✅ 浏览器启动成功（{mode_text}）")
            self.logger.info(f"📦 CapSolver扩展已加载: {self.extension_path}")

            # 配置CapSolver扩展API密钥（只在有头模式下配置）
            config = load_email_config()
            api_key = config['api_key']
            if api_key and not headless_mode:
                await self._configure_capsolver_extension(api_key)

    async def close_browser(self):
        """关闭浏览器实例"""
        try:
            if self.current_page and not self.current_page.is_closed():
                await self.current_page.close()
                self.current_page = None
                self.logger.info("📄 已关闭当前页面")

            if self.context:
                await self.context.close()
                self.context = None
                self.logger.info("🌐 已关闭浏览器上下文")

            if self.browser:
                await self.browser.close()
                self.browser = None
                self.logger.info("🚪 已关闭浏览器实例")

            # 重置状态
            self.extension_loaded = False
            self.current_username = None
            self.last_access_time = None

        except Exception as e:
            self.logger.warning(f"⚠️ 关闭浏览器时出现警告: {e}")

    async def _configure_capsolver_extension(self, api_key: str):
        """配置CapSolver扩展API密钥（基于成功测试脚本的方法）"""
        try:
            self.logger.info("⚙️ 配置CapSolver扩展API密钥...")

            # 确保context存在
            if not self.context:
                self.logger.error("❌ 浏览器上下文未初始化")
                return

            # 按照官方文档获取Service Worker和扩展ID
            service_workers = self.context.service_workers
            if not service_workers:
                self.logger.info("⏳ 等待Service Worker加载...")
                service_worker = await self.context.wait_for_event('serviceworker')
            else:
                service_worker = service_workers[0]

            extension_id = service_worker.url.split('/')[2]
            self.logger.info(f"✅ 扩展ID: {extension_id}")

            # 使用现有页面或等待页面可用
            pages = self.context.pages
            if pages:
                popup_page = pages[0]
                await popup_page.goto(f"chrome-extension://{extension_id}/www/index.html#/popup")
            else:
                # 如果没有现有页面，等待一下再尝试
                await asyncio.sleep(2)
                popup_page = await self.context.new_page()
                await popup_page.goto(f"chrome-extension://{extension_id}/www/index.html#/popup")

            await asyncio.sleep(2)

            # 查找API输入框并填入密钥
            api_input = await popup_page.query_selector("input[placeholder*='API']")
            if api_input:
                await popup_page.fill("input[placeholder*='API']", api_key)
                await popup_page.click('body')  # 触发保存

                # 等待余额元素出现，说明API密钥配置成功
                try:
                    balance_element = await popup_page.wait_for_selector("span.text-balance.ml12", timeout=10000)
                    if balance_element:
                        balance_text = await balance_element.text_content()
                        self.logger.info(f"✅ CapSolver API密钥已配置，当前余额: {balance_text}")
                    else:
                        self.logger.warning("⚠️ 未检测到余额信息，API密钥可能配置失败")
                except Exception as e:
                    self.logger.warning(f"⚠️ 等待余额元素超时: {e}")
            else:
                self.logger.warning("⚠️ 未找到API输入框")

            # 不关闭页面，保留用于后续使用

        except Exception as e:
            self.logger.error(f"❌ 配置CapSolver扩展时出错: {e}")

    async def _wait_for_page_load(self, page: Page, timeout: int = DEFAULT_TIMEOUT):
        """等待页面加载完成"""
        try:
            # 只等待DOM加载完成，不等待网络空闲（避免卡住）
            await page.wait_for_load_state('domcontentloaded', timeout=timeout)
            # 给页面一点时间渲染
            await asyncio.sleep(2)
        except Exception as e:
            self.logger.warning(f"⚠️ 页面加载等待超时: {e}")


    
    async def _navigate_to_inbox(self, page: Page, username: str) -> bool:
        """导航到指定用户的收件箱"""
        try:
            # 智能导航：如果已经在正确的收件箱，跳过导航
            if (self.current_username == username and
                self.current_page and
                not self.current_page.is_closed() and
                self.last_access_time and
                time.time() - self.last_access_time < 300):  # 5分钟内

                self.logger.info(f"♻️ 复用现有收件箱页面: {username}")
                return True

            self.logger.info(f"🔄 导航到收件箱: {username}")

            # 导航到yopmail首页
            await page.goto(self.YOPMAIL_BASE_URL, wait_until='domcontentloaded')
            await self._wait_for_page_load(page)

            # 输入用户名
            await page.fill('input[name="login"]', username)

            # 尝试按Enter键，如果失败则点击按钮
            try:
                await page.locator('input[name="login"]').press('Enter')
            except:
                await page.locator('input[name="login"] + * button').click()

            # 等待页面加载
            await self._wait_for_page_load(page, timeout=10000)

            # 更新状态
            self.current_username = username
            self.current_page = page
            self.last_access_time = time.time()

            self.logger.info(f"✅ 成功导航到收件箱: {username}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 导航到收件箱失败: {e}")
            return False
    
    async def _check_captcha_detected(self, page: Page) -> bool:
        """检测CAPTCHA: 检查页面中是否存在CAPTCHA元素"""
        try:
            captcha_detected = await page.evaluate("""
                () => {
                    // 检查常见的CAPTCHA元素
                    const recaptcha = document.querySelector('.g-recaptcha, [data-sitekey], iframe[src*="recaptcha"]');
                    const hcaptcha = document.querySelector('.h-captcha, [data-sitekey], iframe[src*="hcaptcha"]');
                    const cloudflare = document.querySelector('.cf-turnstile, [data-sitekey]');
                    const general = document.querySelector('[id*="captcha"], [class*="captcha"], [name*="captcha"]');

                    // 检查CapSolver按钮（表示CapSolver检测到了CAPTCHA）
                    const capsolverButton = document.querySelector('#capsolver-solver-tip-button');

                    return !!(recaptcha || hcaptcha || cloudflare || general || capsolverButton);
                }
            """)

            if captcha_detected:
                self.logger.info("🔍 检测到CAPTCHA元素")

            return captcha_detected
        except Exception as e:
            self.logger.warning(f"⚠️ 检查CAPTCHA检测信号时出错: {e}")
            return False

    async def _check_captcha_solved(self, page: Page) -> bool:
        """验证结果: 检查 g-recaptcha-response 元素是否有值"""
        try:
            captcha_solved = await page.evaluate("""
                () => {
                    const response = document.querySelector('[name="g-recaptcha-response"]');
                    return response && response.value && response.value.length > 0;
                }
            """)
            return captcha_solved
        except Exception as e:
            self.logger.warning(f"⚠️ 检查CAPTCHA完成信号时出错: {e}")
            return False

    async def _wait_for_captcha_completion(self, page: Page) -> bool:
        """等待完成: 使用 captchaSolvedCallback 回调函数"""
        try:
            # 设置解决完成回调
            await page.evaluate("""
                () => {
                    window.captchaSolvedCallback = () => {
                        console.log('CapSolver CAPTCHA解决完成！');
                        window.captchaSolved = true;
                    };
                    window.captchaSolved = false;
                }
            """)

            self.logger.info("🤖 等待CapSolver自动解决CAPTCHA...")

            # 等待CAPTCHA解决，固定2分钟
            start_time = time.time()
            while time.time() - start_time < self.CAPTCHA_TIMEOUT / 1000:
                # 检查回调是否被触发
                callback_triggered = await page.evaluate("() => window.captchaSolved === true")
                if callback_triggered:
                    self.logger.info("✅ CAPTCHA回调触发，验证完成！")
                    return True

                # 检查g-recaptcha-response是否有值
                if await self._check_captcha_solved(page):
                    self.logger.info("✅ CAPTCHA响应已填充，验证完成！")
                    return True

                await asyncio.sleep(2)  # 每2秒检查一次

            self.logger.error("❌ CAPTCHA解决超时")
            return False

        except Exception as e:
            self.logger.error(f"❌ 等待CAPTCHA完成时出错: {e}")
            return False
    
    async def _handle_captcha_with_retry(self, page: Page, max_retries: int = 3) -> bool:
        """
        处理CAPTCHA验证，支持重试机制

        Args:
            page: 页面对象
            max_retries: 最大重试次数（默认3次）

        Returns:
            bool: 是否成功处理CAPTCHA
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"🔍 第{attempt + 1}次检查CAPTCHA状态...")

                # 检测信号: 监控 #capsolver-solver-tip-button 元素出现
                captcha_detected = await self._check_captcha_detected(page)

                if not captcha_detected:
                    self.logger.info("✅ 未检测到CAPTCHA，可以继续")
                    return True

                # 验证结果: 检查 g-recaptcha-response 元素是否有值
                if await self._check_captcha_solved(page):
                    self.logger.info("✅ CAPTCHA已解决，可以继续")
                    return True

                # CAPTCHA存在且未解决，使用智能处理器
                self.logger.info(f"🤖 检测到CAPTCHA，第{attempt + 1}次尝试智能解决...")

                # 使用智能CAPTCHA处理器（模拟人类行为）
                if await self.captcha_handler.handle_captcha_intelligently(page):
                    # 等待完成: 使用 captchaSolvedCallback 回调函数
                    if await self._wait_for_captcha_completion(page):
                        self.logger.info("✅ CAPTCHA智能解决成功！")
                        return True

                # 如果不是最后一次尝试，等待更长时间再重试（避免打断解决过程）
                if attempt < max_retries - 1:
                    retry_wait = 30 + (attempt * 10)  # 递增等待：30秒、40秒、50秒
                    self.logger.warning(f"⚠️ 第{attempt + 1}次CAPTCHA解决超时，等待{retry_wait}秒后重试...")
                    await asyncio.sleep(retry_wait)  # 给CapSolver更多时间，并且递增等待
                    # 不立即刷新页面，先检查是否已经解决
                    continue

            except Exception as e:
                self.logger.error(f"❌ 第{attempt + 1}次处理CAPTCHA时出错: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue

        # 所有重试都失败了
        self.logger.error(f"❌ CAPTCHA处理失败，已重试{max_retries}次")
        return False
    
    async def get_verification_link(self, username: str, max_wait_minutes: int = 5) -> Optional[str]:
        """
        获取指定用户的Resend验证链接

        Args:
            username: Yopmail用户名
            max_wait_minutes: 最大等待时间（分钟，保留兼容性但实际使用重试机制）

        Returns:
            验证链接或None
        """
        try:
            self.logger.info(f"📧 开始获取验证邮件: {username}")

            # 确保浏览器已启动（不强制重启，避免浪费资源）
            if not self.browser or not self.browser.is_connected():
                self.logger.info("🔄 启动浏览器实例...")
                await self.start_browser()
            
            # 创建新页面或复用现有页面
            if not self.current_page or self.current_page.is_closed():
                if self.context:
                    self.current_page = await self.context.new_page()
                    # 为新页面应用stealth插件
                    if STEALTH_AVAILABLE and stealth_instance:
                        await stealth_instance.apply_stealth_async(self.current_page)
                        self.logger.info("🥷 已为新页面应用反检测插件")
                    else:
                        self.logger.warning("⚠️ playwright-stealth不可用，使用基础反检测")


                else:
                    raise Exception("浏览器上下文未初始化")

            page = self.current_page
            if not page:
                raise Exception("页面创建失败")

            # 导航到收件箱
            if not await self._navigate_to_inbox(page, username):
                raise Exception("导航到收件箱失败")

            # 获取验证邮件（最多重试3次，每次间隔5秒）
            # 每次重试都会刷新收件箱并检测CAPTCHA
            verification_link = await self._get_resend_email_with_retry(page, max_retries=3)
            
            if verification_link:
                self.logger.info(f"✅ 成功获取验证链接: {verification_link}")
                # 第3点实现：获取完邮件后关闭浏览器
                await self.close_browser()
                return verification_link
            else:
                self.logger.warning(f"⚠️ 未找到验证邮件: {username}")
                # 第3点实现：即使失败也关闭浏览器
                await self.close_browser()
                return None

        except Exception as e:
            self.logger.error(f"❌ 获取验证链接失败: {e}")
            # 第3点实现：出现异常时也关闭浏览器
            await self.close_browser()
            return None

    async def _get_resend_email_with_retry(self, page: Page, max_retries: int = 3) -> Optional[str]:
        """
        获取Resend验证邮件，支持重试机制

        Args:
            page: 页面对象
            max_retries: 最大重试次数（默认3次）

        Returns:
            验证链接或None
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"📧 第{attempt + 1}次尝试获取Resend验证邮件...")

                # 只有第2次及以后才刷新收件箱（第1次不刷新，直接查找）
                if attempt > 0:
                    self.logger.info(f"🔄 第{attempt + 1}次尝试：刷新收件箱页面...")
                    await page.reload(wait_until='domcontentloaded')
                    await self._wait_for_page_load(page, timeout=10000)

                # 刷新后检测是否有CAPTCHA
                if not await self._handle_captcha_with_retry(page, max_retries=3):
                    self.logger.warning(f"⚠️ 第{attempt + 1}次尝试：CAPTCHA处理失败")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(5)  # 等待5秒后重试
                        continue
                    else:
                        return None

                # CAPTCHA处理完成或无CAPTCHA，开始查找Resend邮件
                verification_link = await self._find_resend_email(page)
                if verification_link:
                    self.logger.info(f"✅ 第{attempt + 1}次尝试成功获取验证链接")
                    return verification_link

                # 如果不是最后一次尝试，等待5秒后重试
                if attempt < max_retries - 1:
                    self.logger.info(f"⏱️ 第{attempt + 1}次尝试未找到邮件，等待5秒后重试...")
                    await asyncio.sleep(5)

            except Exception as e:
                self.logger.error(f"❌ 第{attempt + 1}次获取邮件时出错: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue

        self.logger.warning(f"⚠️ 获取Resend邮件失败，已重试{max_retries}次")
        return None

    async def _find_resend_email(self, page: Page) -> Optional[str]:
        """在收件箱中查找Resend验证邮件"""
        try:
            # 等待收件箱iframe加载
            await page.wait_for_selector(self.INBOX_SELECTOR, timeout=10000)

            # 切换到收件箱iframe
            inbox_frame = page.frame_locator(self.INBOX_SELECTOR)

            # 调试：查看收件箱中所有按钮
            all_buttons = inbox_frame.locator('button')
            button_count = await all_buttons.count()
            self.logger.info(f"🔍 收件箱中找到 {button_count} 个按钮")

            for i in range(button_count):
                try:
                    button_text = await all_buttons.nth(i).text_content()
                    self.logger.info(f"  按钮 {i+1}: '{button_text}'")
                except:
                    self.logger.info(f"  按钮 {i+1}: 无法获取文本")

            # 查找包含"Resend"和"Confirm your Resend account"的邮件按钮（修复匹配逻辑）
            resend_button = inbox_frame.locator('button').filter(has_text="Resend").filter(has_text="Confirm your Resend account")
            resend_count = await resend_button.count()
            self.logger.info(f"🔍 精确匹配找到 {resend_count} 个Resend验证邮件按钮")

            if await resend_button.count() > 0:
                self.logger.info("📬 找到Resend认证邮件")

                # 点击邮件
                await resend_button.first.click()

                # 切换到邮件内容iframe（恢复原始逻辑）
                await page.wait_for_selector(self.EMAIL_CONTENT_SELECTOR, timeout=5000)
                content_frame = page.frame_locator(self.EMAIL_CONTENT_SELECTOR)

                # 直接查找"Confirm Account"链接
                confirm_link = content_frame.locator('a').filter(has_text="Confirm Account")

                if await confirm_link.count() > 0:
                    verification_url = await confirm_link.first.get_attribute('href')
                    if verification_url:
                        self.logger.info(f"✅ 成功提取验证链接: {verification_url}")
                        return verification_url

                self.logger.warning("⚠️ 邮件中未找到Confirm Account链接")
                return None
            else:
                self.logger.info("📭 未找到Resend认证邮件")
                return None

        except Exception as e:
            self.logger.warning(f"⚠️ 查找Resend邮件时出错: {e}")
            return None



    async def close(self):
        """关闭浏览器资源"""
        try:
            if self.context:
                await self.context.close()
                self.context = None

            if self.browser:
                await self.browser.close()
                self.browser = None

            self.logger.info("✅ 浏览器资源已关闭")

        except Exception as e:
            self.logger.warning(f"⚠️ 关闭浏览器时出错: {e}")


# 全局管理器实例
_global_manager: Optional[PlaywrightYopManager] = None


async def get_resend_verification_link(username: str, max_wait_minutes: int = 5) -> Optional[str]:
    """
    获取Resend验证链接的便捷函数

    Args:
        username: Yopmail用户名
        max_wait_minutes: 最大等待时间（分钟）

    Returns:
        验证链接或None
    """
    global _global_manager

    try:
        if not _global_manager:
            _global_manager = PlaywrightYopManager()

        return await _global_manager.get_verification_link(username, max_wait_minutes)

    except Exception as e:
        logging.getLogger(__name__).error(f"❌ 获取验证链接失败: {e}")
        return None


async def shutdown_global_manager():
    """关闭全局管理器"""
    global _global_manager

    if _global_manager:
        await _global_manager.close()
        _global_manager = None


# 测试函数
async def test_yopmail_verification():
    """测试Yopmail验证功能"""
    test_username = "test123"

    print(f"🧪 测试获取验证链接: {test_username}")

    async with PlaywrightYopManager() as manager:
        link = await manager.get_verification_link(test_username, max_wait_minutes=2)

        if link:
            print(f"✅ 测试成功，获取到链接: {link}")
        else:
            print("❌ 测试失败，未获取到链接")


if __name__ == "__main__":
    asyncio.run(test_yopmail_verification())
