var e,t=Object.defineProperty,n=(e,n,o)=>(((e,n,o)=>{n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[n]=o})(e,"symbol"!=typeof n?n+"":n,o),o);const o=function(){const e=document.createElement("link").relList;return e&&e.supports&&e.supports("modulepreload")?"modulepreload":"preload"}(),l={},r=function(e,t){return t&&0!==t.length?Promise.all(t.map((e=>{if((e=`${e}`)in l)return;l[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const r=document.createElement("link");return r.rel=t?"stylesheet":o,t||(r.as="script",r.crossOrigin=""),r.href=e,document.head.appendChild(r),t?new Promise(((t,n)=>{r.addEventListener("load",t),r.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e())):e()};
/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function a(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}const i={},s=[],u=()=>{},c=()=>!1,d=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),f=e=>e.startsWith("onUpdate:"),p=Object.assign,v=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},h=Object.prototype.hasOwnProperty,m=(e,t)=>h.call(e,t),g=Array.isArray,b=e=>"[object Map]"===E(e),y=e=>"[object Set]"===E(e),_=e=>"function"==typeof e,w=e=>"string"==typeof e,k=e=>"symbol"==typeof e,x=e=>null!==e&&"object"==typeof e,S=e=>(x(e)||_(e))&&_(e.then)&&_(e.catch),C=Object.prototype.toString,E=e=>C.call(e),T=e=>"[object Object]"===E(e),L=e=>w(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,q=a(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),O=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},P=/-(\w)/g,F=O((e=>e.replace(P,((e,t)=>t?t.toUpperCase():"")))),R=/\B([A-Z])/g,A=O((e=>e.replace(R,"-$1").toLowerCase())),V=O((e=>e.charAt(0).toUpperCase()+e.slice(1))),M=O((e=>e?`on${V(e)}`:"")),I=(e,t)=>!Object.is(e,t),N=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},$=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},B=e=>{const t=parseFloat(e);return isNaN(t)?e:t},z=e=>{const t=w(e)?Number(e):NaN;return isNaN(t)?e:t};let D;const j=()=>D||(D="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function U(e){if(g(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],l=w(o)?Q(o):U(o);if(l)for(const e in l)t[e]=l[e]}return t}if(w(e)||x(e))return e}const H=/;(?![^(]*\))/g,W=/:([^]+)/,K=/\/\*[^]*?\*\//g;function Q(e){const t={};return e.replace(K,"").split(H).forEach((e=>{if(e){const n=e.split(W);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function G(e){let t="";if(w(e))t=e;else if(g(e))for(let n=0;n<e.length;n++){const o=G(e[n]);o&&(t+=o+" ")}else if(x(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Y=a("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function J(e){return!!e||""===e}const Z=e=>!(!e||!0!==e.__v_isRef),X=e=>w(e)?e:null==e?"":g(e)||x(e)&&(e.toString===C||!_(e.toString))?Z(e)?X(e.value):JSON.stringify(e,ee,2):String(e),ee=(e,t)=>Z(t)?ee(e,t.value):b(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[te(t,o)+" =>"]=n,e)),{})}:y(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>te(e)))}:k(t)?te(t):!x(t)||g(t)||T(t)?t:String(t),te=(e,t="")=>{var n;return k(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};
/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let ne,oe;class le{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ne,!e&&ne&&(this.index=(ne.scopes||(ne.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=ne;try{return ne=this,e()}finally{ne=t}}}on(){ne=this}off(){ne=this.parent}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function re(e){return new le(e)}const ae=new WeakSet;class ie{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ne&&ne.active&&ne.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,ae.has(this)&&(ae.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||de(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,Se(this),ve(this);const e=oe,t=_e;oe=this,_e=!0;try{return this.fn()}finally{he(this),oe=e,_e=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)be(e);this.deps=this.depsTail=void 0,Se(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?ae.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){me(this)&&this.run()}get dirty(){return me(this)}}let se,ue,ce=0;function de(e,t=!1){if(e.flags|=8,t)return e.next=ue,void(ue=e);e.next=se,se=e}function fe(){ce++}function pe(){if(--ce>0)return;if(ue){let e=ue;for(ue=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;se;){let n=se;for(se=void 0;n;){const o=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=o}}if(e)throw e}function ve(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function he(e){let t,n=e.depsTail,o=n;for(;o;){const e=o.prevDep;-1===o.version?(o===n&&(n=e),be(o),ye(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=e}e.deps=t,e.depsTail=n}function me(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ge(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ge(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===Ce)return;e.globalVersion=Ce;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!me(e))return void(e.flags&=-3);const n=oe,o=_e;oe=e,_e=!0;try{ve(e);const l=e.fn(e._value);(0===t.version||I(l,e._value))&&(e._value=l,t.version++)}catch(l){throw t.version++,l}finally{oe=n,_e=o,he(e),e.flags&=-3}}function be(e,t=!1){const{dep:n,prevSub:o,nextSub:l}=e;if(o&&(o.nextSub=l,e.prevSub=void 0),l&&(l.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)be(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function ye(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let _e=!0;const we=[];function ke(){we.push(_e),_e=!1}function xe(){const e=we.pop();_e=void 0===e||e}function Se(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=oe;oe=void 0;try{t()}finally{oe=e}}}let Ce=0;class Ee{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Te{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!oe||!_e||oe===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==oe)t=this.activeLink=new Ee(oe,this),oe.deps?(t.prevDep=oe.depsTail,oe.depsTail.nextDep=t,oe.depsTail=t):oe.deps=oe.depsTail=t,Le(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=oe.depsTail,t.nextDep=void 0,oe.depsTail.nextDep=t,oe.depsTail=t,oe.deps===t&&(oe.deps=e)}return t}trigger(e){this.version++,Ce++,this.notify(e)}notify(e){fe();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{pe()}}}function Le(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Le(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const qe=new WeakMap,Oe=Symbol(""),Pe=Symbol(""),Fe=Symbol("");function Re(e,t,n){if(_e&&oe){let t=qe.get(e);t||qe.set(e,t=new Map);let o=t.get(n);o||(t.set(n,o=new Te),o.map=t,o.key=n),o.track()}}function Ae(e,t,n,o,l,r){const a=qe.get(e);if(!a)return void Ce++;const i=e=>{e&&e.trigger()};if(fe(),"clear"===t)a.forEach(i);else{const l=g(e),r=l&&L(n);if(l&&"length"===n){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n===Fe||!k(n)&&n>=e)&&i(t)}))}else switch((void 0!==n||a.has(void 0))&&i(a.get(n)),r&&i(a.get(Fe)),t){case"add":l?r&&i(a.get("length")):(i(a.get(Oe)),b(e)&&i(a.get(Pe)));break;case"delete":l||(i(a.get(Oe)),b(e)&&i(a.get(Pe)));break;case"set":b(e)&&i(a.get(Oe))}}pe()}function Ve(e){const t=_t(e);return t===e?t:(Re(t,0,Fe),bt(e)?t:t.map(kt))}function Me(e){return Re(e=_t(e),0,Fe),e}const Ie={__proto__:null,[Symbol.iterator](){return Ne(this,Symbol.iterator,kt)},concat(...e){return Ve(this).concat(...e.map((e=>g(e)?Ve(e):e)))},entries(){return Ne(this,"entries",(e=>(e[1]=kt(e[1]),e)))},every(e,t){return Be(this,"every",e,t,void 0,arguments)},filter(e,t){return Be(this,"filter",e,t,(e=>e.map(kt)),arguments)},find(e,t){return Be(this,"find",e,t,kt,arguments)},findIndex(e,t){return Be(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Be(this,"findLast",e,t,kt,arguments)},findLastIndex(e,t){return Be(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Be(this,"forEach",e,t,void 0,arguments)},includes(...e){return De(this,"includes",e)},indexOf(...e){return De(this,"indexOf",e)},join(e){return Ve(this).join(e)},lastIndexOf(...e){return De(this,"lastIndexOf",e)},map(e,t){return Be(this,"map",e,t,void 0,arguments)},pop(){return je(this,"pop")},push(...e){return je(this,"push",e)},reduce(e,...t){return ze(this,"reduce",e,t)},reduceRight(e,...t){return ze(this,"reduceRight",e,t)},shift(){return je(this,"shift")},some(e,t){return Be(this,"some",e,t,void 0,arguments)},splice(...e){return je(this,"splice",e)},toReversed(){return Ve(this).toReversed()},toSorted(e){return Ve(this).toSorted(e)},toSpliced(...e){return Ve(this).toSpliced(...e)},unshift(...e){return je(this,"unshift",e)},values(){return Ne(this,"values",kt)}};function Ne(e,t,n){const o=Me(e),l=o[t]();return o===e||bt(e)||(l._next=l.next,l.next=()=>{const e=l._next();return e.value&&(e.value=n(e.value)),e}),l}const $e=Array.prototype;function Be(e,t,n,o,l,r){const a=Me(e),i=a!==e&&!bt(e),s=a[t];if(s!==$e[t]){const t=s.apply(e,r);return i?kt(t):t}let u=n;a!==e&&(i?u=function(t,o){return n.call(this,kt(t),o,e)}:n.length>2&&(u=function(t,o){return n.call(this,t,o,e)}));const c=s.call(a,u,o);return i&&l?l(c):c}function ze(e,t,n,o){const l=Me(e);let r=n;return l!==e&&(bt(e)?n.length>3&&(r=function(t,o,l){return n.call(this,t,o,l,e)}):r=function(t,o,l){return n.call(this,t,kt(o),l,e)}),l[t](r,...o)}function De(e,t,n){const o=_t(e);Re(o,0,Fe);const l=o[t](...n);return-1!==l&&!1!==l||!yt(n[0])?l:(n[0]=_t(n[0]),o[t](...n))}function je(e,t,n=[]){ke(),fe();const o=_t(e)[t].apply(e,n);return pe(),xe(),o}const Ue=a("__proto__,__v_isRef,__isVue"),He=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(k));function We(e){k(e)||(e=String(e));const t=_t(this);return Re(t,0,e),t.hasOwnProperty(e)}class Ke{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const o=this._isReadonly,l=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return l;if("__v_raw"===t)return n===(o?l?ct:ut:l?st:it).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const r=g(e);if(!o){let e;if(r&&(e=Ie[t]))return e;if("hasOwnProperty"===t)return We}const a=Reflect.get(e,t,St(e)?e:n);return(k(t)?He.has(t):Ue(t))?a:(o||Re(e,0,t),l?a:St(a)?r&&L(t)?a:a.value:x(a)?o?vt(a):ft(a):a)}}class Qe extends Ke{constructor(e=!1){super(!1,e)}set(e,t,n,o){let l=e[t];if(!this._isShallow){const t=gt(l);if(bt(n)||gt(n)||(l=_t(l),n=_t(n)),!g(e)&&St(l)&&!St(n))return!t&&(l.value=n,!0)}const r=g(e)&&L(t)?Number(t)<e.length:m(e,t),a=Reflect.set(e,t,n,St(e)?e:o);return e===_t(o)&&(r?I(n,l)&&Ae(e,"set",t,n):Ae(e,"add",t,n)),a}deleteProperty(e,t){const n=m(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Ae(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return k(t)&&He.has(t)||Re(e,0,t),n}ownKeys(e){return Re(e,0,g(e)?"length":Oe),Reflect.ownKeys(e)}}class Ge extends Ke{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Ye=new Qe,Je=new Ge,Ze=new Qe(!0),Xe=e=>e,et=e=>Reflect.getPrototypeOf(e);function tt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function nt(e,t){const n={get(n){const o=this.__v_raw,l=_t(o),r=_t(n);e||(I(n,r)&&Re(l,0,n),Re(l,0,r));const{has:a}=et(l),i=t?Xe:e?xt:kt;return a.call(l,n)?i(o.get(n)):a.call(l,r)?i(o.get(r)):void(o!==l&&o.get(n))},get size(){const t=this.__v_raw;return!e&&Re(_t(t),0,Oe),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,o=_t(n),l=_t(t);return e||(I(t,l)&&Re(o,0,t),Re(o,0,l)),t===l?n.has(t):n.has(t)||n.has(l)},forEach(n,o){const l=this,r=l.__v_raw,a=_t(r),i=t?Xe:e?xt:kt;return!e&&Re(a,0,Oe),r.forEach(((e,t)=>n.call(o,i(e),i(t),l)))}};p(n,e?{add:tt("add"),set:tt("set"),delete:tt("delete"),clear:tt("clear")}:{add(e){t||bt(e)||gt(e)||(e=_t(e));const n=_t(this);return et(n).has.call(n,e)||(n.add(e),Ae(n,"add",e,e)),this},set(e,n){t||bt(n)||gt(n)||(n=_t(n));const o=_t(this),{has:l,get:r}=et(o);let a=l.call(o,e);a||(e=_t(e),a=l.call(o,e));const i=r.call(o,e);return o.set(e,n),a?I(n,i)&&Ae(o,"set",e,n):Ae(o,"add",e,n),this},delete(e){const t=_t(this),{has:n,get:o}=et(t);let l=n.call(t,e);l||(e=_t(e),l=n.call(t,e)),o&&o.call(t,e);const r=t.delete(e);return l&&Ae(t,"delete",e,void 0),r},clear(){const e=_t(this),t=0!==e.size,n=e.clear();return t&&Ae(e,"clear",void 0,void 0),n}});return["keys","values","entries",Symbol.iterator].forEach((o=>{n[o]=function(e,t,n){return function(...o){const l=this.__v_raw,r=_t(l),a=b(r),i="entries"===e||e===Symbol.iterator&&a,s="keys"===e&&a,u=l[e](...o),c=n?Xe:t?xt:kt;return!t&&Re(r,0,s?Pe:Oe),{next(){const{value:e,done:t}=u.next();return t?{value:e,done:t}:{value:i?[c(e[0]),c(e[1])]:c(e),done:t}},[Symbol.iterator](){return this}}}}(o,e,t)})),n}function ot(e,t){const n=nt(e,t);return(t,o,l)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(m(n,o)&&o in t?n:t,o,l)}const lt={get:ot(!1,!1)},rt={get:ot(!1,!0)},at={get:ot(!0,!1)},it=new WeakMap,st=new WeakMap,ut=new WeakMap,ct=new WeakMap;function dt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>E(e).slice(8,-1))(e))}function ft(e){return gt(e)?e:ht(e,!1,Ye,lt,it)}function pt(e){return ht(e,!1,Ze,rt,st)}function vt(e){return ht(e,!0,Je,at,ut)}function ht(e,t,n,o,l){if(!x(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const r=l.get(e);if(r)return r;const a=dt(e);if(0===a)return e;const i=new Proxy(e,2===a?o:n);return l.set(e,i),i}function mt(e){return gt(e)?mt(e.__v_raw):!(!e||!e.__v_isReactive)}function gt(e){return!(!e||!e.__v_isReadonly)}function bt(e){return!(!e||!e.__v_isShallow)}function yt(e){return!!e&&!!e.__v_raw}function _t(e){const t=e&&e.__v_raw;return t?_t(t):e}function wt(e){return!m(e,"__v_skip")&&Object.isExtensible(e)&&$(e,"__v_skip",!0),e}const kt=e=>x(e)?ft(e):e,xt=e=>x(e)?vt(e):e;function St(e){return!!e&&!0===e.__v_isRef}function Ct(e){return Tt(e,!1)}function Et(e){return Tt(e,!0)}function Tt(e,t){return St(e)?e:new Lt(e,t)}class Lt{constructor(e,t){this.dep=new Te,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:_t(e),this._value=t?e:kt(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||bt(e)||gt(e);e=n?e:_t(e),I(e,t)&&(this._rawValue=e,this._value=n?e:kt(e),this.dep.trigger())}}function qt(e){return St(e)?e.value:e}const Ot={get:(e,t,n)=>"__v_raw"===t?e:qt(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const l=e[t];return St(l)&&!St(n)?(l.value=n,!0):Reflect.set(e,t,n,o)}};function Pt(e){return mt(e)?e:new Proxy(e,Ot)}class Ft{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Te(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ce-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&oe!==this)return de(this,!0),!0}get value(){const e=this.dep.track();return ge(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}const Rt={},At=new WeakMap;let Vt;function Mt(e,t,n=i){const{immediate:o,deep:l,once:r,scheduler:a,augmentJob:s,call:c}=n,d=e=>l?e:bt(e)||!1===l||0===l?It(e,1):It(e);let f,p,h,m,b=!1,y=!1;if(St(e)?(p=()=>e.value,b=bt(e)):mt(e)?(p=()=>d(e),b=!0):g(e)?(y=!0,b=e.some((e=>mt(e)||bt(e))),p=()=>e.map((e=>St(e)?e.value:mt(e)?d(e):_(e)?c?c(e,2):e():void 0))):p=_(e)?t?c?()=>c(e,2):e:()=>{if(h){ke();try{h()}finally{xe()}}const t=Vt;Vt=f;try{return c?c(e,3,[m]):e(m)}finally{Vt=t}}:u,t&&l){const e=p,t=!0===l?1/0:l;p=()=>It(e(),t)}const w=ne,k=()=>{f.stop(),w&&w.active&&v(w.effects,f)};if(r&&t){const e=t;t=(...t)=>{e(...t),k()}}let x=y?new Array(e.length).fill(Rt):Rt;const S=e=>{if(1&f.flags&&(f.dirty||e))if(t){const e=f.run();if(l||b||(y?e.some(((e,t)=>I(e,x[t]))):I(e,x))){h&&h();const n=Vt;Vt=f;try{const o=[e,x===Rt?void 0:y&&x[0]===Rt?[]:x,m];c?c(t,3,o):t(...o),x=e}finally{Vt=n}}}else f.run()};return s&&s(S),f=new ie(p),f.scheduler=a?()=>a(S,!1):S,m=e=>function(e,t=!1,n=Vt){if(n){let t=At.get(n);t||At.set(n,t=[]),t.push(e)}}(e,!1,f),h=f.onStop=()=>{const e=At.get(f);if(e){if(c)c(e,4);else for(const t of e)t();At.delete(f)}},t?o?S(!0):x=f.run():a?a(S.bind(null,!0),!0):f.run(),k.pause=f.pause.bind(f),k.resume=f.resume.bind(f),k.stop=k,k}function It(e,t=1/0,n){if(t<=0||!x(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,St(e))It(e.value,t,n);else if(g(e))for(let o=0;o<e.length;o++)It(e[o],t,n);else if(y(e)||b(e))e.forEach((e=>{It(e,t,n)}));else if(T(e)){for(const o in e)It(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&It(e[o],t,n)}return e}
/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Nt(e,t,n,o){try{return o?e(...o):e()}catch(l){Bt(l,t,n)}}function $t(e,t,n,o){if(_(e)){const l=Nt(e,t,n,o);return l&&S(l)&&l.catch((e=>{Bt(e,t,n)})),l}if(g(e)){const l=[];for(let r=0;r<e.length;r++)l.push($t(e[r],t,n,o));return l}}function Bt(e,t,n,o=!0){t&&t.vnode;const{errorHandler:l,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||i;if(t){let o=t.parent;const r=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,a))return;o=o.parent}if(l)return ke(),Nt(l,null,10,[e,r,a]),void xe()}!function(e,t,n,o=!0,l=!1){if(l)throw e;console.error(e)}(e,0,0,o,r)}const zt=[];let Dt=-1;const jt=[];let Ut=null,Ht=0;const Wt=Promise.resolve();let Kt=null;function Qt(e){const t=Kt||Wt;return e?t.then(this?e.bind(this):e):t}function Gt(e){if(!(1&e.flags)){const t=en(e),n=zt[zt.length-1];!n||!(2&e.flags)&&t>=en(n)?zt.push(e):zt.splice(function(e){let t=Dt+1,n=zt.length;for(;t<n;){const o=t+n>>>1,l=zt[o],r=en(l);r<e||r===e&&2&l.flags?t=o+1:n=o}return t}(t),0,e),e.flags|=1,Yt()}}function Yt(){Kt||(Kt=Wt.then(tn))}function Jt(e){g(e)?jt.push(...e):Ut&&-1===e.id?Ut.splice(Ht+1,0,e):1&e.flags||(jt.push(e),e.flags|=1),Yt()}function Zt(e,t,n=Dt+1){for(;n<zt.length;n++){const t=zt[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;zt.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function Xt(e){if(jt.length){const e=[...new Set(jt)].sort(((e,t)=>en(e)-en(t)));if(jt.length=0,Ut)return void Ut.push(...e);for(Ut=e,Ht=0;Ht<Ut.length;Ht++){const e=Ut[Ht];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}Ut=null,Ht=0}}const en=e=>null==e.id?2&e.flags?-1:1/0:e.id;function tn(e){try{for(Dt=0;Dt<zt.length;Dt++){const e=zt[Dt];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),Nt(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;Dt<zt.length;Dt++){const e=zt[Dt];e&&(e.flags&=-2)}Dt=-1,zt.length=0,Xt(),Kt=null,(zt.length||jt.length)&&tn()}}let nn=null,on=null;function ln(e){const t=nn;return nn=e,on=e&&e.type.__scopeId||null,t}function rn(e,t=nn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Tl(-1);const l=ln(t);let r;try{r=e(...n)}finally{ln(l),o._d&&Tl(1)}return r};return o._n=!0,o._c=!0,o._d=!0,o}function an(e,t){if(null===nn)return e;const n=lr(nn),o=e.dirs||(e.dirs=[]);for(let l=0;l<t.length;l++){let[e,r,a,s=i]=t[l];e&&(_(e)&&(e={mounted:e,updated:e}),e.deep&&It(r),o.push({dir:e,instance:n,value:r,oldValue:void 0,arg:a,modifiers:s}))}return e}function sn(e,t,n,o){const l=e.dirs,r=t&&t.dirs;for(let a=0;a<l.length;a++){const i=l[a];r&&(i.oldValue=r[a].value);let s=i.dir[o];s&&(ke(),$t(s,n,8,[e.el,i,e,t]),xe())}}const un=Symbol("_vte"),cn=e=>e.__isTeleport,dn=e=>e&&(e.disabled||""===e.disabled),fn=e=>e&&(e.defer||""===e.defer),pn=e=>"undefined"!=typeof SVGElement&&e instanceof SVGElement,vn=e=>"function"==typeof MathMLElement&&e instanceof MathMLElement,hn=(e,t)=>{const n=e&&e.to;if(w(n)){if(t){return t(n)}return null}return n},mn={name:"Teleport",__isTeleport:!0,process(e,t,n,o,l,r,a,i,s,u){const{mc:c,pc:d,pbc:f,o:{insert:p,querySelector:v,createText:h,createComment:m}}=u,g=dn(t.props);let{shapeFlag:b,children:y,dynamicChildren:_}=t;if(null==e){const e=t.el=h(""),u=t.anchor=h("");p(e,n,o),p(u,n,o);const d=(e,t)=>{16&b&&(l&&l.isCE&&(l.ce._teleportTarget=e),c(y,e,t,l,r,a,i,s))},f=()=>{const e=t.target=hn(t.props,v),n=_n(e,t,h,p);e&&("svg"!==a&&pn(e)?a="svg":"mathml"!==a&&vn(e)&&(a="mathml"),g||(d(e,n),yn(t,!1)))};g&&(d(n,u),yn(t,!0)),fn(t.props)?Ho((()=>{f(),t.el.__isMounted=!0}),r):f()}else{if(fn(t.props)&&!e.el.__isMounted)return void Ho((()=>{mn.process(e,t,n,o,l,r,a,i,s,u),delete e.el.__isMounted}),r);t.el=e.el,t.targetStart=e.targetStart;const c=t.anchor=e.anchor,p=t.target=e.target,h=t.targetAnchor=e.targetAnchor,m=dn(e.props),b=m?n:p,y=m?c:h;if("svg"===a||pn(p)?a="svg":("mathml"===a||vn(p))&&(a="mathml"),_?(f(e.dynamicChildren,_,b,l,r,a,i),Go(e,t,!0)):s||d(e,t,b,y,l,r,a,i,!1),g)m?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):gn(t,n,c,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=hn(t.props,v);e&&gn(t,e,null,u,0)}else m&&gn(t,p,h,u,1);yn(t,g)}},remove(e,t,n,{um:o,o:{remove:l}},r){const{shapeFlag:a,children:i,anchor:s,targetStart:u,targetAnchor:c,target:d,props:f}=e;if(d&&(l(u),l(c)),r&&l(s),16&a){const e=r||!dn(f);for(let l=0;l<i.length;l++){const r=i[l];o(r,t,n,e,!!r.dynamicChildren)}}},move:gn,hydrate:function(e,t,n,o,l,r,{o:{nextSibling:a,parentNode:i,querySelector:s,insert:u,createText:c}},d){const f=t.target=hn(t.props,s);if(f){const s=dn(t.props),p=f._lpa||f.firstChild;if(16&t.shapeFlag)if(s)t.anchor=d(a(e),t,i(e),n,o,l,r),t.targetStart=p,t.targetAnchor=p&&a(p);else{t.anchor=a(e);let i=p;for(;i;){if(i&&8===i.nodeType)if("teleport start anchor"===i.data)t.targetStart=i;else if("teleport anchor"===i.data){t.targetAnchor=i,f._lpa=t.targetAnchor&&a(t.targetAnchor);break}i=a(i)}t.targetAnchor||_n(f,t,c,u),d(p&&a(p),t,f,n,o,l,r)}yn(t,s)}return t.anchor&&a(t.anchor)}};function gn(e,t,n,{o:{insert:o},m:l},r=2){0===r&&o(e.targetAnchor,t,n);const{el:a,anchor:i,shapeFlag:s,children:u,props:c}=e,d=2===r;if(d&&o(a,t,n),(!d||dn(c))&&16&s)for(let f=0;f<u.length;f++)l(u[f],t,n,2);d&&o(i,t,n)}const bn=mn;function yn(e,t){const n=e.ctx;if(n&&n.ut){let o,l;for(t?(o=e.el,l=e.anchor):(o=e.targetStart,l=e.targetAnchor);o&&o!==l;)1===o.nodeType&&o.setAttribute("data-v-owner",n.uid),o=o.nextSibling;n.ut()}}function _n(e,t,n,o){const l=t.targetStart=n(""),r=t.targetAnchor=n("");return l[un]=r,e&&(o(l,e),o(r,e)),r}const wn=Symbol("_leaveCb"),kn=Symbol("_enterCb");const xn=[Function,Array],Sn={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:xn,onEnter:xn,onAfterEnter:xn,onEnterCancelled:xn,onBeforeLeave:xn,onLeave:xn,onAfterLeave:xn,onLeaveCancelled:xn,onBeforeAppear:xn,onAppear:xn,onAfterAppear:xn,onAppearCancelled:xn},Cn=e=>{const t=e.subTree;return t.component?Cn(t.component):t};function En(e){let t=e[0];if(e.length>1)for(const n of e)if(n.type!==_l){t=n;break}return t}const Tn={name:"BaseTransition",props:Sn,setup(e,{slots:t}){const n=Ql(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Wn((()=>{e.isMounted=!0})),Gn((()=>{e.isUnmounting=!0})),e}();return()=>{const l=t.default&&Rn(t.default(),!0);if(!l||!l.length)return;const r=En(l),a=_t(e),{mode:i}=a;if(o.isLeaving)return On(r);const s=Pn(r);if(!s)return On(r);let u=qn(s,a,o,n,(e=>u=e));s.type!==_l&&Fn(s,u);let c=n.subTree&&Pn(n.subTree);if(c&&c.type!==_l&&!Fl(s,c)&&Cn(n).type!==_l){let e=qn(c,a,o,n);if(Fn(c,e),"out-in"===i&&s.type!==_l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,c=void 0},On(r);"in-out"===i&&s.type!==_l?e.delayLeave=(e,t,n)=>{Ln(o,c)[String(c.key)]=c,e[wn]=()=>{t(),e[wn]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{n(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return r}}};function Ln(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function qn(e,t,n,o,l){const{appear:r,mode:a,persisted:i=!1,onBeforeEnter:s,onEnter:u,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:f,onLeave:p,onAfterLeave:v,onLeaveCancelled:h,onBeforeAppear:m,onAppear:b,onAfterAppear:y,onAppearCancelled:_}=t,w=String(e.key),k=Ln(n,e),x=(e,t)=>{e&&$t(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),g(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},C={mode:a,persisted:i,beforeEnter(t){let o=s;if(!n.isMounted){if(!r)return;o=m||s}t[wn]&&t[wn](!0);const l=k[w];l&&Fl(e,l)&&l.el[wn]&&l.el[wn](),x(o,[t])},enter(e){let t=u,o=c,l=d;if(!n.isMounted){if(!r)return;t=b||u,o=y||c,l=_||d}let a=!1;const i=e[kn]=t=>{a||(a=!0,x(t?l:o,[e]),C.delayedLeave&&C.delayedLeave(),e[kn]=void 0)};t?S(t,[e,i]):i()},leave(t,o){const l=String(e.key);if(t[kn]&&t[kn](!0),n.isUnmounting)return o();x(f,[t]);let r=!1;const a=t[wn]=n=>{r||(r=!0,o(),x(n?h:v,[t]),t[wn]=void 0,k[l]===e&&delete k[l])};k[l]=e,p?S(p,[t,a]):a()},clone(e){const r=qn(e,t,n,o,l);return l&&l(r),r}};return C}function On(e){if(Nn(e))return(e=Il(e)).children=null,e}function Pn(e){if(!Nn(e))return cn(e.type)&&e.children?En(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&_(n.default))return n.default()}}function Fn(e,t){6&e.shapeFlag&&e.component?(e.transition=t,Fn(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Rn(e,t=!1,n){let o=[],l=0;for(let r=0;r<e.length;r++){let a=e[r];const i=null==n?a.key:String(n)+String(null!=a.key?a.key:r);a.type===bl?(128&a.patchFlag&&l++,o=o.concat(Rn(a.children,t,i))):(t||a.type!==_l)&&o.push(null!=i?Il(a,{key:i}):a)}if(l>1)for(let r=0;r<o.length;r++)o[r].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function An(e,t){return _(e)?(()=>p({name:e.name},t,{setup:e}))():e}function Vn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Mn(e,t,n,o,l=!1){if(g(e))return void e.forEach(((e,r)=>Mn(e,t&&(g(t)?t[r]:t),n,o,l)));if(In(o)&&!l)return void(512&o.shapeFlag&&o.type.__asyncResolved&&o.component.subTree.component&&Mn(e,t,n,o.component.subTree));const r=4&o.shapeFlag?lr(o.component):o.el,a=l?null:r,{i:s,r:u}=e,c=t&&t.r,d=s.refs===i?s.refs={}:s.refs,f=s.setupState,p=_t(f),h=f===i?()=>!1:e=>m(p,e);if(null!=c&&c!==u&&(w(c)?(d[c]=null,h(c)&&(f[c]=null)):St(c)&&(c.value=null)),_(u))Nt(u,s,12,[a,d]);else{const t=w(u),o=St(u);if(t||o){const i=()=>{if(e.f){const n=t?h(u)?f[u]:d[u]:u.value;l?g(n)&&v(n,r):g(n)?n.includes(r)||n.push(r):t?(d[u]=[r],h(u)&&(f[u]=d[u])):(u.value=[r],e.k&&(d[e.k]=u.value))}else t?(d[u]=a,h(u)&&(f[u]=a)):o&&(u.value=a,e.k&&(d[e.k]=a))};a?(i.id=-1,Ho(i,n)):i()}}}j().requestIdleCallback,j().cancelIdleCallback;const In=e=>!!e.type.__asyncLoader,Nn=e=>e.type.__isKeepAlive;function $n(e,t){zn(e,"a",t)}function Bn(e,t){zn(e,"da",t)}function zn(e,t,n=Kl){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(jn(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Nn(e.parent.vnode)&&Dn(o,t,n,e),e=e.parent}}function Dn(e,t,n,o){const l=jn(t,e,o,!0);Yn((()=>{v(o[t],l)}),n)}function jn(e,t,n=Kl,o=!1){if(n){const l=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...o)=>{ke();const l=Jl(n),r=$t(t,n,e,o);return l(),xe(),r});return o?l.unshift(r):l.push(r),r}}const Un=e=>(t,n=Kl)=>{er&&"sp"!==e||jn(e,((...e)=>t(...e)),n)},Hn=Un("bm"),Wn=Un("m"),Kn=Un("bu"),Qn=Un("u"),Gn=Un("bum"),Yn=Un("um"),Jn=Un("sp"),Zn=Un("rtg"),Xn=Un("rtc");function eo(e,t=Kl){jn("ec",e,t)}function to(e,t){return function(e,t,n=!0,o=!1){const l=nn||Kl;if(l){const n=l.type;if("components"===e){const e=rr(n,!1);if(e&&(e===t||e===F(t)||e===V(F(t))))return n}const r=oo(l[e]||n[e],t)||oo(l.appContext[e],t);return!r&&o?n:r}}("components",e,!0,t)||e}const no=Symbol.for("v-ndc");function oo(e,t){return e&&(e[t]||e[F(t)]||e[V(F(t))])}function lo(e,t,n,o){let l;const r=n&&n[o],a=g(e);if(a||w(e)){let n=!1;a&&mt(e)&&(n=!bt(e),e=Me(e)),l=new Array(e.length);for(let o=0,a=e.length;o<a;o++)l[o]=t(n?kt(e[o]):e[o],o,void 0,r&&r[o])}else if("number"==typeof e){l=new Array(e);for(let n=0;n<e;n++)l[n]=t(n+1,n,void 0,r&&r[n])}else if(x(e))if(e[Symbol.iterator])l=Array.from(e,((e,n)=>t(e,n,void 0,r&&r[n])));else{const n=Object.keys(e);l=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];l[o]=t(e[a],a,o,r&&r[o])}}else l=[];return n&&(n[o]=l),l}function ro(e,t,n={},o,l){if(nn.ce||nn.parent&&In(nn.parent)&&nn.parent.ce)return"default"!==t&&(n.name=t),Sl(),Ol(bl,null,[Ml("slot",n,o&&o())],64);let r=e[t];r&&r._c&&(r._d=!1),Sl();const a=r&&ao(r(n)),i=n.key||a&&a.key,s=Ol(bl,{key:(i&&!k(i)?i:`_${t}`)+(!a&&o?"_fb":"")},a||(o?o():[]),a&&1===e._?64:-2);return!l&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),r&&r._c&&(r._d=!0),s}function ao(e){return e.some((e=>!Pl(e)||e.type!==_l&&!(e.type===bl&&!ao(e.children))))?e:null}const io=e=>e?Xl(e)?lr(e):io(e.parent):null,so=p(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>io(e.parent),$root:e=>io(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>go(e),$forceUpdate:e=>e.f||(e.f=()=>{Gt(e.update)}),$nextTick:e=>e.n||(e.n=Qt.bind(e.proxy)),$watch:e=>tl.bind(e)}),uo=(e,t)=>e!==i&&!e.__isScriptSetup&&m(e,t),co={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:n,setupState:o,data:l,props:r,accessCache:a,type:s,appContext:u}=e;let c;if("$"!==t[0]){const s=a[t];if(void 0!==s)switch(s){case 1:return o[t];case 2:return l[t];case 4:return n[t];case 3:return r[t]}else{if(uo(o,t))return a[t]=1,o[t];if(l!==i&&m(l,t))return a[t]=2,l[t];if((c=e.propsOptions[0])&&m(c,t))return a[t]=3,r[t];if(n!==i&&m(n,t))return a[t]=4,n[t];po&&(a[t]=0)}}const d=so[t];let f,p;return d?("$attrs"===t&&Re(e.attrs,0,""),d(e)):(f=s.__cssModules)&&(f=f[t])?f:n!==i&&m(n,t)?(a[t]=4,n[t]):(p=u.config.globalProperties,m(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:l,ctx:r}=e;return uo(l,t)?(l[t]=n,!0):o!==i&&m(o,t)?(o[t]=n,!0):!m(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(r[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:l,propsOptions:r}},a){let s;return!!n[a]||e!==i&&m(e,a)||uo(t,a)||(s=r[0])&&m(s,a)||m(o,a)||m(so,a)||m(l.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:m(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function fo(e){return g(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let po=!0;function vo(e){const t=go(e),n=e.proxy,o=e.ctx;po=!1,t.beforeCreate&&ho(t.beforeCreate,e,"bc");const{data:l,computed:r,methods:a,watch:i,provide:s,inject:c,created:d,beforeMount:f,mounted:p,beforeUpdate:v,updated:h,activated:m,deactivated:b,beforeDestroy:y,beforeUnmount:w,destroyed:k,unmounted:S,render:C,renderTracked:E,renderTriggered:T,errorCaptured:L,serverPrefetch:q,expose:O,inheritAttrs:P,components:F,directives:R,filters:A}=t;if(c&&function(e,t,n=u){g(e)&&(e=wo(e));for(const o in e){const n=e[o];let l;l=x(n)?"default"in n?Oo(n.from||o,n.default,!0):Oo(n.from||o):Oo(n),St(l)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e}):t[o]=l}}(c,o,null),a)for(const u in a){const e=a[u];_(e)&&(o[u]=e.bind(n))}if(l){const t=l.call(n,n);x(t)&&(e.data=ft(t))}if(po=!0,r)for(const g in r){const e=r[g],t=_(e)?e.bind(n,n):_(e.get)?e.get.bind(n,n):u,l=!_(e)&&_(e.set)?e.set.bind(n):u,a=ar({get:t,set:l});Object.defineProperty(o,g,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(i)for(const u in i)mo(i[u],o,n,u);if(s){const e=_(s)?s.call(n):s;Reflect.ownKeys(e).forEach((t=>{qo(t,e[t])}))}function V(e,t){g(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&ho(d,e,"c"),V(Hn,f),V(Wn,p),V(Kn,v),V(Qn,h),V($n,m),V(Bn,b),V(eo,L),V(Xn,E),V(Zn,T),V(Gn,w),V(Yn,S),V(Jn,q),g(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===u&&(e.render=C),null!=P&&(e.inheritAttrs=P),F&&(e.components=F),R&&(e.directives=R),q&&Vn(e)}function ho(e,t,n){$t(g(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function mo(e,t,n,o){let l=o.includes(".")?nl(n,o):()=>n[o];if(w(e)){const n=t[e];_(n)&&Xo(l,n)}else if(_(e))Xo(l,e.bind(n));else if(x(e))if(g(e))e.forEach((e=>mo(e,t,n,o)));else{const o=_(e.handler)?e.handler.bind(n):t[e.handler];_(o)&&Xo(l,o,e)}}function go(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:l,optionsCache:r,config:{optionMergeStrategies:a}}=e.appContext,i=r.get(t);let s;return i?s=i:l.length||n||o?(s={},l.length&&l.forEach((e=>bo(s,e,a,!0))),bo(s,t,a)):s=t,x(t)&&r.set(t,s),s}function bo(e,t,n,o=!1){const{mixins:l,extends:r}=t;r&&bo(e,r,n,!0),l&&l.forEach((t=>bo(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=yo[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const yo={data:_o,props:So,emits:So,methods:xo,computed:xo,beforeCreate:ko,created:ko,beforeMount:ko,mounted:ko,beforeUpdate:ko,updated:ko,beforeDestroy:ko,beforeUnmount:ko,destroyed:ko,unmounted:ko,activated:ko,deactivated:ko,errorCaptured:ko,serverPrefetch:ko,components:xo,directives:xo,watch:function(e,t){if(!e)return t;if(!t)return e;const n=p(Object.create(null),e);for(const o in t)n[o]=ko(e[o],t[o]);return n},provide:_o,inject:function(e,t){return xo(wo(e),wo(t))}};function _o(e,t){return t?e?function(){return p(_(e)?e.call(this,this):e,_(t)?t.call(this,this):t)}:t:e}function wo(e){if(g(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ko(e,t){return e?[...new Set([].concat(e,t))]:t}function xo(e,t){return e?p(Object.create(null),e,t):t}function So(e,t){return e?g(e)&&g(t)?[...new Set([...e,...t])]:p(Object.create(null),fo(e),fo(null!=t?t:{})):t}function Co(){return{app:null,config:{isNativeTag:c,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Eo=0;function To(e,t){return function(n,o=null){_(n)||(n=p({},n)),null==o||x(o)||(o=null);const l=Co(),r=new WeakSet,a=[];let i=!1;const s=l.app={_uid:Eo++,_component:n,_props:o,_container:null,_context:l,_instance:null,version:sr,get config(){return l.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&_(e.install)?(r.add(e),e.install(s,...t)):_(e)&&(r.add(e),e(s,...t))),s),mixin:e=>(l.mixins.includes(e)||l.mixins.push(e),s),component:(e,t)=>t?(l.components[e]=t,s):l.components[e],directive:(e,t)=>t?(l.directives[e]=t,s):l.directives[e],mount(r,a,u){if(!i){const c=s._ceVNode||Ml(n,o);return c.appContext=l,!0===u?u="svg":!1===u&&(u=void 0),a&&t?t(c,r):e(c,r,u),i=!0,s._container=r,r.__vue_app__=s,lr(c.component)}},onUnmount(e){a.push(e)},unmount(){i&&($t(a,s._instance,16),e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(l.provides[e]=t,s),runWithContext(e){const t=Lo;Lo=s;try{return e()}finally{Lo=t}}};return s}}let Lo=null;function qo(e,t){if(Kl){let n=Kl.provides;const o=Kl.parent&&Kl.parent.provides;o===n&&(n=Kl.provides=Object.create(o)),n[e]=t}else;}function Oo(e,t,n=!1){const o=Kl||nn;if(o||Lo){const l=Lo?Lo._context.provides:o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(l&&e in l)return l[e];if(arguments.length>1)return n&&_(t)?t.call(o&&o.proxy):t}}const Po={},Fo=()=>Object.create(Po),Ro=e=>Object.getPrototypeOf(e)===Po;function Ao(e,t,n,o){const[l,r]=e.propsOptions;let a,s=!1;if(t)for(let i in t){if(q(i))continue;const u=t[i];let c;l&&m(l,c=F(i))?r&&r.includes(c)?(a||(a={}))[c]=u:n[c]=u:rl(e.emitsOptions,i)||i in o&&u===o[i]||(o[i]=u,s=!0)}if(r){const t=_t(n),o=a||i;for(let a=0;a<r.length;a++){const i=r[a];n[i]=Vo(l,t,i,o[i],e,!m(o,i))}}return s}function Vo(e,t,n,o,l,r){const a=e[n];if(null!=a){const e=m(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&!a.skipFactory&&_(e)){const{propsDefaults:r}=l;if(n in r)o=r[n];else{const a=Jl(l);o=r[n]=e.call(null,t),a()}}else o=e;l.ce&&l.ce._setProp(n,o)}a[0]&&(r&&!e?o=!1:!a[1]||""!==o&&o!==A(n)||(o=!0))}return o}const Mo=new WeakMap;function Io(e,t,n=!1){const o=n?Mo:t.propsCache,l=o.get(e);if(l)return l;const r=e.props,a={},u=[];let c=!1;if(!_(e)){const o=e=>{c=!0;const[n,o]=Io(e,t,!0);p(a,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!r&&!c)return x(e)&&o.set(e,s),s;if(g(r))for(let s=0;s<r.length;s++){const e=F(r[s]);No(e)&&(a[e]=i)}else if(r)for(const i in r){const e=F(i);if(No(e)){const t=r[i],n=a[e]=g(t)||_(t)?{type:t}:p({},t),o=n.type;let l=!1,s=!0;if(g(o))for(let e=0;e<o.length;++e){const t=o[e],n=_(t)&&t.name;if("Boolean"===n){l=!0;break}"String"===n&&(s=!1)}else l=_(o)&&"Boolean"===o.name;n[0]=l,n[1]=s,(l||m(n,"default"))&&u.push(e)}}const d=[a,u];return x(e)&&o.set(e,d),d}function No(e){return"$"!==e[0]&&!q(e)}const $o=e=>"_"===e[0]||"$stable"===e,Bo=e=>g(e)?e.map(Bl):[Bl(e)],zo=(e,t,n)=>{if(t._n)return t;const o=rn(((...e)=>Bo(t(...e))),n);return o._c=!1,o},Do=(e,t,n)=>{const o=e._ctx;for(const l in e){if($o(l))continue;const n=e[l];if(_(n))t[l]=zo(0,n,o);else if(null!=n){const e=Bo(n);t[l]=()=>e}}},jo=(e,t)=>{const n=Bo(t);e.slots.default=()=>n},Uo=(e,t,n)=>{for(const o in t)(n||"_"!==o)&&(e[o]=t[o])},Ho=function(e,t){t&&t.pendingBranch?g(e)?t.effects.push(...e):t.effects.push(e):Jt(e)};function Wo(e){return function(e,t){j().__VUE__=!0;const{insert:n,remove:o,patchProp:l,createElement:r,createText:a,createComment:c,setText:d,setElementText:f,parentNode:p,nextSibling:v,setScopeId:h=u,insertStaticContent:g}=e,b=(e,t,n,o=null,l=null,r=null,a,i=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!Fl(e,t)&&(o=X(e),Q(e,l,r,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);const{type:u,ref:c,shapeFlag:d}=t;switch(u){case yl:y(e,t,n,o);break;case _l:_(e,t,n,o);break;case wl:null==e&&w(t,n,o,a);break;case bl:V(e,t,n,o,l,r,a,i,s);break;default:1&d?C(e,t,n,o,l,r,a,i,s):6&d?M(e,t,n,o,l,r,a,i,s):(64&d||128&d)&&u.process(e,t,n,o,l,r,a,i,s,ne)}null!=c&&l&&Mn(c,e&&e.ref,r,t||e,!t)},y=(e,t,o,l)=>{if(null==e)n(t.el=a(t.children),o,l);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},_=(e,t,o,l)=>{null==e?n(t.el=c(t.children||""),o,l):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=g(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,l)=>{let r;for(;e&&e!==t;)r=v(e),n(e,o,l),e=r;n(t,o,l)},x=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),o(e),e=n;o(t)},C=(e,t,n,o,l,r,a,i,s)=>{"svg"===t.type?a="svg":"math"===t.type&&(a="mathml"),null==e?E(t,n,o,l,r,a,i,s):O(e,t,l,r,a,i,s)},E=(e,t,o,a,i,s,u,c)=>{let d,p;const{props:v,shapeFlag:h,transition:m,dirs:g}=e;if(d=e.el=r(e.type,s,v&&v.is,v),8&h?f(d,e.children):16&h&&L(e.children,d,null,a,i,Ko(e,s),u,c),g&&sn(e,null,a,"created"),T(d,e,e.scopeId,u,a),v){for(const e in v)"value"===e||q(e)||l(d,e,null,v[e],s,a);"value"in v&&l(d,"value",null,v.value,s),(p=v.onVnodeBeforeMount)&&Ul(p,a,e)}g&&sn(e,null,a,"beforeMount");const b=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,m);b&&m.beforeEnter(d),n(d,t,o),((p=v&&v.onVnodeMounted)||b||g)&&Ho((()=>{p&&Ul(p,a,e),b&&m.enter(d),g&&sn(e,null,a,"mounted")}),i)},T=(e,t,n,o,l)=>{if(n&&h(e,n),o)for(let r=0;r<o.length;r++)h(e,o[r]);if(l){let n=l.subTree;if(t===n||dl(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=l.vnode;T(e,t,t.scopeId,t.slotScopeIds,l.parent)}}},L=(e,t,n,o,l,r,a,i,s=0)=>{for(let u=s;u<e.length;u++){const s=e[u]=i?zl(e[u]):Bl(e[u]);b(null,s,t,n,o,l,r,a,i)}},O=(e,t,n,o,r,a,s)=>{const u=t.el=e.el;let{patchFlag:c,dynamicChildren:d,dirs:p}=t;c|=16&e.patchFlag;const v=e.props||i,h=t.props||i;let m;if(n&&Qo(n,!1),(m=h.onVnodeBeforeUpdate)&&Ul(m,n,t,e),p&&sn(t,e,n,"beforeUpdate"),n&&Qo(n,!0),(v.innerHTML&&null==h.innerHTML||v.textContent&&null==h.textContent)&&f(u,""),d?P(e.dynamicChildren,d,u,n,o,Ko(t,r),a):s||U(e,t,u,null,n,o,Ko(t,r),a,!1),c>0){if(16&c)R(u,v,h,n,r);else if(2&c&&v.class!==h.class&&l(u,"class",null,h.class,r),4&c&&l(u,"style",v.style,h.style,r),8&c){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const o=e[t],a=v[o],i=h[o];i===a&&"value"!==o||l(u,o,a,i,r,n)}}1&c&&e.children!==t.children&&f(u,t.children)}else s||null!=d||R(u,v,h,n,r);((m=h.onVnodeUpdated)||p)&&Ho((()=>{m&&Ul(m,n,t,e),p&&sn(t,e,n,"updated")}),o)},P=(e,t,n,o,l,r,a)=>{for(let i=0;i<t.length;i++){const s=e[i],u=t[i],c=s.el&&(s.type===bl||!Fl(s,u)||70&s.shapeFlag)?p(s.el):n;b(s,u,c,null,o,l,r,a,!0)}},R=(e,t,n,o,r)=>{if(t!==n){if(t!==i)for(const a in t)q(a)||a in n||l(e,a,t[a],null,r,o);for(const a in n){if(q(a))continue;const i=n[a],s=t[a];i!==s&&"value"!==a&&l(e,a,s,i,r,o)}"value"in n&&l(e,"value",t.value,n.value,r)}},V=(e,t,o,l,r,i,s,u,c)=>{const d=t.el=e?e.el:a(""),f=t.anchor=e?e.anchor:a("");let{patchFlag:p,dynamicChildren:v,slotScopeIds:h}=t;h&&(u=u?u.concat(h):h),null==e?(n(d,o,l),n(f,o,l),L(t.children||[],o,f,r,i,s,u,c)):p>0&&64&p&&v&&e.dynamicChildren?(P(e.dynamicChildren,v,o,r,i,s,u),(null!=t.key||r&&t===r.subTree)&&Go(e,t,!0)):U(e,t,o,f,r,i,s,u,c)},M=(e,t,n,o,l,r,a,i,s)=>{t.slotScopeIds=i,null==e?512&t.shapeFlag?l.ctx.activate(t,n,o,a,s):I(t,n,o,l,r,a,s):B(e,t,s)},I=(e,t,n,o,l,r,a)=>{const s=e.component=function(e,t,n){const o=e.type,l=(t?t.appContext:e.appContext)||Hl,r={uid:Wl++,vnode:e,type:o,parent:t,appContext:l,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new le(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(l.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Io(o,l),emitsOptions:ll(o,l),emit:null,emitted:null,propsDefaults:i,inheritAttrs:o.inheritAttrs,ctx:i,data:i,props:i,attrs:i,slots:i,refs:i,setupState:i,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};r.ctx={_:r},r.root=t?t.root:r,r.emit=ol.bind(null,r),e.ce&&e.ce(r);return r}(e,o,l);if(Nn(e)&&(s.ctx.renderer=ne),function(e,t=!1,n=!1){t&&Yl(t);const{props:o,children:l}=e.vnode,r=Xl(e);(function(e,t,n,o=!1){const l={},r=Fo();e.propsDefaults=Object.create(null),Ao(e,t,l,r);for(const a in e.propsOptions[0])a in l||(l[a]=void 0);n?e.props=o?l:pt(l):e.type.props?e.props=l:e.props=r,e.attrs=r})(e,o,r,t),((e,t,n)=>{const o=e.slots=Fo();if(32&e.vnode.shapeFlag){const e=t._;e?(Uo(o,t,n),n&&$(o,"_",e,!0)):Do(t,o)}else t&&jo(e,t)})(e,l,n);const a=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,co);const{setup:o}=n;if(o){ke();const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,or),slots:e.slots,emit:e.emit,expose:t}}(e):null,l=Jl(e),r=Nt(o,e,0,[e.props,n]),a=S(r);if(xe(),l(),!a&&!e.sp||In(e)||Vn(e),a){if(r.then(Zl,Zl),t)return r.then((n=>{tr(e,n,t)})).catch((t=>{Bt(t,e,0)}));e.asyncDep=r}else tr(e,r,t)}else nr(e,t)}(e,t):void 0;t&&Yl(!1)}(s,!1,a),s.asyncDep){if(l&&l.registerDep(s,z,a),!e.el){const e=s.subTree=Ml(_l);_(null,e,t,n)}}else z(s,e,t,n,l,r,a)},B=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:l,component:r}=e,{props:a,children:i,patchFlag:s}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return!(!l&&!i||i&&i.$stable)||o!==a&&(o?!a||ul(o,a,u):!!a);if(1024&s)return!0;if(16&s)return o?ul(o,a,u):!!a;if(8&s){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!rl(u,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void D(o,t,n);o.next=t,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,l,r,a)=>{const i=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:s,vnode:u}=e;{const n=Yo(e);if(n)return t&&(t.el=u.el,D(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||i()}))}let c,d=t;Qo(e,!1),t?(t.el=u.el,D(e,t,a)):t=u,n&&N(n),(c=t.props&&t.props.onVnodeBeforeUpdate)&&Ul(c,s,t,u),Qo(e,!0);const f=al(e),v=e.subTree;e.subTree=f,b(v,f,p(v.el),X(v),e,l,r),t.el=f.el,null===d&&cl(e,f.el),o&&Ho(o,l),(c=t.props&&t.props.onVnodeUpdated)&&Ho((()=>Ul(c,s,t,u)),l)}else{let a;const{el:i,props:s}=t,{bm:u,m:c,parent:d,root:f,type:p}=e,v=In(t);if(Qo(e,!1),u&&N(u),!v&&(a=s&&s.onVnodeBeforeMount)&&Ul(a,d,t),Qo(e,!0),i&&re){const t=()=>{e.subTree=al(e),re(i,e.subTree,e,l,null)};v&&p.__asyncHydrate?p.__asyncHydrate(i,e,t):t()}else{f.ce&&f.ce._injectChildStyle(p);const a=e.subTree=al(e);b(null,a,n,o,e,l,r),t.el=a.el}if(c&&Ho(c,l),!v&&(a=s&&s.onVnodeMounted)){const e=t;Ho((()=>Ul(a,d,e)),l)}(256&t.shapeFlag||d&&In(d.vnode)&&256&d.vnode.shapeFlag)&&e.a&&Ho(e.a,l),e.isMounted=!0,t=n=o=null}};e.scope.on();const s=e.effect=new ie(i);e.scope.off();const u=e.update=s.run.bind(s),c=e.job=s.runIfDirty.bind(s);c.i=e,c.id=e.uid,s.scheduler=()=>Gt(c),Qo(e,!0),u()},D=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:l,attrs:r,vnode:{patchFlag:a}}=e,i=_t(l),[s]=e.propsOptions;let u=!1;if(!(o||a>0)||16&a){let o;Ao(e,t,l,r)&&(u=!0);for(const r in i)t&&(m(t,r)||(o=A(r))!==r&&m(t,o))||(s?!n||void 0===n[r]&&void 0===n[o]||(l[r]=Vo(s,i,r,void 0,e,!0)):delete l[r]);if(r!==i)for(const e in r)t&&m(t,e)||(delete r[e],u=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(rl(e.emitsOptions,a))continue;const c=t[a];if(s)if(m(r,a))c!==r[a]&&(r[a]=c,u=!0);else{const t=F(a);l[t]=Vo(s,i,t,c,e,!1)}else c!==r[a]&&(r[a]=c,u=!0)}}u&&Ae(e.attrs,"set","")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:l}=e;let r=!0,a=i;if(32&o.shapeFlag){const e=t._;e?n&&1===e?r=!1:Uo(l,t,n):(r=!t.$stable,Do(t,l)),a=t}else t&&(jo(e,t),a={default:1});if(r)for(const i in l)$o(i)||null!=a[i]||delete l[i]})(e,t.children,n),ke(),Zt(e),xe()},U=(e,t,n,o,l,r,a,i,s=!1)=>{const u=e&&e.children,c=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:v}=t;if(p>0){if(128&p)return void W(u,d,n,o,l,r,a,i,s);if(256&p)return void H(u,d,n,o,l,r,a,i,s)}8&v?(16&c&&Z(u,l,r),d!==u&&f(n,d)):16&c?16&v?W(u,d,n,o,l,r,a,i,s):Z(u,l,r,!0):(8&c&&f(n,""),16&v&&L(d,n,o,l,r,a,i,s))},H=(e,t,n,o,l,r,a,i,u)=>{t=t||s;const c=(e=e||s).length,d=t.length,f=Math.min(c,d);let p;for(p=0;p<f;p++){const o=t[p]=u?zl(t[p]):Bl(t[p]);b(e[p],o,n,null,l,r,a,i,u)}c>d?Z(e,l,r,!0,!1,f):L(t,n,o,l,r,a,i,u,f)},W=(e,t,n,o,l,r,a,i,u)=>{let c=0;const d=t.length;let f=e.length-1,p=d-1;for(;c<=f&&c<=p;){const o=e[c],s=t[c]=u?zl(t[c]):Bl(t[c]);if(!Fl(o,s))break;b(o,s,n,null,l,r,a,i,u),c++}for(;c<=f&&c<=p;){const o=e[f],s=t[p]=u?zl(t[p]):Bl(t[p]);if(!Fl(o,s))break;b(o,s,n,null,l,r,a,i,u),f--,p--}if(c>f){if(c<=p){const e=p+1,s=e<d?t[e].el:o;for(;c<=p;)b(null,t[c]=u?zl(t[c]):Bl(t[c]),n,s,l,r,a,i,u),c++}}else if(c>p)for(;c<=f;)Q(e[c],l,r,!0),c++;else{const v=c,h=c,m=new Map;for(c=h;c<=p;c++){const e=t[c]=u?zl(t[c]):Bl(t[c]);null!=e.key&&m.set(e.key,c)}let g,y=0;const _=p-h+1;let w=!1,k=0;const x=new Array(_);for(c=0;c<_;c++)x[c]=0;for(c=v;c<=f;c++){const o=e[c];if(y>=_){Q(o,l,r,!0);continue}let s;if(null!=o.key)s=m.get(o.key);else for(g=h;g<=p;g++)if(0===x[g-h]&&Fl(o,t[g])){s=g;break}void 0===s?Q(o,l,r,!0):(x[s-h]=c+1,s>=k?k=s:w=!0,b(o,t[s],n,null,l,r,a,i,u),y++)}const S=w?function(e){const t=e.slice(),n=[0];let o,l,r,a,i;const s=e.length;for(o=0;o<s;o++){const s=e[o];if(0!==s){if(l=n[n.length-1],e[l]<s){t[o]=l,n.push(o);continue}for(r=0,a=n.length-1;r<a;)i=r+a>>1,e[n[i]]<s?r=i+1:a=i;s<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}r=n.length,a=n[r-1];for(;r-- >0;)n[r]=a,a=t[a];return n}(x):s;for(g=S.length-1,c=_-1;c>=0;c--){const e=h+c,s=t[e],f=e+1<d?t[e+1].el:o;0===x[c]?b(null,s,n,f,l,r,a,i,u):w&&(g<0||c!==S[g]?K(s,n,f,2):g--)}}},K=(e,t,o,l,r=null)=>{const{el:a,type:i,transition:s,children:u,shapeFlag:c}=e;if(6&c)return void K(e.component.subTree,t,o,l);if(128&c)return void e.suspense.move(t,o,l);if(64&c)return void i.move(e,t,o,ne);if(i===bl){n(a,t,o);for(let e=0;e<u.length;e++)K(u[e],t,o,l);return void n(e.anchor,t,o)}if(i===wl)return void k(e,t,o);if(2!==l&&1&c&&s)if(0===l)s.beforeEnter(a),n(a,t,o),Ho((()=>s.enter(a)),r);else{const{leave:e,delayLeave:l,afterLeave:r}=s,i=()=>n(a,t,o),u=()=>{e(a,(()=>{i(),r&&r()}))};l?l(a,i,u):u()}else n(a,t,o)},Q=(e,t,n,o=!1,l=!1)=>{const{type:r,props:a,ref:i,children:s,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:f,cacheIndex:p}=e;if(-2===d&&(l=!1),null!=i&&Mn(i,null,n,e,!0),null!=p&&(t.renderCache[p]=void 0),256&c)return void t.ctx.deactivate(e);const v=1&c&&f,h=!In(e);let m;if(h&&(m=a&&a.onVnodeBeforeUnmount)&&Ul(m,t,e),6&c)J(e.component,n,o);else{if(128&c)return void e.suspense.unmount(n,o);v&&sn(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,ne,o):u&&!u.hasOnce&&(r!==bl||d>0&&64&d)?Z(u,t,n,!1,!0):(r===bl&&384&d||!l&&16&c)&&Z(s,t,n),o&&G(e)}(h&&(m=a&&a.onVnodeUnmounted)||v)&&Ho((()=>{m&&Ul(m,t,e),v&&sn(e,null,t,"unmounted")}),n)},G=e=>{const{type:t,el:n,anchor:l,transition:r}=e;if(t===bl)return void Y(n,l);if(t===wl)return void x(e);const a=()=>{o(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,l=()=>t(n,a);o?o(e.el,a,l):l()}else a()},Y=(e,t)=>{let n;for(;e!==t;)n=v(e),o(e),e=n;o(t)},J=(e,t,n)=>{const{bum:o,scope:l,job:r,subTree:a,um:i,m:s,a:u}=e;Jo(s),Jo(u),o&&N(o),l.stop(),r&&(r.flags|=8,Q(a,e,t,n)),i&&Ho(i,t),Ho((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Z=(e,t,n,o=!1,l=!1,r=0)=>{for(let a=r;a<e.length;a++)Q(e[a],t,n,o,l)},X=e=>{if(6&e.shapeFlag)return X(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=v(e.anchor||e.el),n=t&&t[un];return n?v(n):t};let ee=!1;const te=(e,t,n)=>{null==e?t._vnode&&Q(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),t._vnode=e,ee||(ee=!0,Zt(),Xt(),ee=!1)},ne={p:b,um:Q,m:K,r:G,mt:I,mc:L,pc:U,pbc:P,n:X,o:e};let oe,re;t&&([oe,re]=t(ne));return{render:te,hydrate:oe,createApp:To(te,oe)}}(e)}function Ko({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Qo({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Go(e,t,n=!1){const o=e.children,l=t.children;if(g(o)&&g(l))for(let r=0;r<o.length;r++){const e=o[r];let t=l[r];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=l[r]=zl(l[r]),t.el=e.el),n||-2===t.patchFlag||Go(e,t)),t.type===yl&&(t.el=e.el)}}function Yo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Yo(t)}function Jo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Zo=Symbol.for("v-scx");function Xo(e,t,n){return el(e,t,n)}function el(e,t,n=i){const{immediate:o,deep:l,flush:r,once:a}=n,s=p({},n),c=t&&o||!t&&"post"!==r;let d;if(er)if("sync"===r){const e=Oo(Zo);d=e.__watcherHandles||(e.__watcherHandles=[])}else if(!c){const e=()=>{};return e.stop=u,e.resume=u,e.pause=u,e}const f=Kl;s.call=(e,t,n)=>$t(e,f,t,n);let v=!1;"post"===r?s.scheduler=e=>{Ho(e,f&&f.suspense)}:"sync"!==r&&(v=!0,s.scheduler=(e,t)=>{t?e():Gt(e)}),s.augmentJob=e=>{t&&(e.flags|=4),v&&(e.flags|=2,f&&(e.id=f.uid,e.i=f))};const h=Mt(e,t,s);return er&&(d?d.push(h):c&&h()),h}function tl(e,t,n){const o=this.proxy,l=w(e)?e.includes(".")?nl(o,e):()=>o[e]:e.bind(o,o);let r;_(t)?r=t:(r=t.handler,n=t);const a=Jl(this),i=el(l,r.bind(o),n);return a(),i}function nl(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function ol(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i;let l=n;const r=t.startsWith("update:"),a=r&&((e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${F(t)}Modifiers`]||e[`${A(t)}Modifiers`])(o,t.slice(7));let s;a&&(a.trim&&(l=n.map((e=>w(e)?e.trim():e))),a.number&&(l=n.map(B)));let u=o[s=M(t)]||o[s=M(F(t))];!u&&r&&(u=o[s=M(A(t))]),u&&$t(u,e,6,l);const c=o[s+"Once"];if(c){if(e.emitted){if(e.emitted[s])return}else e.emitted={};e.emitted[s]=!0,$t(c,e,6,l)}}function ll(e,t,n=!1){const o=t.emitsCache,l=o.get(e);if(void 0!==l)return l;const r=e.emits;let a={},i=!1;if(!_(e)){const o=e=>{const n=ll(e,t,!0);n&&(i=!0,p(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return r||i?(g(r)?r.forEach((e=>a[e]=null)):p(a,r),x(e)&&o.set(e,a),a):(x(e)&&o.set(e,null),null)}function rl(e,t){return!(!e||!d(t))&&(t=t.slice(2).replace(/Once$/,""),m(e,t[0].toLowerCase()+t.slice(1))||m(e,A(t))||m(e,t))}function al(e){const{type:t,vnode:n,proxy:o,withProxy:l,propsOptions:[r],slots:a,attrs:i,emit:s,render:u,renderCache:c,props:d,data:p,setupState:v,ctx:h,inheritAttrs:m}=e,g=ln(e);let b,y;try{if(4&n.shapeFlag){const e=l||o,t=e;b=Bl(u.call(t,e,c,d,v,p,h)),y=i}else{const e=t;0,b=Bl(e.length>1?e(d,{attrs:i,slots:a,emit:s}):e(d,null)),y=t.props?i:il(i)}}catch(w){kl.length=0,Bt(w,e,1),b=Ml(_l)}let _=b;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(r&&e.some(f)&&(y=sl(y,r)),_=Il(_,y,!1,!0))}return n.dirs&&(_=Il(_,null,!1,!0),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&Fn(_,n.transition),b=_,ln(g),b}const il=e=>{let t;for(const n in e)("class"===n||"style"===n||d(n))&&((t||(t={}))[n]=e[n]);return t},sl=(e,t)=>{const n={};for(const o in e)f(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function ul(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let l=0;l<o.length;l++){const r=o[l];if(t[r]!==e[r]&&!rl(n,r))return!0}return!1}function cl({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}const dl=e=>e.__isSuspense;let fl=0;const pl={name:"Suspense",__isSuspense:!0,process(e,t,n,o,l,r,a,i,s,u){if(null==e)!function(e,t,n,o,l,r,a,i,s){const{p:u,o:{createElement:c}}=s,d=c("div"),f=e.suspense=hl(e,l,o,t,d,n,r,a,i,s);u(null,f.pendingBranch=e.ssContent,d,null,o,f,r,a),f.deps>0?(vl(e,"onPending"),vl(e,"onFallback"),u(null,e.ssFallback,t,n,o,null,r,a),gl(f,e.ssFallback)):f.resolve(!1,!0)}(t,n,o,l,r,a,i,s,u);else{if(r&&r.deps>0&&!e.suspense.isInFallback)return t.suspense=e.suspense,t.suspense.vnode=t,void(t.el=e.el);!function(e,t,n,o,l,r,a,i,{p:s,um:u,o:{createElement:c}}){const d=t.suspense=e.suspense;d.vnode=t,t.el=e.el;const f=t.ssContent,p=t.ssFallback,{activeBranch:v,pendingBranch:h,isInFallback:m,isHydrating:g}=d;if(h)d.pendingBranch=f,Fl(f,h)?(s(h,f,d.hiddenContainer,null,l,d,r,a,i),d.deps<=0?d.resolve():m&&(g||(s(v,p,n,o,l,null,r,a,i),gl(d,p)))):(d.pendingId=fl++,g?(d.isHydrating=!1,d.activeBranch=h):u(h,l,d),d.deps=0,d.effects.length=0,d.hiddenContainer=c("div"),m?(s(null,f,d.hiddenContainer,null,l,d,r,a,i),d.deps<=0?d.resolve():(s(v,p,n,o,l,null,r,a,i),gl(d,p))):v&&Fl(f,v)?(s(v,f,n,o,l,d,r,a,i),d.resolve(!0)):(s(null,f,d.hiddenContainer,null,l,d,r,a,i),d.deps<=0&&d.resolve()));else if(v&&Fl(f,v))s(v,f,n,o,l,d,r,a,i),gl(d,f);else if(vl(t,"onPending"),d.pendingBranch=f,512&f.shapeFlag?d.pendingId=f.component.suspenseId:d.pendingId=fl++,s(null,f,d.hiddenContainer,null,l,d,r,a,i),d.deps<=0)d.resolve();else{const{timeout:e,pendingId:t}=d;e>0?setTimeout((()=>{d.pendingId===t&&d.fallback(p)}),e):0===e&&d.fallback(p)}}(e,t,n,o,l,a,i,s,u)}},hydrate:function(e,t,n,o,l,r,a,i,s){const u=t.suspense=hl(t,o,n,e.parentNode,document.createElement("div"),null,l,r,a,i,!0),c=s(e,u.pendingBranch=t.ssContent,n,u,r,a);0===u.deps&&u.resolve(!1,!0);return c},normalize:function(e){const{shapeFlag:t,children:n}=e,o=32&t;e.ssContent=ml(o?n.default:n),e.ssFallback=o?ml(n.fallback):Ml(_l)}};function vl(e,t){const n=e.props&&e.props[t];_(n)&&n()}function hl(e,t,n,o,l,r,a,i,s,u,c=!1){const{p:d,m:f,um:p,n:v,o:{parentNode:h,remove:m}}=u;let g;const b=function(e){const t=e.props&&e.props.suspensible;return null!=t&&!1!==t}(e);b&&t&&t.pendingBranch&&(g=t.pendingId,t.deps++);const y=e.props?z(e.props.timeout):void 0,_=r,w={vnode:e,parent:t,parentComponent:n,namespace:a,container:o,hiddenContainer:l,deps:0,pendingId:fl++,timeout:"number"==typeof y?y:-1,activeBranch:null,pendingBranch:null,isInFallback:!c,isHydrating:c,isUnmounted:!1,effects:[],resolve(e=!1,n=!1){const{vnode:o,activeBranch:l,pendingBranch:a,pendingId:i,effects:s,parentComponent:u,container:c}=w;let d=!1;w.isHydrating?w.isHydrating=!1:e||(d=l&&a.transition&&"out-in"===a.transition.mode,d&&(l.transition.afterLeave=()=>{i===w.pendingId&&(f(a,c,r===_?v(l):r,0),Jt(s))}),l&&(h(l.el)===c&&(r=v(l)),p(l,u,w,!0)),d||f(a,c,r,0)),gl(w,a),w.pendingBranch=null,w.isInFallback=!1;let m=w.parent,y=!1;for(;m;){if(m.pendingBranch){m.effects.push(...s),y=!0;break}m=m.parent}y||d||Jt(s),w.effects=[],b&&t&&t.pendingBranch&&g===t.pendingId&&(t.deps--,0!==t.deps||n||t.resolve()),vl(o,"onResolve")},fallback(e){if(!w.pendingBranch)return;const{vnode:t,activeBranch:n,parentComponent:o,container:l,namespace:r}=w;vl(t,"onFallback");const a=v(n),u=()=>{w.isInFallback&&(d(null,e,l,a,o,null,r,i,s),gl(w,e))},c=e.transition&&"out-in"===e.transition.mode;c&&(n.transition.afterLeave=u),w.isInFallback=!0,p(n,o,null,!0),c||u()},move(e,t,n){w.activeBranch&&f(w.activeBranch,e,t,n),w.container=e},next:()=>w.activeBranch&&v(w.activeBranch),registerDep(e,t,n){const o=!!w.pendingBranch;o&&w.deps++;const l=e.vnode.el;e.asyncDep.catch((t=>{Bt(t,e,0)})).then((r=>{if(e.isUnmounted||w.isUnmounted||w.pendingId!==e.suspenseId)return;e.asyncResolved=!0;const{vnode:i}=e;tr(e,r,!1),l&&(i.el=l);const s=!l&&e.subTree.el;t(e,i,h(l||e.subTree.el),l?null:v(e.subTree),w,a,n),s&&m(s),cl(e,i.el),o&&0==--w.deps&&w.resolve()}))},unmount(e,t){w.isUnmounted=!0,w.activeBranch&&p(w.activeBranch,n,e,t),w.pendingBranch&&p(w.pendingBranch,n,e,t)}};return w}function ml(e){let t;if(_(e)){const n=El&&e._c;n&&(e._d=!1,Sl()),e=e(),n&&(e._d=!0,t=xl,Cl())}if(g(e)){const t=function(e,t=!0){let n;for(let o=0;o<e.length;o++){const t=e[o];if(!Pl(t))return;if(t.type!==_l||"v-if"===t.children){if(n)return;n=t}}return n}(e);e=t}return e=Bl(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter((t=>t!==e))),e}function gl(e,t){e.activeBranch=t;const{vnode:n,parentComponent:o}=e;let l=t.el;for(;!l&&t.component;)l=(t=t.component.subTree).el;n.el=l,o&&o.subTree===n&&(o.vnode.el=l,cl(o,l))}const bl=Symbol.for("v-fgt"),yl=Symbol.for("v-txt"),_l=Symbol.for("v-cmt"),wl=Symbol.for("v-stc"),kl=[];let xl=null;function Sl(e=!1){kl.push(xl=e?null:[])}function Cl(){kl.pop(),xl=kl[kl.length-1]||null}let El=1;function Tl(e,t=!1){El+=e,e<0&&xl&&t&&(xl.hasOnce=!0)}function Ll(e){return e.dynamicChildren=El>0?xl||s:null,Cl(),El>0&&xl&&xl.push(e),e}function ql(e,t,n,o,l,r){return Ll(Vl(e,t,n,o,l,r,!0))}function Ol(e,t,n,o,l){return Ll(Ml(e,t,n,o,l,!0))}function Pl(e){return!!e&&!0===e.__v_isVNode}function Fl(e,t){return e.type===t.type&&e.key===t.key}const Rl=({key:e})=>null!=e?e:null,Al=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?w(e)||St(e)||_(e)?{i:nn,r:e,k:t,f:!!n}:e:null);function Vl(e,t=null,n=null,o=0,l=null,r=(e===bl?0:1),a=!1,i=!1){const s={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Rl(t),ref:t&&Al(t),scopeId:on,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:l,dynamicChildren:null,appContext:null,ctx:nn};return i?(Dl(s,n),128&r&&e.normalize(s)):n&&(s.shapeFlag|=w(n)?8:16),El>0&&!a&&xl&&(s.patchFlag>0||6&r)&&32!==s.patchFlag&&xl.push(s),s}const Ml=function(e,t=null,n=null,o=0,l=null,r=!1){e&&e!==no||(e=_l);if(Pl(e)){const o=Il(e,t,!0);return n&&Dl(o,n),El>0&&!r&&xl&&(6&o.shapeFlag?xl[xl.indexOf(e)]=o:xl.push(o)),o.patchFlag=-2,o}a=e,_(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?yt(e)||Ro(e)?p({},e):e:null}(t);let{class:e,style:n}=t;e&&!w(e)&&(t.class=G(e)),x(n)&&(yt(n)&&!g(n)&&(n=p({},n)),t.style=U(n))}const i=w(e)?1:dl(e)?128:cn(e)?64:x(e)?4:_(e)?2:0;return Vl(e,t,n,o,l,i,r,!0)};function Il(e,t,n=!1,o=!1){const{props:l,ref:r,patchFlag:a,children:i,transition:s}=e,u=t?jl(l||{},t):l,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Rl(u),ref:t&&t.ref?n&&r?g(r)?r.concat(Al(t)):[r,Al(t)]:Al(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==bl?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:s,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Il(e.ssContent),ssFallback:e.ssFallback&&Il(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return s&&o&&Fn(c,s.clone(c)),c}function Nl(e=" ",t=0){return Ml(yl,null,e,t)}function $l(e="",t=!1){return t?(Sl(),Ol(_l,null,e)):Ml(_l,null,e)}function Bl(e){return null==e||"boolean"==typeof e?Ml(_l):g(e)?Ml(bl,null,e.slice()):Pl(e)?zl(e):Ml(yl,null,String(e))}function zl(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Il(e)}function Dl(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(g(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Dl(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Ro(t)?3===o&&nn&&(1===nn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=nn}}else _(t)?(t={default:t,_ctx:nn},n=32):(t=String(t),64&o?(n=16,t=[Nl(t)]):n=8);e.children=t,e.shapeFlag|=n}function jl(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=G([t.class,o.class]));else if("style"===e)t.style=U([t.style,o.style]);else if(d(e)){const n=t[e],l=o[e];!l||n===l||g(n)&&n.includes(l)||(t[e]=n?[].concat(n,l):l)}else""!==e&&(t[e]=o[e])}return t}function Ul(e,t,n,o=null){$t(e,t,7,[n,o])}const Hl=Co();let Wl=0;let Kl=null;const Ql=()=>Kl||nn;let Gl,Yl;{const e=j(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};Gl=t("__VUE_INSTANCE_SETTERS__",(e=>Kl=e)),Yl=t("__VUE_SSR_SETTERS__",(e=>er=e))}const Jl=e=>{const t=Kl;return Gl(e),e.scope.on(),()=>{e.scope.off(),Gl(t)}},Zl=()=>{Kl&&Kl.scope.off(),Gl(null)};function Xl(e){return 4&e.vnode.shapeFlag}let er=!1;function tr(e,t,n){_(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:x(t)&&(e.setupState=Pt(t)),nr(e,n)}function nr(e,t,n){const o=e.type;e.render||(e.render=o.render||u);{const t=Jl(e);ke();try{vo(e)}finally{xe(),t()}}}const or={get:(e,t)=>(Re(e,0,""),e[t])};function lr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Pt(wt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in so?so[n](e):void 0,has:(e,t)=>t in e||t in so})):e.proxy}function rr(e,t=!0){return _(e)?e.displayName||e.name:e.name||t&&e.__name}const ar=(e,t)=>{const n=function(e,t,n=!1){let o,l;return _(e)?o=e:(o=e.get,l=e.set),new Ft(o,l,n)}(e,0,er);return n};function ir(e,t,n){const o=arguments.length;return 2===o?x(t)&&!g(t)?Pl(t)?Ml(e,null,[t]):Ml(e,t):Ml(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Pl(n)&&(n=[n]),Ml(e,t,n))}const sr="3.5.13";
/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let ur;const cr="undefined"!=typeof window&&window.trustedTypes;if(cr)try{ur=cr.createPolicy("vue",{createHTML:e=>e})}catch(hy){}const dr=ur?e=>ur.createHTML(e):e=>e,fr="undefined"!=typeof document?document:null,pr=fr&&fr.createElement("template"),vr={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const l="svg"===t?fr.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?fr.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?fr.createElement(e,{is:n}):fr.createElement(e);return"select"===e&&o&&null!=o.multiple&&l.setAttribute("multiple",o.multiple),l},createText:e=>fr.createTextNode(e),createComment:e=>fr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>fr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,l,r){const a=n?n.previousSibling:t.lastChild;if(l&&(l===r||l.nextSibling))for(;t.insertBefore(l.cloneNode(!0),n),l!==r&&(l=l.nextSibling););else{pr.innerHTML=dr("svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e);const l=pr.content;if("svg"===o||"mathml"===o){const e=l.firstChild;for(;e.firstChild;)l.appendChild(e.firstChild);l.removeChild(e)}t.insertBefore(l,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},hr="transition",mr=Symbol("_vtc"),gr={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},br=p({},Sn,gr),yr=(e=>(e.displayName="Transition",e.props=br,e))(((e,{slots:t})=>ir(Tn,function(e){const t={};for(const p in e)p in gr||(t[p]=e[p]);if(!1===e.css)return t;const{name:n="v",type:o,duration:l,enterFromClass:r=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:i=`${n}-enter-to`,appearFromClass:s=r,appearActiveClass:u=a,appearToClass:c=i,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,h=function(e){if(null==e)return null;if(x(e))return[kr(e.enter),kr(e.leave)];{const t=kr(e);return[t,t]}}(l),m=h&&h[0],g=h&&h[1],{onBeforeEnter:b,onEnter:y,onEnterCancelled:_,onLeave:w,onLeaveCancelled:k,onBeforeAppear:S=b,onAppear:C=y,onAppearCancelled:E=_}=t,T=(e,t,n,o)=>{e._enterCancelled=o,Sr(e,t?c:i),Sr(e,t?u:a),n&&n()},L=(e,t)=>{e._isLeaving=!1,Sr(e,d),Sr(e,v),Sr(e,f),t&&t()},q=e=>(t,n)=>{const l=e?C:y,a=()=>T(t,e,n);_r(l,[t,a]),Cr((()=>{Sr(t,e?s:r),xr(t,e?c:i),wr(l)||Tr(t,o,m,a)}))};return p(t,{onBeforeEnter(e){_r(b,[e]),xr(e,r),xr(e,a)},onBeforeAppear(e){_r(S,[e]),xr(e,s),xr(e,u)},onEnter:q(!1),onAppear:q(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>L(e,t);xr(e,d),e._enterCancelled?(xr(e,f),Or()):(Or(),xr(e,f)),Cr((()=>{e._isLeaving&&(Sr(e,d),xr(e,v),wr(w)||Tr(e,o,g,n))})),_r(w,[e,n])},onEnterCancelled(e){T(e,!1,void 0,!0),_r(_,[e])},onAppearCancelled(e){T(e,!0,void 0,!0),_r(E,[e])},onLeaveCancelled(e){L(e),_r(k,[e])}})}(e),t))),_r=(e,t=[])=>{g(e)?e.forEach((e=>e(...t))):e&&e(...t)},wr=e=>!!e&&(g(e)?e.some((e=>e.length>1)):e.length>1);function kr(e){return z(e)}function xr(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[mr]||(e[mr]=new Set)).add(t)}function Sr(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[mr];n&&(n.delete(t),n.size||(e[mr]=void 0))}function Cr(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Er=0;function Tr(e,t,n,o){const l=e._endId=++Er,r=()=>{l===e._endId&&o()};if(null!=n)return setTimeout(r,n);const{type:a,timeout:i,propCount:s}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),l=o("transitionDelay"),r=o("transitionDuration"),a=Lr(l,r),i=o("animationDelay"),s=o("animationDuration"),u=Lr(i,s);let c=null,d=0,f=0;t===hr?a>0&&(c=hr,d=a,f=r.length):"animation"===t?u>0&&(c="animation",d=u,f=s.length):(d=Math.max(a,u),c=d>0?a>u?hr:"animation":null,f=c?c===hr?r.length:s.length:0);const p=c===hr&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:c,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!a)return o();const u=a+"end";let c=0;const d=()=>{e.removeEventListener(u,f),r()},f=t=>{t.target===e&&++c>=s&&d()};setTimeout((()=>{c<s&&d()}),i+1),e.addEventListener(u,f)}function Lr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>qr(t)+qr(e[n]))))}function qr(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function Or(){return document.body.offsetHeight}const Pr=Symbol("_vod"),Fr=Symbol("_vsh"),Rr={beforeMount(e,{value:t},{transition:n}){e[Pr]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ar(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ar(e,!0),o.enter(e)):o.leave(e,(()=>{Ar(e,!1)})):Ar(e,t))},beforeUnmount(e,{value:t}){Ar(e,t)}};function Ar(e,t){e.style.display=t?e[Pr]:"none",e[Fr]=!t}const Vr=Symbol(""),Mr=/(^|;)\s*display\s*:/;const Ir=/\s*!important$/;function Nr(e,t,n){if(g(n))n.forEach((n=>Nr(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Br[t];if(n)return n;let o=F(t);if("filter"!==o&&o in e)return Br[t]=o;o=V(o);for(let l=0;l<$r.length;l++){const n=$r[l]+o;if(n in e)return Br[t]=n}return t}(e,t);Ir.test(n)?e.setProperty(A(o),n.replace(Ir,""),"important"):e[o]=n}}const $r=["Webkit","Moz","ms"],Br={};const zr="http://www.w3.org/1999/xlink";function Dr(e,t,n,o,l,r=Y(t)){o&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(zr,t.slice(6,t.length)):e.setAttributeNS(zr,t,n):null==n||r&&!J(n)?e.removeAttribute(t):e.setAttribute(t,r?"":k(n)?String(n):n)}function jr(e,t,n,o,l){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?dr(n):n));const r=e.tagName;if("value"===t&&"PROGRESS"!==r&&!r.includes("-")){const o="OPTION"===r?e.getAttribute("value")||"":e.value,l=null==n?"checkbox"===e.type?"on":"":String(n);return o===l&&"_value"in e||(e.value=l),null==n&&e.removeAttribute(t),void(e._value=n)}let a=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=J(n):null==n&&"string"===o?(n="",a=!0):"number"===o&&(n=0,a=!0)}try{e[t]=n}catch(hy){}a&&e.removeAttribute(l||t)}const Ur=Symbol("_vei");function Hr(e,t,n,o,l=null){const r=e[Ur]||(e[Ur]={}),a=r[t];if(o&&a)a.value=o;else{const[n,i]=function(e){let t;if(Wr.test(e)){let n;for(t={};n=e.match(Wr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):A(e.slice(2)),t]}(t);if(o){const a=r[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();$t(function(e,t){if(g(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=(()=>Kr||(Qr.then((()=>Kr=0)),Kr=Date.now()))(),n}(o,l);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,a,i)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,i),r[t]=void 0)}}const Wr=/(?:Once|Passive|Capture)$/;let Kr=0;const Qr=Promise.resolve();const Gr=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const Yr=["ctrl","shift","alt","meta"],Jr={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Yr.some((n=>e[`${n}Key`]&&!t.includes(n)))},Zr=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=Jr[t[e]];if(o&&o(n,t))return}return e(n,...o)})},Xr=p({patchProp:(e,t,n,o,l,r)=>{const a="svg"===l;"class"===t?function(e,t,n){const o=e[mr];o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,a):"style"===t?function(e,t,n){const o=e.style,l=w(n);let r=!1;if(n&&!l){if(t)if(w(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Nr(o,t,"")}else for(const e in t)null==n[e]&&Nr(o,e,"");for(const e in n)"display"===e&&(r=!0),Nr(o,e,n[e])}else if(l){if(t!==n){const e=o[Vr];e&&(n+=";"+e),o.cssText=n,r=Mr.test(n)}}else t&&e.removeAttribute("style");Pr in e&&(e[Pr]=r?o.display:"",e[Fr]&&(o.display="none"))}(e,n,o):d(t)?f(t)||Hr(e,t,0,o,r):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&Gr(t)&&_(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(Gr(t)&&w(n))return!1;return t in e}(e,t,o,a))?(jr(e,t,o),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Dr(e,t,o,a,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&w(o)?("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),Dr(e,t,o,a)):jr(e,F(t),o,0,t)}},vr);let ea;function ta(){return ea||(ea=Wo(Xr))}let na,oa=0;const la=new Array(256);for(let gy=0;gy<256;gy++)la[gy]=(gy+256).toString(16).substring(1);const ra=(()=>{const e="undefined"!=typeof crypto?crypto:"undefined"!=typeof window?window.crypto||window.msCrypto:void 0;if(void 0!==e){if(void 0!==e.randomBytes)return e.randomBytes;if(void 0!==e.getRandomValues)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return e=>{const t=[];for(let n=e;n>0;n--)t.push(Math.floor(256*Math.random()));return t}})();function aa(){(void 0===na||oa+16>4096)&&(oa=0,na=ra(4096));const e=Array.prototype.slice.call(na,oa,oa+=16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,la[e[0]]+la[e[1]]+la[e[2]]+la[e[3]]+"-"+la[e[4]]+la[e[5]]+"-"+la[e[6]]+la[e[7]]+"-"+la[e[8]]+la[e[9]]+"-"+la[e[10]]+la[e[11]]+la[e[12]]+la[e[13]]+la[e[14]]+la[e[15]]}var ia,sa="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},ua={exports:{}},ca="object"==typeof Reflect?Reflect:null,da=ca&&"function"==typeof ca.apply?ca.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};ia=ca&&"function"==typeof ca.ownKeys?ca.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var fa=Number.isNaN||function(e){return e!=e};function pa(){pa.init.call(this)}ua.exports=pa,ua.exports.once=function(e,t){return new Promise((function(n,o){function l(n){e.removeListener(t,r),o(n)}function r(){"function"==typeof e.removeListener&&e.removeListener("error",l),n([].slice.call(arguments))}xa(e,t,r,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&xa(e,"error",t,n)}(e,l,{once:!0})}))},pa.EventEmitter=pa,pa.prototype._events=void 0,pa.prototype._eventsCount=0,pa.prototype._maxListeners=void 0;var va=10;function ha(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function ma(e){return void 0===e._maxListeners?pa.defaultMaxListeners:e._maxListeners}function ga(e,t,n,o){var l,r,a,i;if(ha(n),void 0===(r=e._events)?(r=e._events=Object.create(null),e._eventsCount=0):(void 0!==r.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),r=e._events),a=r[t]),void 0===a)a=r[t]=n,++e._eventsCount;else if("function"==typeof a?a=r[t]=o?[n,a]:[a,n]:o?a.unshift(n):a.push(n),(l=ma(e))>0&&a.length>l&&!a.warned){a.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=a.length,i=s,console&&console.warn&&console.warn(i)}return e}function ba(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function ya(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},l=ba.bind(o);return l.listener=n,o.wrapFn=l,l}function _a(e,t,n){var o=e._events;if(void 0===o)return[];var l=o[t];return void 0===l?[]:"function"==typeof l?n?[l.listener||l]:[l]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(l):ka(l,l.length)}function wa(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function ka(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}function xa(e,t,n,o){if("function"==typeof e.on)o.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function l(r){o.once&&e.removeEventListener(t,l),n(r)}))}}Object.defineProperty(pa,"defaultMaxListeners",{enumerable:!0,get:function(){return va},set:function(e){if("number"!=typeof e||e<0||fa(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");va=e}}),pa.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},pa.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||fa(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},pa.prototype.getMaxListeners=function(){return ma(this)},pa.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var o="error"===e,l=this._events;if(void 0!==l)o=o&&void 0===l.error;else if(!o)return!1;if(o){var r;if(t.length>0&&(r=t[0]),r instanceof Error)throw r;var a=new Error("Unhandled error."+(r?" ("+r.message+")":""));throw a.context=r,a}var i=l[e];if(void 0===i)return!1;if("function"==typeof i)da(i,this,t);else{var s=i.length,u=ka(i,s);for(n=0;n<s;++n)da(u[n],this,t)}return!0},pa.prototype.addListener=function(e,t){return ga(this,e,t,!1)},pa.prototype.on=pa.prototype.addListener,pa.prototype.prependListener=function(e,t){return ga(this,e,t,!0)},pa.prototype.once=function(e,t){return ha(t),this.on(e,ya(this,e,t)),this},pa.prototype.prependOnceListener=function(e,t){return ha(t),this.prependListener(e,ya(this,e,t)),this},pa.prototype.removeListener=function(e,t){var n,o,l,r,a;if(ha(t),void 0===(o=this._events))return this;if(void 0===(n=o[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete o[e],o.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(l=-1,r=n.length-1;r>=0;r--)if(n[r]===t||n[r].listener===t){a=n[r].listener,l=r;break}if(l<0)return this;0===l?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,l),1===n.length&&(o[e]=n[0]),void 0!==o.removeListener&&this.emit("removeListener",e,a||t)}return this},pa.prototype.off=pa.prototype.removeListener,pa.prototype.removeAllListeners=function(e){var t,n,o;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var l,r=Object.keys(n);for(o=0;o<r.length;++o)"removeListener"!==(l=r[o])&&this.removeAllListeners(l);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},pa.prototype.listeners=function(e){return _a(this,e,!0)},pa.prototype.rawListeners=function(e){return _a(this,e,!1)},pa.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):wa.call(e,t)},pa.prototype.listenerCount=wa,pa.prototype.eventNames=function(){return this._eventsCount>0?ia(this._events):[]};const Sa={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce(((t,n)=>Ca(n)+Ca(e[n])+t),0):0},Ca=e=>Sa[typeof e](e);class Ea extends ua.exports.EventEmitter{constructor(e){super(),this.setMaxListeners(1/0),this.wall=e,e.listen((e=>{Array.isArray(e)?e.forEach((e=>this._emit(e))):this._emit(e)})),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=33554432}send(e,t){return this._send([{event:e,payload:t}])}getEvents(){return this._events}on(e,t){return super.on(e,(e=>{t({...e,respond:t=>this.send(e.eventResponseKey,t)})}))}_emit(e){"string"==typeof e?this.emit(e):this.emit(e.event,e.payload)}_send(e){return this._sendingQueue.push(e),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;const e=this._sendingQueue.shift(),t=e[0],n=`${t.event}.${aa()}`+".result";return new Promise(((o,l)=>{let r=[];const a=e=>{if(void 0!==e&&e._chunkSplit){const t=e._chunkSplit;r=[...r,...e.data],t.lastChunk&&(this.off(n,a),o(r))}else this.off(n,a),o(e)};this.on(n,a);try{const t=e.map((e=>({...e,payload:{data:e.payload,eventResponseKey:n}})));this.wall.send(t)}catch(i){const e="Message length exceeded maximum allowed length.";if(i.message===e)if(Array.isArray(t.payload)){const e=Ca(t);if(e>this._maxMessageSize){const n=Math.ceil(e/this._maxMessageSize),o=Math.ceil(t.payload.length/n);let l=t.payload;for(let e=0;e<n;e++){let r=Math.min(l.length,o);this.wall.send([{event:t.event,payload:{_chunkSplit:{count:n,lastChunk:e===n-1},data:l.splice(0,r)}}])}}}else;}this._sending=!1,setTimeout((()=>this._nextSend()),16)}))}}function Ta(e,t,n,o){return Object.defineProperty(e,t,{get:n,set:o,enumerable:!0}),e}const La=Ct(!1);let qa;const Oa="ontouchstart"in window||window.navigator.maxTouchPoints>0;const Pa=navigator.userAgent||navigator.vendor||window.opera,Fa={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Ra={userAgent:Pa,is:function(e){const t=e.toLowerCase(),n=function(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}(t),o=function(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}(t,n),l={mobile:!1,desktop:!1,cordova:!1,capacitor:!1,nativeMobile:!1,electron:!1,bex:!1,linux:!1,mac:!1,win:!1,cros:!1,chrome:!1,firefox:!1,opera:!1,safari:!1,vivaldi:!1,edge:!1,edgeChromium:!1,ie:!1,webkit:!1,android:!1,ios:!1,ipad:!1,iphone:!1,ipod:!1,kindle:!1,winphone:!1,blackberry:!1,playbook:!1,silk:!1};o.browser&&(l[o.browser]=!0,l.version=o.version,l.versionNumber=parseInt(o.version,10)),o.platform&&(l[o.platform]=!0);const r=l.android||l.ios||l.bb||l.blackberry||l.ipad||l.iphone||l.ipod||l.kindle||l.playbook||l.silk||l["windows phone"];if(!0===r||-1!==t.indexOf("mobile")?l.mobile=!0:l.desktop=!0,l["windows phone"]&&(l.winphone=!0,delete l["windows phone"]),l.edga||l.edgios||l.edg?(l.edge=!0,o.browser="edge"):l.crios?(l.chrome=!0,o.browser="chrome"):l.fxios&&(l.firefox=!0,o.browser="firefox"),(l.ipod||l.ipad||l.iphone)&&(l.ios=!0),l.vivaldi&&(o.browser="vivaldi",l.vivaldi=!0),(l.chrome||l.opr||l.safari||l.vivaldi||!0===l.mobile&&!0!==l.ios&&!0!==r)&&(l.webkit=!0),l.opr&&(o.browser="opera",l.opera=!0),l.safari&&(l.blackberry||l.bb?(o.browser="blackberry",l.blackberry=!0):l.playbook?(o.browser="playbook",l.playbook=!0):l.android?(o.browser="android",l.android=!0):l.kindle?(o.browser="kindle",l.kindle=!0):l.silk&&(o.browser="silk",l.silk=!0)),l.name=o.browser,l.platform=o.platform,-1!==t.indexOf("electron"))l.electron=!0;else if(-1!==document.location.href.indexOf("-extension://"))l.bex=!0;else{if(void 0!==window.Capacitor?(l.capacitor=!0,l.nativeMobile=!0,l.nativeMobileWrapper="capacitor"):void 0===window._cordovaNative&&void 0===window.cordova||(l.cordova=!0,l.nativeMobile=!0,l.nativeMobileWrapper="cordova"),!0===La.value&&(qa={is:{...l}}),!0===Oa&&!0===l.mac&&(!0===l.desktop&&!0===l.safari||!0===l.nativeMobile&&!0!==l.android&&!0!==l.ios&&!0!==l.ipad)){delete l.mac,delete l.desktop;const e=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(l,{mobile:!0,ios:!0,platform:e,[e]:!0})}!0!==l.mobile&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete l.desktop,l.mobile=!0)}return l}(Pa),has:{touch:Oa},within:{iframe:window.self!==window.top}},Aa={install(e){const{$q:t}=e;!0===La.value?(e.onSSRHydrated.push((()=>{Object.assign(t.platform,Ra),La.value=!1})),t.platform=ft(this)):t.platform=this}};{let e;Ta(Ra.has,"webStorage",(()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch(t){}return e=!1,!1})),Object.assign(Aa,Ra),!0===La.value&&(Object.assign(Aa,qa,Fa),qa=null)}function Va(e){return wt(An(e))}const Ma=(e,t)=>{const n=ft(e);for(const o in e)Ta(t,o,(()=>n[o]),(e=>{n[o]=e}));return t},Ia={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(Ia,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch(my){}function Na(){}function $a(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function Ba(e){e.stopPropagation()}function za(e){!1!==e.cancelable&&e.preventDefault()}function Da(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function ja(e,t,n){const o=`__q_${t}_evt`;e[o]=void 0!==e[o]?e[o].concat(n):n,n.forEach((t=>{t[0].addEventListener(t[1],e[t[2]],Ia[t[3]])}))}function Ua(e,t){const n=`__q_${t}_evt`;void 0!==e[n]&&(e[n].forEach((t=>{t[0].removeEventListener(t[1],e[t[2]],Ia[t[3]])})),e[n]=void 0)}function Ha(e,t=250,n){let o=null;function l(){const l=arguments,r=()=>{o=null,!0!==n&&e.apply(this,l)};null!==o?clearTimeout(o):!0===n&&e.apply(this,l),o=setTimeout(r,t)}return l.cancel=()=>{null!==o&&clearTimeout(o)},l}const Wa=["sm","md","lg","xl"],{passive:Ka}=Ia;var Qa=Ma({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:Na,setDebounce:Na,install({$q:e,onSSRHydrated:t}){var n;if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));const{visualViewport:o}=window,l=o||window,r=document.scrollingElement||document.documentElement,a=void 0===o||!0===Ra.is.mobile?()=>[Math.max(window.innerWidth,r.clientWidth),Math.max(window.innerHeight,r.clientHeight)]:()=>[o.width*o.scale+window.innerWidth-r.clientWidth,o.height*o.scale+window.innerHeight-r.clientHeight],i=!0===(null==(n=e.config.screen)?void 0:n.bodyClasses);this.__update=e=>{const[t,n]=a();if(n!==this.height&&(this.height=n),t!==this.width)this.width=t;else if(!0!==e)return;let o=this.sizes;this.gt.xs=t>=o.sm,this.gt.sm=t>=o.md,this.gt.md=t>=o.lg,this.gt.lg=t>=o.xl,this.lt.sm=t<o.sm,this.lt.md=t<o.md,this.lt.lg=t<o.lg,this.lt.xl=t<o.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,o=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",o!==this.name&&(!0===i&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${o}`)),this.name=o)};let s,u={},c=16;this.setSizes=e=>{Wa.forEach((t=>{void 0!==e[t]&&(u[t]=e[t])}))},this.setDebounce=e=>{c=e};const d=()=>{const e=getComputedStyle(document.body);e.getPropertyValue("--q-size-sm")&&Wa.forEach((t=>{this.sizes[t]=parseInt(e.getPropertyValue(`--q-size-${t}`),10)})),this.setSizes=e=>{Wa.forEach((t=>{e[t]&&(this.sizes[t]=e[t])})),this.__update(!0)},this.setDebounce=e=>{void 0!==s&&l.removeEventListener("resize",s,Ka),s=e>0?Ha(this.__update,e):this.__update,l.addEventListener("resize",s,Ka)},this.setDebounce(c),0!==Object.keys(u).length?(this.setSizes(u),u=void 0):this.__update(),!0===i&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===La.value?t.push(d):d()}});const Ga=Ma({isActive:!1,mode:!1},{__media:void 0,set(e){Ga.mode=e,"auto"===e?(void 0===Ga.__media&&(Ga.__media=window.matchMedia("(prefers-color-scheme: dark)"),Ga.__updateMedia=()=>{Ga.set("auto")},Ga.__media.addListener(Ga.__updateMedia)),e=Ga.__media.matches):void 0!==Ga.__media&&(Ga.__media.removeListener(Ga.__updateMedia),Ga.__media=void 0),Ga.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){Ga.set(!1===Ga.isActive)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,!0!==this.__installed&&this.set(void 0!==n&&n)}});function Ya(e,t,n=document.body){if("string"!=typeof e)throw new TypeError("Expected a string as propName");if("string"!=typeof t)throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let Ja=!1;function Za(e){Ja=!0===e.isComposing}function Xa(e){return!0===Ja||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function ei(e,t){return!0!==Xa(e)&&[].concat(t).includes(e.keyCode)}function ti(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}var ni={install(e){if(!0!==this.__installed){if(!0===La.value)!function(){const{is:e}=Ra,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(!0!==e.nativeMobile&&!0!==e.electron&&!0!==e.bex)if(!0===e.desktop)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(!0===e.mobile){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const t=ti(e);void 0!==t&&n.add(`platform-${t}`)}!0===Ra.has.touch&&(n.delete("no-touch"),n.add("touch")),!0===Ra.within.iframe&&n.add("within-iframe");const o=Array.from(n).join(" ");t!==o&&(document.body.className=o)}();else{const{$q:t}=e;void 0!==t.config.brand&&function(e){for(const t in e)Ya(t,e[t])}(t.config.brand);const n=function({is:e,has:t,within:n},o){const l=[!0===e.desktop?"desktop":"mobile",(!1===t.touch?"no-":"")+"touch"];if(!0===e.mobile){const t=ti(e);void 0!==t&&l.push("platform-"+t)}if(!0===e.nativeMobile){const t=e.nativeMobileWrapper;l.push(t),l.push("native-mobile"),!0!==e.ios||void 0!==o[t]&&!1===o[t].iosStatusBarPadding||l.push("q-ios-padding")}else!0===e.electron?l.push("electron"):!0===e.bex&&l.push("bex");return!0===n.iframe&&l.push("within-iframe"),l}(Ra,t.config);document.body.classList.add.apply(document.body.classList,n)}!0===Ra.is.ios&&document.body.addEventListener("touchstart",Na),window.addEventListener("keydown",Za,!0)}}};const oi=()=>!0;function li(e){return"string"==typeof e&&""!==e&&"/"!==e&&"#/"!==e}function ri(e){return!0===e.startsWith("#")&&(e=e.substring(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substring(0,e.length-1)),"#"+e}var ai={__history:[],add:Na,remove:Na,install({$q:e}){if(!0===this.__installed)return;const{cordova:t,capacitor:n}=Ra.is;if(!0!==t&&!0!==n)return;const o=e.config[!0===t?"cordova":"capacitor"];if(!1===(null==o?void 0:o.backButton))return;if(!0===n&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=oi),this.__history.push(e)},this.remove=e=>{const t=this.__history.indexOf(e);t>=0&&this.__history.splice(t,1)};const l=function(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return oi;const t=["#/"];return!0===Array.isArray(e.backButtonExit)&&t.push(...e.backButtonExit.filter(li).map(ri)),()=>t.includes(window.location.hash)}(Object.assign({backButtonExit:!0},o)),r=()=>{if(this.__history.length){const e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===l()?navigator.app.exitApp():window.history.back()};!0===t?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",r,!1)})):window.Capacitor.Plugins.App.addListener("backButton",r)}},ii={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days",prevMonth:"Previous month",nextMonth:"Next month",prevYear:"Previous year",nextYear:"Next year",today:"Today",prevRangeYears:e=>`Previous ${e} years`,nextRangeYears:e=>`Next ${e} years`},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},pagination:{first:"First page",prev:"Previous page",next:"Next page",last:"Last page"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function si(){const e=!0===Array.isArray(navigator.languages)&&0!==navigator.languages.length?navigator.languages[0]:navigator.language;if("string"==typeof e)return e.split(/[-_]/).map(((e,t)=>0===t?e.toLowerCase():t>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}const ui=Ma({__qLang:{}},{getLocale:si,set(e=ii,t){const n={...e,rtl:!0===e.rtl,getLocale:si};if(n.set=ui.set,void 0===ui.__langConfig||!0!==ui.__langConfig.noHtmlAttrs){const e=document.documentElement;e.setAttribute("dir",!0===n.rtl?"rtl":"ltr"),e.setAttribute("lang",n.isoName)}Object.assign(ui.__qLang,n)},install({$q:e,lang:t,ssrContext:n}){e.lang=ui.__qLang,ui.__langConfig=e.config.lang,!0===this.__installed?void 0!==t&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys:e=>Reflect.ownKeys(e).filter((e=>"set"!==e&&"getLocale"!==e))}),this.set(t||ii))}});var ci={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}};const di=Ma({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=di.set,Object.assign(di.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,Ta(e,"iconMapFn",(()=>this.iconMapFn),(e=>{this.iconMapFn=e})),!0===this.__installed?void 0!==t&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys:e=>Reflect.ownKeys(e).filter((e=>"set"!==e))}),this.set(t||ci))}});function fi(){}const pi={};let vi=!1;function hi(e,t){if(e===t)return!0;if(null!==e&&null!==t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;let n,o;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(o=n;0!=o--;)if(!0!==hi(e[o],t[o]))return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let n=e.entries();for(o=n.next();!0!==o.done;){if(!0!==t.has(o.value[0]))return!1;o=n.next()}for(n=e.entries(),o=n.next();!0!==o.done;){if(!0!==hi(o.value[1],t.get(o.value[0])))return!1;o=n.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const n=e.entries();for(o=n.next();!0!==o.done;){if(!0!==t.has(o.value[0]))return!1;o=n.next()}return!0}if(null!=e.buffer&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(o=n;0!=o--;)if(e[o]!==t[o])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const l=Object.keys(e).filter((t=>void 0!==e[t]));if(n=l.length,n!==Object.keys(t).filter((e=>void 0!==t[e])).length)return!1;for(o=n;0!=o--;){const n=l[o];if(!0!==hi(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function mi(e){return null!==e&&"object"==typeof e&&!0!==Array.isArray(e)}function gi(e){return"[object Date]"===Object.prototype.toString.call(e)}function bi(e){return"number"==typeof e&&isFinite(e)}const yi=[Aa,ni,Ga,Qa,ai,ui,di];function _i(e,t){t.forEach((t=>{t.install(e),t.__installed=!0}))}var wi={name:"Quasar",version:"2.18.1",install:function(e,t={}){const n={version:"2.18.1"};var o,l,r;!1===vi?(void 0!==t.config&&Object.assign(pi,t.config),n.config={...pi},vi=!0):n.config=t.config||{},o=e,l=t,r={parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]},o.config.globalProperties.$q=r.$q,o.provide("_q_",r.$q),_i(r,yi),void 0!==l.components&&Object.values(l.components).forEach((e=>{!0===mi(e)&&void 0!==e.name&&o.component(e.name,e)})),void 0!==l.directives&&Object.values(l.directives).forEach((e=>{!0===mi(e)&&void 0!==e.name&&o.directive(e.name,e)})),void 0!==l.plugins&&_i(r,Object.values(l.plugins).filter((e=>"function"==typeof e.install&&!1===yi.includes(e)))),!0===La.value&&(r.$q.onSSRHydrated=()=>{r.onSSRHydrated.forEach((e=>{e()})),r.$q.onSSRHydrated=()=>{}})},lang:ui,iconSet:di},ki=(e,t)=>{const n=e.__vccOpts||e;for(const[o,l]of t)n[o]=l;return n};var xi=ki({},[["render",function(e,t){const n=to("router-view");return Sl(),Ol(n)}]]);
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */
const Si=Symbol();var Ci,Ei;(Ei=Ci||(Ci={})).direct="direct",Ei.patchObject="patch object",Ei.patchFunction="patch function";var Ti=()=>function(){const e=re(!0),t=e.run((()=>Ct({})));let n=[],o=[];const l=wt({install(e){l._a=e,e.provide(Si,l),e.config.globalProperties.$pinia=l,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return l}();
/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Li="undefined"!=typeof document;function qi(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Oi=Object.assign;function Pi(e,t){const n={};for(const o in t){const l=t[o];n[o]=Ri(l)?l.map(e):e(l)}return n}const Fi=()=>{},Ri=Array.isArray,Ai=/#/g,Vi=/&/g,Mi=/\//g,Ii=/=/g,Ni=/\?/g,$i=/\+/g,Bi=/%5B/g,zi=/%5D/g,Di=/%5E/g,ji=/%60/g,Ui=/%7B/g,Hi=/%7C/g,Wi=/%7D/g,Ki=/%20/g;function Qi(e){return encodeURI(""+e).replace(Hi,"|").replace(Bi,"[").replace(zi,"]")}function Gi(e){return Qi(e).replace($i,"%2B").replace(Ki,"+").replace(Ai,"%23").replace(Vi,"%26").replace(ji,"`").replace(Ui,"{").replace(Wi,"}").replace(Di,"^")}function Yi(e){return null==e?"":function(e){return Qi(e).replace(Ai,"%23").replace(Ni,"%3F")}(e).replace(Mi,"%2F")}function Ji(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Zi=/\/$/;function Xi(e,t,n="/"){let o,l={},r="",a="";const i=t.indexOf("#");let s=t.indexOf("?");return i<s&&i>=0&&(s=-1),s>-1&&(o=t.slice(0,s),r=t.slice(s+1,i>-1?i:t.length),l=e(r)),i>-1&&(o=o||t.slice(0,i),a=t.slice(i,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),l=o[o.length-1];".."!==l&&"."!==l||o.push("");let r,a,i=n.length-1;for(r=0;r<o.length;r++)if(a=o[r],"."!==a){if(".."!==a)break;i>1&&i--}return n.slice(0,i).join("/")+"/"+o.slice(r).join("/")}(null!=o?o:t,n),{fullPath:o+(r&&"?")+r+a,path:o,query:l,hash:Ji(a)}}function es(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function ts(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ns(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!os(e[n],t[n]))return!1;return!0}function os(e,t){return Ri(e)?ls(e,t):Ri(t)?ls(t,e):e===t}function ls(e,t){return Ri(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const rs={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var as,is,ss,us;function cs(e){if(!e)if(Li){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Zi,"")}(is=as||(as={})).pop="pop",is.push="push",(us=ss||(ss={})).back="back",us.forward="forward",us.unknown="";const ds=/^[^#]+#/;function fs(e,t){return e.replace(ds,"#")+t}const ps=()=>({left:window.scrollX,top:window.scrollY});function vs(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),l="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!l)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(l,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function hs(e,t){return(history.state?history.state.position-t:-1)+e}const ms=new Map;function gs(e,t){const{pathname:n,search:o,hash:l}=t,r=e.indexOf("#");if(r>-1){let t=l.includes(e.slice(r))?e.slice(r).length:1,n=l.slice(t);return"/"!==n[0]&&(n="/"+n),es(n,"")}return es(n,e)+o+l}function bs(e,t,n,o=!1,l=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:l?ps():null}}function ys(e){const{history:t,location:n}=window,o={value:gs(e,n)},l={value:t.state};function r(o,r,a){const i=e.indexOf("#"),s=i>-1?(n.host&&document.querySelector("base")?e:e.slice(i))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](r,"",s),l.value=r}catch(u){console.error(u),n[a?"replace":"assign"](s)}}return l.value||r(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:l,push:function(e,n){const a=Oi({},l.value,t.state,{forward:e,scroll:ps()});r(a.current,a,!0),r(e,Oi({},bs(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){r(e,Oi({},t.state,bs(l.value.back,e,l.value.forward,!0),n,{position:l.value.position}),!0),o.value=e}}}function _s(e){const t=ys(e=cs(e)),n=function(e,t,n,o){let l=[],r=[],a=null;const i=({state:r})=>{const i=gs(e,location),s=n.value,u=t.value;let c=0;if(r){if(n.value=i,t.value=r,a&&a===s)return void(a=null);c=u?r.position-u.position:0}else o(i);l.forEach((e=>{e(n.value,s,{delta:c,type:as.pop,direction:c?c>0?ss.forward:ss.back:ss.unknown})}))};function s(){const{history:e}=window;e.state&&e.replaceState(Oi({},e.state,{scroll:ps()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",s,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(e){l.push(e);const t=()=>{const t=l.indexOf(e);t>-1&&l.splice(t,1)};return r.push(t),t},destroy:function(){for(const e of r)e();r=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",s)}}}(e,t.state,t.location,t.replace);const o=Oi({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:fs.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ws(e){return(e=location.host?e||location.pathname+location.search:"").includes("#")||(e+="#"),_s(e)}function ks(e){return"string"==typeof e||"symbol"==typeof e}const xs=Symbol("");var Ss,Cs;function Es(e,t){return Oi(new Error,{type:e,[xs]:!0},t)}function Ts(e,t){return e instanceof Error&&xs in e&&(null==t||!!(e.type&t))}(Cs=Ss||(Ss={}))[Cs.aborted=4]="aborted",Cs[Cs.cancelled=8]="cancelled",Cs[Cs.duplicated=16]="duplicated";const Ls={sensitive:!1,strict:!1,start:!0,end:!0},qs=/[.+*?^${}()[\]/\\]/g;function Os(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ps(e,t){let n=0;const o=e.score,l=t.score;for(;n<o.length&&n<l.length;){const e=Os(o[n],l[n]);if(e)return e;n++}if(1===Math.abs(l.length-o.length)){if(Fs(o))return 1;if(Fs(l))return-1}return l.length-o.length}function Fs(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Rs={type:0,value:""},As=/[a-zA-Z0-9_]/;function Vs(e,t,n){const o=function(e,t){const n=Oi({},Ls,t),o=[];let l=n.start?"^":"";const r=[];for(const s of e){const e=s.length?[]:[90];n.strict&&!s.length&&(l+="/");for(let t=0;t<s.length;t++){const o=s[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(l+="/"),l+=o.value.replace(qs,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:u,regexp:c}=o;r.push({name:e,repeatable:n,optional:u});const d=c||"[^/]+?";if("[^/]+?"!==d){a+=10;try{new RegExp(`(${d})`)}catch(i){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+i.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=u&&s.length<2?`(?:/${f})`:"/"+f),u&&(f+="?"),l+=f,a+=20,u&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(l+="/?"),n.end?l+="$":n.strict&&!l.endsWith("/")&&(l+="(?:/|$)");const a=new RegExp(l,n.sensitive?"":"i");return{re:a,score:o,keys:r,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",l=r[o-1];n[l.name]=e&&l.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const l of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of l)if(0===e.type)n+=e.value;else if(1===e.type){const{value:r,repeatable:a,optional:i}=e,s=r in t?t[r]:"";if(Ri(s)&&!a)throw new Error(`Provided param "${r}" is an array but it is not repeatable (* or + modifiers)`);const u=Ri(s)?s.join("/"):s;if(!u){if(!i)throw new Error(`Missing required param "${r}"`);l.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=u}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Rs]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${u}": ${e}`)}let n=0,o=n;const l=[];let r;function a(){r&&l.push(r),r=[]}let i,s=0,u="",c="";function d(){u&&(0===n?r.push({type:0,value:u}):1===n||2===n||3===n?(r.length>1&&("*"===i||"+"===i)&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:u,regexp:c,repeatable:"*"===i||"+"===i,optional:"*"===i||"?"===i})):t("Invalid state to consume buffer"),u="")}function f(){u+=i}for(;s<e.length;)if(i=e[s++],"\\"!==i||2===n)switch(n){case 0:"/"===i?(u&&d(),a()):":"===i?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===i?n=2:As.test(i)?f():(d(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&s--);break;case 2:")"===i?"\\"==c[c.length-1]?c=c.slice(0,-1)+i:n=3:c+=i;break;case 3:d(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&s--,c="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${u}"`),d(),a(),l}(e.path),n),l=Oi(o,{record:e,parent:t,children:[],alias:[]});return t&&!l.record.aliasOf==!t.record.aliasOf&&t.children.push(l),l}function Ms(e,t){const n=[],o=new Map;function l(e,n,o){const i=!o,s=Ns(e);s.aliasOf=o&&o.record;const u=Ds(t,e),c=[s];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)c.push(Ns(Oi({},s,{components:o?o.record.components:s.components,path:e,aliasOf:o?o.record:s})))}let d,f;for(const t of c){const{path:c}=t;if(n&&"/"!==c[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(c&&o+c)}if(d=Vs(t,n,u),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),i&&e.name&&!Bs(d)&&r(e.name)),js(d)&&a(d),s.children){const e=s.children;for(let t=0;t<e.length;t++)l(e[t],d,o&&o.children[t])}o=o||d}return f?()=>{r(f)}:Fi}function r(e){if(ks(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(r),t.alias.forEach(r))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(r),e.alias.forEach(r))}}function a(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const l=n+o>>1;Ps(e,t[l])<0?o=l:n=l+1}const l=function(e){let t=e;for(;t=t.parent;)if(js(t)&&0===Ps(e,t))return t;return}(e);l&&(o=t.lastIndexOf(l,o-1));return o}(e,n);n.splice(t,0,e),e.record.name&&!Bs(e)&&o.set(e.record.name,e)}return t=Ds({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>l(e))),{addRoute:l,resolve:function(e,t){let l,r,a,i={};if("name"in e&&e.name){if(l=o.get(e.name),!l)throw Es(1,{location:e});a=l.record.name,i=Oi(Is(t.params,l.keys.filter((e=>!e.optional)).concat(l.parent?l.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Is(e.params,l.keys.map((e=>e.name)))),r=l.stringify(i)}else if(null!=e.path)r=e.path,l=n.find((e=>e.re.test(r))),l&&(i=l.parse(r),a=l.record.name);else{if(l=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!l)throw Es(1,{location:e,currentLocation:t});a=l.record.name,i=Oi({},t.params,e.params),r=l.stringify(i)}const s=[];let u=l;for(;u;)s.unshift(u.record),u=u.parent;return{name:a,path:r,params:i,matched:s,meta:zs(s)}},removeRoute:r,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Is(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ns(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:$s(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function $s(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Bs(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function zs(e){return e.reduce(((e,t)=>Oi(e,t.meta)),{})}function Ds(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function js({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Us(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace($i," "),l=e.indexOf("="),r=Ji(l<0?e:e.slice(0,l)),a=l<0?null:Ji(e.slice(l+1));if(r in t){let e=t[r];Ri(e)||(e=t[r]=[e]),e.push(a)}else t[r]=a}return t}function Hs(e){let t="";for(let n in e){const o=e[n];if(n=Gi(n).replace(Ii,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ri(o)?o.map((e=>e&&Gi(e))):[o&&Gi(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ws(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ri(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Ks=Symbol(""),Qs=Symbol(""),Gs=Symbol(""),Ys=Symbol(""),Js=Symbol("");function Zs(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Xs(e,t,n,o,l,r=(e=>e())){const a=o&&(o.enterCallbacks[l]=o.enterCallbacks[l]||[]);return()=>new Promise(((i,s)=>{const u=e=>{var r;!1===e?s(Es(4,{from:n,to:t})):e instanceof Error?s(e):"string"==typeof(r=e)||r&&"object"==typeof r?s(Es(2,{from:t,to:e})):(a&&o.enterCallbacks[l]===a&&"function"==typeof e&&a.push(e),i())},c=r((()=>e.call(o&&o.instances[l],t,n,u)));let d=Promise.resolve(c);e.length<3&&(d=d.then(u)),d.catch((e=>s(e)))}))}function eu(e,t,n,o,l=(e=>e())){const r=[];for(const a of e)for(const e in a.components){let i=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(qi(i)){const s=(i.__vccOpts||i)[t];s&&r.push(Xs(s,n,o,a,e,l))}else{let s=i();r.push((()=>s.then((r=>{if(!r)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const i=(s=r).__esModule||"Module"===s[Symbol.toStringTag]||s.default&&qi(s.default)?r.default:r;var s;a.mods[e]=r,a.components[e]=i;const u=(i.__vccOpts||i)[t];return u&&Xs(u,n,o,a,e,l)()}))))}}return r}function tu(e){const t=Oo(Gs),n=Oo(Ys),o=ar((()=>{const n=qt(e.to);return t.resolve(n)})),l=ar((()=>{const{matched:e}=o.value,{length:t}=e,l=e[t-1],r=n.matched;if(!l||!r.length)return-1;const a=r.findIndex(ts.bind(null,l));if(a>-1)return a;const i=ou(e[t-2]);return t>1&&ou(l)===i&&r[r.length-1].path!==i?r.findIndex(ts.bind(null,e[t-2])):a})),r=ar((()=>l.value>-1&&function(e,t){for(const n in t){const o=t[n],l=e[n];if("string"==typeof o){if(o!==l)return!1}else if(!Ri(l)||l.length!==o.length||o.some(((e,t)=>e!==l[t])))return!1}return!0}(n.params,o.value.params))),a=ar((()=>l.value>-1&&l.value===n.matched.length-1&&ns(n.params,o.value.params)));return{route:o,href:ar((()=>o.value.href)),isActive:r,isExactActive:a,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[qt(e.replace)?"replace":"push"](qt(e.to)).catch(Fi);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}}}const nu=An({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:tu,setup(e,{slots:t}){const n=ft(tu(e)),{options:o}=Oo(Gs),l=ar((()=>({[lu(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[lu(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&(1===(r=t.default(n)).length?r[0]:r);var r;return e.custom?o:ir("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:l.value},o)}}});function ou(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const lu=(e,t,n)=>null!=e?e:null!=t?t:n;function ru(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const au=An({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Oo(Js),l=ar((()=>e.route||o.value)),r=Oo(Qs,0),a=ar((()=>{let e=qt(r);const{matched:t}=l.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),i=ar((()=>l.value.matched[a.value]));qo(Qs,ar((()=>a.value+1))),qo(Ks,i),qo(Js,l);const s=Ct();return Xo((()=>[s.value,i.value,e.name]),(([e,t,n],[o,l,r])=>{t&&(t.instances[n]=e,l&&l!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=l.leaveGuards),t.updateGuards.size||(t.updateGuards=l.updateGuards))),!e||!t||l&&ts(t,l)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=l.value,r=e.name,a=i.value,u=a&&a.components[r];if(!u)return ru(n.default,{Component:u,route:o});const c=a.props[r],d=c?!0===c?o.params:"function"==typeof c?c(o):c:null,f=ir(u,Oi({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[r]=null)},ref:s}));return ru(n.default,{Component:f,route:o})||f}}});function iu(e){const t=Ms(e.routes,e),n=e.parseQuery||Us,o=e.stringifyQuery||Hs,l=e.history,r=Zs(),a=Zs(),i=Zs(),s=Et(rs);let u=rs;Li&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Pi.bind(null,(e=>""+e)),d=Pi.bind(null,Yi),f=Pi.bind(null,Ji);function p(e,r){if(r=Oi({},r||s.value),"string"==typeof e){const o=Xi(n,e,r.path),a=t.resolve({path:o.path},r),i=l.createHref(o.fullPath);return Oi(o,a,{params:f(a.params),hash:Ji(o.hash),redirectedFrom:void 0,href:i})}let a;if(null!=e.path)a=Oi({},e,{path:Xi(n,e.path,r.path).path});else{const t=Oi({},e.params);for(const e in t)null==t[e]&&delete t[e];a=Oi({},e,{params:d(t)}),r.params=d(r.params)}const i=t.resolve(a,r),u=e.hash||"";i.params=c(f(i.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Oi({},e,{hash:(v=u,Qi(v).replace(Ui,"{").replace(Wi,"}").replace(Di,"^")),path:i.path}));var v;const h=l.createHref(p);return Oi({fullPath:p,hash:u,query:o===Hs?Ws(e.query):e.query||{}},i,{redirectedFrom:void 0,href:h})}function v(e){return"string"==typeof e?Xi(n,e,s.value.path):Oi({},e)}function h(e,t){if(u!==e)return Es(8,{from:t,to:e})}function m(e){return b(e)}function g(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=v(o):{path:o},o.params={}),Oi({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function b(e,t){const n=u=p(e),l=s.value,r=e.state,a=e.force,i=!0===e.replace,c=g(n);if(c)return b(Oi(v(c),{state:"object"==typeof c?Oi({},r,c.state):r,force:a,replace:i}),t||n);const d=n;let f;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,l=n.matched.length-1;return o>-1&&o===l&&ts(t.matched[o],n.matched[l])&&ns(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,l,n)&&(f=Es(16,{to:d,from:l}),P(l,l,!0,!1)),(f?Promise.resolve(f):w(d,l)).catch((e=>Ts(e)?Ts(e,2)?e:O(e):q(e,d,l))).then((e=>{if(e){if(Ts(e,2))return b(Oi({replace:i},v(e.to),{state:"object"==typeof e.to?Oi({},r,e.to.state):r,force:a}),t||d)}else e=x(d,l,!0,i,r);return k(d,l,e),e}))}function y(e,t){const n=h(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=A.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,l,i]=function(e,t){const n=[],o=[],l=[],r=Math.max(t.matched.length,e.matched.length);for(let a=0;a<r;a++){const r=t.matched[a];r&&(e.matched.find((e=>ts(e,r)))?o.push(r):n.push(r));const i=e.matched[a];i&&(t.matched.find((e=>ts(e,i)))||l.push(i))}return[n,o,l]}(e,t);n=eu(o.reverse(),"beforeRouteLeave",e,t);for(const r of o)r.leaveGuards.forEach((o=>{n.push(Xs(o,e,t))}));const s=y.bind(null,e,t);return n.push(s),M(n).then((()=>{n=[];for(const o of r.list())n.push(Xs(o,e,t));return n.push(s),M(n)})).then((()=>{n=eu(l,"beforeRouteUpdate",e,t);for(const o of l)o.updateGuards.forEach((o=>{n.push(Xs(o,e,t))}));return n.push(s),M(n)})).then((()=>{n=[];for(const o of i)if(o.beforeEnter)if(Ri(o.beforeEnter))for(const l of o.beforeEnter)n.push(Xs(l,e,t));else n.push(Xs(o.beforeEnter,e,t));return n.push(s),M(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=eu(i,"beforeRouteEnter",e,t,_),n.push(s),M(n)))).then((()=>{n=[];for(const o of a.list())n.push(Xs(o,e,t));return n.push(s),M(n)})).catch((e=>Ts(e,8)?e:Promise.reject(e)))}function k(e,t,n){i.list().forEach((o=>_((()=>o(e,t,n)))))}function x(e,t,n,o,r){const a=h(e,t);if(a)return a;const i=t===rs,u=Li?history.state:{};n&&(o||i?l.replace(e.fullPath,Oi({scroll:i&&u&&u.scroll},r)):l.push(e.fullPath,r)),s.value=e,P(e,t,n,i),O()}let S;function C(){S||(S=l.listen(((e,t,n)=>{if(!V.listening)return;const o=p(e),r=g(o);if(r)return void b(Oi(r,{replace:!0,force:!0}),o).catch(Fi);u=o;const a=s.value;var i,c;Li&&(i=hs(a.fullPath,n.delta),c=ps(),ms.set(i,c)),w(o,a).catch((e=>Ts(e,12)?e:Ts(e,2)?(b(Oi(v(e.to),{force:!0}),o).then((e=>{Ts(e,20)&&!n.delta&&n.type===as.pop&&l.go(-1,!1)})).catch(Fi),Promise.reject()):(n.delta&&l.go(-n.delta,!1),q(e,o,a)))).then((e=>{(e=e||x(o,a,!1))&&(n.delta&&!Ts(e,8)?l.go(-n.delta,!1):n.type===as.pop&&Ts(e,20)&&l.go(-1,!1)),k(o,a,e)})).catch(Fi)})))}let E,T=Zs(),L=Zs();function q(e,t,n){O(e);const o=L.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function O(e){return E||(E=!e,C(),T.list().forEach((([t,n])=>e?n(e):t())),T.reset()),e}function P(t,n,o,l){const{scrollBehavior:r}=e;if(!Li||!r)return Promise.resolve();const a=!o&&function(e){const t=ms.get(e);return ms.delete(e),t}(hs(t.fullPath,0))||(l||!o)&&history.state&&history.state.scroll||null;return Qt().then((()=>r(t,n,a))).then((e=>e&&vs(e))).catch((e=>q(e,t,n)))}const F=e=>l.go(e);let R;const A=new Set,V={currentRoute:s,listening:!0,addRoute:function(e,n){let o,l;return ks(e)?(o=t.getRecordMatcher(e),l=n):l=e,t.addRoute(l,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:m,replace:function(e){return m(Oi(v(e),{replace:!0}))},go:F,back:()=>F(-1),forward:()=>F(1),beforeEach:r.add,beforeResolve:a.add,afterEach:i.add,onError:L.add,isReady:function(){return E&&s.value!==rs?Promise.resolve():new Promise(((e,t)=>{T.add([e,t])}))},install(e){e.component("RouterLink",nu),e.component("RouterView",au),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>qt(s)}),Li&&!R&&s.value===rs&&(R=!0,m(l.location).catch((e=>{})));const t={};for(const o in rs)Object.defineProperty(t,o,{get:()=>s.value[o],enumerable:!0});e.provide(Gs,this),e.provide(Ys,pt(t)),e.provide(Js,s);const n=e.unmount;A.add(e),e.unmount=function(){A.delete(e),A.size<1&&(u=rs,S&&S(),S=null,s.value=rs,R=!1,E=!1),n()}}};function M(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return V}const su="undefined"!=typeof ResizeObserver,uu=!0===su?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"};var cu=Va({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:t}){let n,o=null,l={width:-1,height:-1};function r(t){!0===t||0===e.debounce||"0"===e.debounce?a():null===o&&(o=setTimeout(a,e.debounce))}function a(){if(null!==o&&(clearTimeout(o),o=null),n){const{offsetWidth:e,offsetHeight:o}=n;e===l.width&&o===l.height||(l={width:e,height:o},t("resize",l))}}const{proxy:i}=Ql();if(i.trigger=r,!0===su){let e;const t=o=>{n=i.$el.parentNode,n?(e=new ResizeObserver(r),e.observe(n),a()):!0!==o&&Qt((()=>{t(!0)}))};return Wn((()=>{t()})),Gn((()=>{null!==o&&clearTimeout(o),void 0!==e&&(void 0!==e.disconnect?e.disconnect():n&&e.unobserve(n))})),Na}{let e=function(){null!==o&&(clearTimeout(o),o=null),void 0!==s&&(void 0!==s.removeEventListener&&s.removeEventListener("resize",r,Ia.passive),s=void 0)},t=function(){e(),(null==n?void 0:n.contentDocument)&&(s=n.contentDocument.defaultView,s.addEventListener("resize",r,Ia.passive),a())};const{isHydrated:l}=function(){const e=Ct(!La.value);return!1===e.value&&Wn((()=>{e.value=!0})),{isHydrated:e}}();let s;return Wn((()=>{Qt((()=>{n=i.$el,n&&t()}))})),Gn(e),()=>{if(!0===l.value)return ir("object",{class:"q--avoid-card-border",style:uu.style,tabindex:-1,type:"text/html",data:uu.url,"aria-hidden":"true",onLoad:t})}}}});function du(e,t){return void 0!==e&&e()||t}function fu(e,t){if(void 0!==e){const t=e();if(null!=t)return t.slice()}return t}function pu(e,t){return void 0!==e?t.concat(e()):t}var vu=Va({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:t,emit:n}){const{proxy:{$q:o}}=Ql(),l=Oo("_q_l_",fi);if(l===fi)return console.error("QHeader needs to be child of QLayout"),fi;const r=Ct(parseInt(e.heightHint,10)),a=Ct(!0),i=ar((()=>!0===e.reveal||-1!==l.view.value.indexOf("H")||o.platform.is.ios&&!0===l.isContainer.value)),s=ar((()=>{if(!0!==e.modelValue)return 0;if(!0===i.value)return!0===a.value?r.value:0;const t=r.value-l.scroll.value.position;return t>0?t:0})),u=ar((()=>!0!==e.modelValue||!0===i.value&&!0!==a.value)),c=ar((()=>!0===e.modelValue&&!0===u.value&&!0===e.reveal)),d=ar((()=>"q-header q-layout__section--marginal "+(!0===i.value?"fixed":"absolute")+"-top"+(!0===e.bordered?" q-header--bordered":"")+(!0===u.value?" q-header--hidden":"")+(!0!==e.modelValue?" q-layout--prevent-focus":""))),f=ar((()=>{const e=l.rows.value.top,t={};return"l"===e[0]&&!0===l.left.space&&(t[!0===o.lang.rtl?"right":"left"]=`${l.left.size}px`),"r"===e[2]&&!0===l.right.space&&(t[!0===o.lang.rtl?"left":"right"]=`${l.right.size}px`),t}));function p(e,t){l.update("header",e,t)}function v(e,t){e.value!==t&&(e.value=t)}function h({height:e}){v(r,e),p("size",e)}function m(e){!0===c.value&&v(a,!0),n("focusin",e)}Xo((()=>e.modelValue),(e=>{p("space",e),v(a,!0),l.animate()})),Xo(s,(e=>{p("offset",e)})),Xo((()=>e.reveal),(t=>{!1===t&&v(a,e.modelValue)})),Xo(a,(e=>{l.animate(),n("reveal",e)})),Xo(l.scroll,(t=>{!0===e.reveal&&v(a,"up"===t.direction||t.position<=e.revealOffset||t.position-t.inflectionPoint<100)}));const g={};return l.instances.header=g,!0===e.modelValue&&p("size",r.value),p("space",e.modelValue),p("offset",s.value),Gn((()=>{l.instances.header===g&&(l.instances.header=void 0,p("size",0),p("offset",0),p("space",!1))})),()=>{const n=fu(t.default,[]);return!0===e.elevated&&n.push(ir("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),n.push(ir(cu,{debounce:0,onResize:h})),ir("header",{class:d.value,style:f.value,onFocusin:m},n)}}}),hu=Va({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:t}){const{proxy:{$q:n}}=Ql(),o=Oo("_q_l_",fi);if(o===fi)return console.error("QPage needs to be a deep child of QLayout"),fi;if(Oo("_q_pc_",fi)===fi)return console.error("QPage needs to be child of QPageContainer"),fi;const l=ar((()=>{const t=(!0===o.header.space?o.header.size:0)+(!0===o.footer.space?o.footer.size:0);if("function"==typeof e.styleFn){const l=!0===o.isContainer.value?o.containerHeight.value:n.screen.height;return e.styleFn(t,l)}return{minHeight:!0===o.isContainer.value?o.containerHeight.value-t+"px":0===n.screen.height?0!==t?`calc(100vh - ${t}px)`:"100vh":n.screen.height-t+"px"}})),r=ar((()=>"q-page"+(!0===e.padding?" q-layout-padding":"")));return()=>ir("main",{class:r.value,style:l.value},du(t.default))}}),mu=Va({name:"QPageContainer",setup(e,{slots:t}){const{proxy:{$q:n}}=Ql(),o=Oo("_q_l_",fi);if(o===fi)return console.error("QPageContainer needs to be child of QLayout"),fi;qo("_q_pc_",!0);const l=ar((()=>{const e={};return!0===o.header.space&&(e.paddingTop=`${o.header.size}px`),!0===o.right.space&&(e["padding"+(!0===n.lang.rtl?"Left":"Right")]=`${o.right.size}px`),!0===o.footer.space&&(e.paddingBottom=`${o.footer.size}px`),!0===o.left.space&&(e["padding"+(!0===n.lang.rtl?"Right":"Left")]=`${o.left.size}px`),e}));return()=>ir("div",{class:"q-page-container",style:l.value},du(t.default))}});function gu(e,t){const n=e.style;for(const o in t)n[o]=t[o]}function bu(e,t){if(null==e||!0===e.contains(t))return!0;for(let n=e.nextElementSibling;null!==n;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}const yu=[Element,String],_u=[null,document,document.body,document.scrollingElement,document.documentElement];function wu(e,t){let n=function(e){if(null==e)return;if("string"==typeof e)try{return document.querySelector(e)||void 0}catch(n){return}const t=qt(e);return t?t.$el||t:void 0}(t);if(void 0===n){if(null==e)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return _u.includes(n)?window:n}function ku(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function xu(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let Su;function Cu(){if(void 0!==Su)return Su;const e=document.createElement("p"),t=document.createElement("div");gu(e,{width:"100%",height:"200px"}),gu(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let o=e.offsetWidth;return n===o&&(o=t.clientWidth),t.remove(),Su=n-o,Su}function Eu(e,t=!0){return!(!e||e.nodeType!==Node.ELEMENT_NODE)&&(t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"])))}const{passive:Tu}=Ia,Lu=["both","horizontal","vertical"];var qu=Va({name:"QScrollObserver",props:{axis:{type:String,validator:e=>Lu.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:yu},emits:["scroll"],setup(e,{emit:t}){const n={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let o,l,r=null;function a(){null==r||r();const l=Math.max(0,ku(o)),a=xu(o),i={top:l-n.position.top,left:a-n.position.left};if("vertical"===e.axis&&0===i.top||"horizontal"===e.axis&&0===i.left)return;const s=Math.abs(i.top)>=Math.abs(i.left)?i.top<0?"up":"down":i.left<0?"left":"right";n.position={top:l,left:a},n.directionChanged=n.direction!==s,n.delta=i,!0===n.directionChanged&&(n.direction=s,n.inflectionPoint=n.position),t("scroll",{...n})}function i(){o=wu(l,e.scrollTarget),o.addEventListener("scroll",u,Tu),u(!0)}function s(){void 0!==o&&(o.removeEventListener("scroll",u,Tu),o=void 0)}function u(t){if(!0===t||0===e.debounce||"0"===e.debounce)a();else if(null===r){const[t,n]=e.debounce?[setTimeout(a,e.debounce),clearTimeout]:[requestAnimationFrame(a),cancelAnimationFrame];r=()=>{n(t),r=null}}}Xo((()=>e.scrollTarget),(()=>{s(),i()}));const{proxy:c}=Ql();return Xo((()=>c.$q.lang.rtl),a),Wn((()=>{l=c.$el.parentNode,i()})),Gn((()=>{null==r||r(),s()})),Object.assign(c,{trigger:u,getPosition:()=>n}),Na}}),Ou=Va({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:t,emit:n}){const{proxy:{$q:o}}=Ql(),l=Ct(null),r=Ct(o.screen.height),a=Ct(!0===e.container?0:o.screen.width),i=Ct({position:0,direction:"down",inflectionPoint:0}),s=Ct(0),u=Ct(!0===La.value?0:Cu()),c=ar((()=>"q-layout q-layout--"+(!0===e.container?"containerized":"standard"))),d=ar((()=>!1===e.container?{minHeight:o.screen.height+"px"}:null)),f=ar((()=>0!==u.value?{[!0===o.lang.rtl?"left":"right"]:`${u.value}px`}:null)),p=ar((()=>0!==u.value?{[!0===o.lang.rtl?"right":"left"]:0,[!0===o.lang.rtl?"left":"right"]:`-${u.value}px`,width:`calc(100% + ${u.value}px)`}:null));function v(t){if(!0===e.container||!0!==document.qScrollPrevented){const o={position:t.position.top,direction:t.direction,directionChanged:t.directionChanged,inflectionPoint:t.inflectionPoint.top,delta:t.delta.top};i.value=o,void 0!==e.onScroll&&n("scroll",o)}}function h(t){const{height:o,width:l}=t;let i=!1;r.value!==o&&(i=!0,r.value=o,void 0!==e.onScrollHeight&&n("scrollHeight",o),g()),a.value!==l&&(i=!0,a.value=l),!0===i&&void 0!==e.onResize&&n("resize",t)}function m({height:e}){s.value!==e&&(s.value=e,g())}function g(){if(!0===e.container){const e=r.value>s.value?Cu():0;u.value!==e&&(u.value=e)}}let b=null;const y={instances:{},view:ar((()=>e.view)),isContainer:ar((()=>e.container)),rootRef:l,height:r,containerHeight:s,scrollbarWidth:u,totalWidth:ar((()=>a.value+u.value)),rows:ar((()=>{const t=e.view.toLowerCase().split(" ");return{top:t[0].split(""),middle:t[1].split(""),bottom:t[2].split("")}})),header:ft({size:0,offset:0,space:!1}),right:ft({size:300,offset:0,space:!1}),footer:ft({size:0,offset:0,space:!1}),left:ft({size:300,offset:0,space:!1}),scroll:i,animate(){null!==b?clearTimeout(b):document.body.classList.add("q-body--layout-animate"),b=setTimeout((()=>{b=null,document.body.classList.remove("q-body--layout-animate")}),155)},update(e,t,n){y[e][t]=n}};if(qo("_q_l_",y),Cu()>0){let t=function(){r=null,a.classList.remove("hide-scrollbar")},n=function(){if(null===r){if(a.scrollHeight>o.screen.height)return;a.classList.add("hide-scrollbar")}else clearTimeout(r);r=setTimeout(t,300)},l=function(e){null!==r&&"remove"===e&&(clearTimeout(r),t()),window[`${e}EventListener`]("resize",n)},r=null;const a=document.body;Xo((()=>!0!==e.container?"add":"remove"),l),!0!==e.container&&l("add"),Yn((()=>{l("remove")}))}return()=>{const n=pu(t.default,[ir(qu,{onScroll:v}),ir(cu,{onResize:h})]),o=ir("div",{class:c.value,style:d.value,ref:!0===e.container?void 0:l,tabindex:-1},n);return!0===e.container?ir("div",{class:"q-layout-container overflow-hidden",ref:l},[ir(cu,{onResize:m}),ir("div",{class:"absolute-full",style:f.value},[ir("div",{class:"scroll",style:p.value},[o])])]):o}}}),Pu=Va({name:"QSpace",setup(){const e=ir("div",{class:"q-space"});return()=>e}});const Fu={xs:18,sm:24,md:32,lg:38,xl:46},Ru={size:String};function Au(e,t=Fu){return ar((()=>void 0!==e.size?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null))}const Vu="0 0 24 24",Mu=e=>e,Iu=e=>`ionicons ${e}`,Nu={"mdi-":e=>`mdi ${e}`,"icon-":Mu,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":Iu,"ion-ios":Iu,"ion-logo":Iu,"iconfont ":Mu,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`,"i-":Mu},$u={o_:"-outlined",r_:"-round",s_:"-sharp"},Bu={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},zu=new RegExp("^("+Object.keys(Nu).join("|")+")"),Du=new RegExp("^("+Object.keys($u).join("|")+")"),ju=new RegExp("^("+Object.keys(Bu).join("|")+")"),Uu=/^[Mm]\s?[-+]?\.?\d/,Hu=/^img:/,Wu=/^svguse:/,Ku=/^ion-/,Qu=/^(fa-(classic|sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /;var Gu=Va({name:"QIcon",props:{...Ru,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=Ql(),o=Au(e),l=ar((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),r=ar((()=>{let t,o=e.name;if("none"===o||!o)return{none:!0};if(null!==n.iconMapFn){const e=n.iconMapFn(o);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls,content:void 0!==e.content?e.content:" "};if(o=e.icon,"none"===o||!o)return{none:!0}}}if(!0===Uu.test(o)){const[e,t=Vu]=o.split("|");return{svg:!0,viewBox:t,nodes:e.split("&&").map((e=>{const[t,n,o]=e.split("@@");return ir("path",{style:n,d:t,transform:o})}))}}if(!0===Hu.test(o))return{img:!0,src:o.substring(4)};if(!0===Wu.test(o)){const[e,t=Vu]=o.split("|");return{svguse:!0,src:e.substring(7),viewBox:t}}let l=" ";const r=o.match(zu);if(null!==r)t=Nu[r[1]](o);else if(!0===Qu.test(o))t=o;else if(!0===Ku.test(o))t=`ionicons ion-${!0===n.platform.is.ios?"ios":"md"}${o.substring(3)}`;else if(!0===ju.test(o)){t="notranslate material-symbols";const e=o.match(ju);null!==e&&(o=o.substring(6),t+=Bu[e[1]]),l=o}else{t="notranslate material-icons";const e=o.match(Du);null!==e&&(o=o.substring(2),t+=$u[e[1]]),l=o}return{cls:t,content:l}}));return()=>{const n={class:l.value,style:o.value,"aria-hidden":"true"};return!0===r.value.none?ir(e.tag,n,du(t.default)):!0===r.value.img?ir(e.tag,n,pu(t.default,[ir("img",{src:r.value.src})])):!0===r.value.svg?ir(e.tag,n,pu(t.default,[ir("svg",{viewBox:r.value.viewBox||"0 0 24 24"},r.value.nodes)])):!0===r.value.svguse?ir(e.tag,n,pu(t.default,[ir("svg",{viewBox:r.value.viewBox},[ir("use",{"xlink:href":r.value.src})])])):(void 0!==r.value.cls&&(n.class+=" "+r.value.cls),ir(e.tag,n,pu(t.default,[r.value.content])))}}});const Yu={dark:{type:Boolean,default:null}};function Ju(e,t){return ar((()=>null===e.dark?t.dark.isActive:e.dark))}const Zu={name:String};function Xu(e){return ar((()=>e.name||e.for))}var ec={xs:30,sm:35,md:40,lg:50,xl:60};const tc={...Yu,...Ru,...Zu,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>"tf"===e||"ft"===e},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},nc=["update:modelValue"];function oc(e,t){const{props:n,slots:o,emit:l,proxy:r}=Ql(),{$q:a}=r,i=Ju(n,a),s=Ct(null),{refocusTargetEl:u,refocusTarget:c}=function(e,t){const n=Ct(null);return{refocusTargetEl:ar((()=>!0===e.disable?null:ir("span",{ref:n,class:"no-outline",tabindex:-1}))),refocusTarget:function(e){const o=t.value;!0!==(null==e?void 0:e.qAvoidFocus)&&(0===(null==e?void 0:e.type.indexOf("key"))?document.activeElement!==o&&!0===(null==o?void 0:o.contains(document.activeElement))&&o.focus():null===n.value||void 0!==e&&!0!==(null==o?void 0:o.contains(e.target))||n.value.focus())}}}(n,s),d=Au(n,ec),f=ar((()=>void 0!==n.val&&Array.isArray(n.modelValue))),p=ar((()=>{const e=_t(n.val);return!0===f.value?n.modelValue.findIndex((t=>_t(t)===e)):-1})),v=ar((()=>!0===f.value?-1!==p.value:_t(n.modelValue)===_t(n.trueValue))),h=ar((()=>!0===f.value?-1===p.value:_t(n.modelValue)===_t(n.falseValue))),m=ar((()=>!1===v.value&&!1===h.value)),g=ar((()=>!0===n.disable?-1:n.tabindex||0)),b=ar((()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(!0===n.disable?" disabled":"")+(!0===i.value?` q-${e}--dark`:"")+(!0===n.dense?` q-${e}--dense`:"")+(!0===n.leftLabel?" reverse":""))),y=ar((()=>{const t=!0===v.value?"truthy":!0===h.value?"falsy":"indet",o=void 0===n.color||!0!==n.keepColor&&("toggle"===e?!0!==v.value:!0===h.value)?"":` text-${n.color}`;return`q-${e}__inner relative-position non-selectable q-${e}__inner--${t}${o}`})),_=function(e={}){return(t,n,o)=>{t[n](ir("input",{class:"hidden"+(o||""),...e.value}))}}(ar((()=>{const e={type:"checkbox"};return void 0!==n.name&&Object.assign(e,{".checked":v.value,"^checked":!0===v.value?"checked":void 0,name:n.name,value:!0===f.value?n.val:n.trueValue}),e}))),w=ar((()=>{const t={tabindex:g.value,role:"toggle"===e?"switch":"checkbox","aria-label":n.label,"aria-checked":!0===m.value?"mixed":!0===v.value?"true":"false"};return!0===n.disable&&(t["aria-disabled"]="true"),t}));function k(e){void 0!==e&&(Da(e),c(e)),!0!==n.disable&&l("update:modelValue",function(){if(!0===f.value){if(!0===v.value){const e=n.modelValue.slice();return e.splice(p.value,1),e}return n.modelValue.concat([n.val])}if(!0===v.value){if("ft"!==n.toggleOrder||!1===n.toggleIndeterminate)return n.falseValue}else{if(!0!==h.value)return"ft"!==n.toggleOrder?n.trueValue:n.falseValue;if("ft"===n.toggleOrder||!1===n.toggleIndeterminate)return n.trueValue}return n.indeterminateValue}(),e)}function x(e){13!==e.keyCode&&32!==e.keyCode||Da(e)}function S(e){13!==e.keyCode&&32!==e.keyCode||k(e)}const C=t(v,m);return Object.assign(r,{toggle:k}),()=>{const t=C();!0!==n.disable&&_(t,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const l=[ir("div",{class:y.value,style:d.value,"aria-hidden":"true"},t)];null!==u.value&&l.push(u.value);const r=void 0!==n.label?pu(o.default,[n.label]):du(o.default);return void 0!==r&&l.push(ir("div",{class:`q-${e}__label q-anchor--skip`},r)),ir("div",{ref:s,class:b.value,...w.value,onClick:k,onKeydown:x,onKeyup:S},l)}}var lc=Va({name:"QToggle",props:{...tc,icon:String,iconColor:String},emits:nc,setup:e=>oc("toggle",(function(t,n){const o=ar((()=>(!0===t.value?e.checkedIcon:!0===n.value?e.indeterminateIcon:e.uncheckedIcon)||e.icon)),l=ar((()=>!0===t.value?e.iconColor:null));return()=>[ir("div",{class:"q-toggle__track"}),ir("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},void 0!==o.value?[ir(Gu,{name:o.value,color:l.value})]:void 0)]}))}),rc=Va({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:t}){const n=ar((()=>"q-toolbar row no-wrap items-center"+(!0===e.inset?" q-toolbar--inset":"")));return()=>ir("div",{class:n.value,role:"toolbar"},du(t.default))}});const ac=chrome.runtime.getURL("assets/config.js"),ic=null!=(e=globalThis.browser)?e:globalThis.chrome;const sc={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},uc={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function cc(){const e=await async function(){var e,t;const n=await ic.storage.local.get("defaultConfig");if(null==(e=n.defaultConfig)?void 0:e.apiKey)return n.defaultConfig;let o={};const l=["DelayTime","RepeatTimes","port"],r=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],a=await fetch(ac),i=(await a.text()).replace(/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,""),s=i.slice(i.indexOf("{")+1,i.lastIndexOf("}")),u=JSON.stringify(s).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),c=u.indexOf("blackUrlList"),d=u.slice(c),f=d.indexOf("],");let p=d.slice(0,f+1);u.replace(p,"").split(",").forEach((e=>{const[t,n]=e.split(":");if(t&&n){let e=n.replaceAll("'","").replaceAll('"',"");for(let n=0;n<l.length;n++)t.endsWith(l[n])&&(e=Number(e));for(let n=0;n<r.length;n++)t.startsWith(r[n])&&(e="true"===e);o[t]=e}})),p=p.replaceAll("'","").replaceAll('"',"");const v=p.indexOf(":["),h=p.slice(v+2,p.length-1);o.blackUrlList=h.split(",");const m=await ic.storage.local.get("config");return(null==(t=null==m?void 0:m.config)?void 0:t.apiKey)&&(o.apiKey=m.config.apiKey),ic.storage.local.set({defaultConfig:o}),o}(),t=Object.keys(e);for(let n of t)if(("proxyType"!==n||uc[n].includes(e[n]))&&(!n.endsWith("Mode")||uc.mode.includes(e[n]))){if("port"===n){if("number"!=typeof e.port)continue;sc.port=e.port}Reflect.has(sc,n)&&typeof sc[n]==typeof e[n]&&(sc[n]=e[n])}return sc}const dc=cc();let fc={default:dc,async get(e){return(await this.getAll())[e]},async getAll(){const e=await cc(),t=await ic.storage.local.get("config");return fc.joinConfig(e,t.config)},async set(e){const t=await fc.getAll(),n=fc.joinConfig(t,e);return ic.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let o in e)n[o]=e[o];if(t)for(let o in t)n[o]=t[o];return n}};var pc=Va({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const n=ar((()=>"q-item__section column q-item__section--"+(!0===e.avatar||!0===e.side||!0===e.thumbnail?"side":"main")+(!0===e.top?" q-item__section--top justify-start":" justify-center")+(!0===e.avatar?" q-item__section--avatar":"")+(!0===e.thumbnail?" q-item__section--thumbnail":"")+(!0===e.noWrap?" q-item__section--nowrap":"")));return()=>ir("div",{class:n.value},du(t.default))}}),vc=Va({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=ar((()=>parseInt(e.lines,10))),o=ar((()=>"q-item__label"+(!0===e.overline?" q-item__label--overline text-overline":"")+(!0===e.caption?" q-item__label--caption text-caption":"")+(!0===e.header?" q-item__label--header":"")+(1===n.value?" ellipsis":""))),l=ar((()=>void 0!==e.lines&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null));return()=>ir("div",{style:l.value,class:o.value},du(t.default))}});function hc(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function mc(e){return void 0!==e.appContext.config.globalProperties.$router}function gc(e){return!0===e.isUnmounted||!0===e.isDeactivated}function bc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function yc(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function _c(e,t){return!0===Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function wc(e,t){return!0===Array.isArray(e)?_c(e,t):!0===Array.isArray(t)?_c(t,e):e===t}const kc={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},xc={...kc,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function Sc({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=Ql(),{props:o,proxy:l,emit:r}=n,a=mc(n),i=ar((()=>!0!==o.disable&&void 0!==o.href)),s=ar(!0===t?()=>!0===a&&!0!==o.disable&&!0!==i.value&&void 0!==o.to&&null!==o.to&&""!==o.to:()=>!0===a&&!0!==i.value&&void 0!==o.to&&null!==o.to&&""!==o.to),u=ar((()=>!0===s.value?b(o.to):null)),c=ar((()=>null!==u.value)),d=ar((()=>!0===i.value||!0===c.value)),f=ar((()=>"a"===o.type||!0===d.value?"a":o.tag||e||"div")),p=ar((()=>!0===i.value?{href:o.href,target:o.target}:!0===c.value?{href:u.value.href,target:o.target}:{})),v=ar((()=>{if(!1===c.value)return-1;const{matched:e}=u.value,{length:t}=e,n=e[t-1];if(void 0===n)return-1;const o=l.$route.matched;if(0===o.length)return-1;const r=o.findIndex(yc.bind(null,n));if(-1!==r)return r;const a=bc(e[t-2]);return t>1&&bc(n)===a&&o[o.length-1].path!==a?o.findIndex(yc.bind(null,e[t-2])):r})),h=ar((()=>!0===c.value&&-1!==v.value&&function(e,t){for(const n in t){const o=t[n],l=e[n];if("string"==typeof o){if(o!==l)return!1}else if(!1===Array.isArray(l)||l.length!==o.length||o.some(((e,t)=>e!==l[t])))return!1}return!0}(l.$route.params,u.value.params))),m=ar((()=>!0===h.value&&v.value===l.$route.matched.length-1&&function(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!1===wc(e[n],t[n]))return!1;return!0}(l.$route.params,u.value.params))),g=ar((()=>!0===c.value?!0===m.value?` ${o.exactActiveClass} ${o.activeClass}`:!0===o.exact?"":!0===h.value?` ${o.activeClass}`:"":""));function b(e){try{return l.$router.resolve(e)}catch(my){}return null}function y(e,{returnRouterError:t,to:n=o.to,replace:r=o.replace}={}){if(!0===o.disable)return e.preventDefault(),Promise.resolve(!1);if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||void 0!==e.button&&0!==e.button||"_blank"===o.target)return Promise.resolve(!1);e.preventDefault();const a=l.$router[!0===r?"replace":"push"](n);return!0===t?a:a.then((()=>{})).catch((()=>{}))}return{hasRouterLink:c,hasHrefLink:i,hasLink:d,linkTag:f,resolvedLink:u,linkIsActive:h,linkIsExactActive:m,linkClass:g,linkAttrs:p,getLink:b,navigateToRouterLink:y,navigateOnClick:function(e){if(!0===c.value){const t=t=>y(e,t);r("click",e,t),!0!==e.defaultPrevented&&t()}else r("click",e)}}}var Cc=Va({name:"QItem",props:{...Yu,...xc,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:o}}=Ql(),l=Ju(e,o),{hasLink:r,linkAttrs:a,linkClass:i,linkTag:s,navigateOnClick:u}=Sc(),c=Ct(null),d=Ct(null),f=ar((()=>!0===e.clickable||!0===r.value||"label"===e.tag)),p=ar((()=>!0!==e.disable&&!0===f.value)),v=ar((()=>"q-item q-item-type row no-wrap"+(!0===e.dense?" q-item--dense":"")+(!0===l.value?" q-item--dark":"")+(!0===r.value&&null===e.active?i.value:!0===e.active?" q-item--active"+(void 0!==e.activeClass?` ${e.activeClass}`:""):"")+(!0===e.disable?" disabled":"")+(!0===p.value?" q-item--clickable q-link cursor-pointer "+(!0===e.manualFocus?"q-manual-focusable":"q-focusable q-hoverable")+(!0===e.focused?" q-manual-focusable--focused":""):""))),h=ar((()=>{if(void 0===e.insetLevel)return null;return{["padding"+(!0===o.lang.rtl?"Right":"Left")]:16+56*e.insetLevel+"px"}}));function m(e){!0===p.value&&(null!==d.value&&!0!==e.qAvoidFocus&&(!0!==e.qKeyEvent&&document.activeElement===c.value?d.value.focus():document.activeElement===d.value&&c.value.focus()),u(e))}function g(e){if(!0===p.value&&!0===ei(e,[13,32])){Da(e),e.qKeyEvent=!0;const t=new MouseEvent("click",e);t.qKeyEvent=!0,c.value.dispatchEvent(t)}n("keyup",e)}return()=>{const n={ref:c,class:v.value,style:h.value,role:"listitem",onClick:m,onKeyup:g};return!0===p.value?(n.tabindex=e.tabindex||"0",Object.assign(n,a.value)):!0===f.value&&(n["aria-disabled"]="true"),ir(s.value,n,function(){const e=fu(t.default,[]);return!0===p.value&&e.unshift(ir("div",{class:"q-focus-helper",tabindex:-1,ref:d})),e}())}}});const Ec={size:{type:[String,Number],default:"1em"},color:String};function Tc(e){return{cSize:ar((()=>e.size in Fu?`${Fu[e.size]}px`:e.size)),classes:ar((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}var Lc=Va({name:"QSpinner",props:{...Ec,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=Tc(e);return()=>ir("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[ir("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function qc(e,t){return null==e?!0===t?`f_${aa()}`:null:e}const Oc=/^on[A-Z]/;function Pc(){const{attrs:e,vnode:t}=Ql(),n={listeners:Ct({}),attributes:Ct({})};function o(){const o={},l={};for(const t in e)"class"!==t&&"style"!==t&&!1===Oc.test(t)&&(o[t]=e[t]);for(const e in t.props)!0===Oc.test(e)&&(l[e]=t.props[e]);n.attributes.value=o,n.listeners.value=l}return Kn(o),o(),n}const Fc=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,Rc=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Ac=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Vc=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,Mc=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,Ic={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>Fc.test(e),hexaColor:e=>Rc.test(e),hexOrHexaColor:e=>Ac.test(e),rgbColor:e=>Vc.test(e),rgbaColor:e=>Mc.test(e),rgbOrRgbaColor:e=>Vc.test(e)||Mc.test(e),hexOrRgbColor:e=>Fc.test(e)||Vc.test(e),hexaOrRgbaColor:e=>Rc.test(e)||Mc.test(e),anyColor:e=>Ac.test(e)||Vc.test(e)||Mc.test(e)},Nc=[!0,!1,"ondemand"],$c={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>Nc.includes(e)}};function Bc(e,t){const{props:n,proxy:o}=Ql(),l=Ct(!1),r=Ct(null),a=Ct(!1);!function({validate:e,resetValidation:t,requiresQForm:n}){const o=Oo("_q_fo_",!1);if(!1!==o){const{props:n,proxy:l}=Ql();Object.assign(l,{validate:e,resetValidation:t}),Xo((()=>n.disable),(e=>{!0===e?("function"==typeof t&&t(),o.unbindComponent(l)):o.bindComponent(l)})),Wn((()=>{!0!==n.disable&&o.bindComponent(l)})),Gn((()=>{!0!==n.disable&&o.unbindComponent(l)}))}else!0===n&&console.error("Parent QForm not found on useFormChild()!")}({validate:h,resetValidation:v});let i,s=0;const u=ar((()=>void 0!==n.rules&&null!==n.rules&&0!==n.rules.length)),c=ar((()=>!0!==n.disable&&!0===u.value&&!1===t.value)),d=ar((()=>!0===n.error||!0===l.value)),f=ar((()=>"string"==typeof n.errorMessage&&0!==n.errorMessage.length?n.errorMessage:r.value));function p(){"ondemand"!==n.lazyRules&&!0===c.value&&!0===a.value&&m()}function v(){s++,t.value=!1,a.value=!1,l.value=!1,r.value=null,m.cancel()}function h(e=n.modelValue){if(!0===n.disable||!1===u.value)return!0;const o=++s,i=!0!==t.value?()=>{a.value=!0}:()=>{},c=(e,n)=>{!0===e&&i(),l.value=e,r.value=n||null,t.value=!1},d=[];for(let t=0;t<n.rules.length;t++){const o=n.rules[t];let l;if("function"==typeof o?l=o(e,Ic):"string"==typeof o&&void 0!==Ic[o]&&(l=Ic[o](e)),!1===l||"string"==typeof l)return c(!0,l),!1;!0!==l&&void 0!==l&&d.push(l)}return 0===d.length?(c(!1),!0):(t.value=!0,Promise.all(d).then((e=>{if(void 0===e||!1===Array.isArray(e)||0===e.length)return o===s&&c(!1),!0;const t=e.find((e=>!1===e||"string"==typeof e));return o===s&&c(void 0!==t,t),void 0===t}),(e=>(o===s&&(console.error(e),c(!0)),!1))))}Xo((()=>n.modelValue),(()=>{a.value=!0,!0===c.value&&!1===n.lazyRules&&m()})),Xo((()=>n.reactiveRules),(e=>{!0===e?void 0===i&&(i=Xo((()=>n.rules),p,{immediate:!0,deep:!0})):void 0!==i&&(i(),i=void 0)}),{immediate:!0}),Xo((()=>n.lazyRules),p),Xo(e,(e=>{!0===e?a.value=!0:!0===c.value&&"ondemand"!==n.lazyRules&&m()}));const m=Ha(h,0);return Gn((()=>{null==i||i(),m.cancel()})),Object.assign(o,{resetValidation:v,validate:h}),Ta(o,"hasError",(()=>d.value)),{isDirtyModel:a,hasRules:u,hasError:d,errorMessage:f,validate:h,resetValidation:v}}let zc=[],Dc=[];function jc(e){Dc=Dc.filter((t=>t!==e))}function Uc(e){jc(e),0===Dc.length&&0!==zc.length&&(zc[zc.length-1](),zc=[])}function Hc(e){0===Dc.length?e():zc.push(e)}function Wc(e){return null!=e&&0!==(""+e).length}const Kc={...{...Yu,...$c,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},maxlength:[Number,String]},Qc=["update:modelValue","clear","focus","blur"];function Gc({requiredForAttr:e=!0,tagProp:t,changeEvent:n=!1}={}){const{props:o,proxy:l}=Ql(),r=Ju(o,l.$q),a=function({getValue:e,required:t=!0}={}){if(!0===La.value){const o=Ct(void 0!==e?null==(n=e())?null:n:null);return!0===t&&null===o.value&&Wn((()=>{o.value=`f_${aa()}`})),void 0!==e&&Xo(e,(e=>{o.value=qc(e,t)})),o}var n;return void 0!==e?ar((()=>qc(e(),t))):Ct(`f_${aa()}`)}({required:e,getValue:()=>o.for});return{requiredForAttr:e,changeEvent:n,tag:!0===t?ar((()=>o.tag)):{value:"label"},isDark:r,editable:ar((()=>!0!==o.disable&&!0!==o.readonly)),innerLoading:Ct(!1),focused:Ct(!1),hasPopupOpen:!1,splitAttrs:Pc(),targetUid:a,rootRef:Ct(null),targetRef:Ct(null),controlRef:Ct(null)}}function Yc(e){const{props:t,emit:n,slots:o,attrs:l,proxy:r}=Ql(),{$q:a}=r;let i=null;void 0===e.hasValue&&(e.hasValue=ar((()=>Wc(t.modelValue)))),void 0===e.emitValue&&(e.emitValue=e=>{n("update:modelValue",e)}),void 0===e.controlEvents&&(e.controlEvents={onFocusin:S,onFocusout:C}),Object.assign(e,{clearValue:E,onControlFocusin:S,onControlFocusout:C,focus:x}),void 0===e.computedCounter&&(e.computedCounter=ar((()=>{if(!1!==t.counter){const e="string"==typeof t.modelValue||"number"==typeof t.modelValue?(""+t.modelValue).length:!0===Array.isArray(t.modelValue)?t.modelValue.length:0,n=void 0!==t.maxlength?t.maxlength:t.maxValues;return e+(void 0!==n?" / "+n:"")}})));const{isDirtyModel:s,hasRules:u,hasError:c,errorMessage:d,resetValidation:f}=Bc(e.focused,e.innerLoading),p=void 0!==e.floatingLabel?ar((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.floatingLabel.value)):ar((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.hasValue.value)),v=ar((()=>!0===t.bottomSlots||void 0!==t.hint||!0===u.value||!0===t.counter||null!==t.error)),h=ar((()=>!0===t.filled?"filled":!0===t.outlined?"outlined":!0===t.borderless?"borderless":t.standout?"standout":"standard")),m=ar((()=>`q-field row no-wrap items-start q-field--${h.value}`+(void 0!==e.fieldClass?` ${e.fieldClass.value}`:"")+(!0===t.rounded?" q-field--rounded":"")+(!0===t.square?" q-field--square":"")+(!0===p.value?" q-field--float":"")+(!0===b.value?" q-field--labeled":"")+(!0===t.dense?" q-field--dense":"")+(!0===t.itemAligned?" q-field--item-aligned q-item-type":"")+(!0===e.isDark.value?" q-field--dark":"")+(void 0===e.getControl?" q-field--auto-height":"")+(!0===e.focused.value?" q-field--focused":"")+(!0===c.value?" q-field--error":"")+(!0===c.value||!0===e.focused.value?" q-field--highlighted":"")+(!0!==t.hideBottomSpace&&!0===v.value?" q-field--with-bottom":"")+(!0===t.disable?" q-field--disabled":!0===t.readonly?" q-field--readonly":""))),g=ar((()=>"q-field__control relative-position row no-wrap"+(void 0!==t.bgColor?` bg-${t.bgColor}`:"")+(!0===c.value?" text-negative":"string"==typeof t.standout&&0!==t.standout.length&&!0===e.focused.value?` ${t.standout}`:void 0!==t.color?` text-${t.color}`:""))),b=ar((()=>!0===t.labelSlot||void 0!==t.label)),y=ar((()=>"q-field__label no-pointer-events absolute ellipsis"+(void 0!==t.labelColor&&!0!==c.value?` text-${t.labelColor}`:""))),_=ar((()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:p.value,modelValue:t.modelValue,emitValue:e.emitValue}))),w=ar((()=>{const n={};return e.targetUid.value&&(n.for=e.targetUid.value),!0===t.disable&&(n["aria-disabled"]="true"),n}));function k(){var t;const n=document.activeElement;let o=null==(t=e.targetRef)?void 0:t.value;!o||null!==n&&n.id===e.targetUid.value||(!0===o.hasAttribute("tabindex")||(o=o.querySelector("[tabindex]")),o!==n&&(null==o||o.focus({preventScroll:!0})))}function x(){Hc(k)}function S(t){null!==i&&(clearTimeout(i),i=null),!0===e.editable.value&&!1===e.focused.value&&(e.focused.value=!0,n("focus",t))}function C(t,o){null!==i&&clearTimeout(i),i=setTimeout((()=>{i=null,(!0!==document.hasFocus()||!0!==e.hasPopupOpen&&void 0!==e.controlRef&&null!==e.controlRef.value&&!1===e.controlRef.value.contains(document.activeElement))&&(!0===e.focused.value&&(e.focused.value=!1,n("blur",t)),null==o||o())}))}function E(o){var l;if(Da(o),!0!==a.platform.is.mobile){((null==(l=e.targetRef)?void 0:l.value)||e.rootRef.value).focus()}else!0===e.rootRef.value.contains(document.activeElement)&&document.activeElement.blur();"file"===t.type&&(e.inputRef.value.value=null),n("update:modelValue",null),!0===e.changeEvent&&n("change",null),n("clear",t.modelValue),Qt((()=>{const e=s.value;f(),s.value=e}))}function T(e){[13,32].includes(e.keyCode)&&E(e)}function L(){const n=[];return void 0!==o.prepend&&n.push(ir("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:za},o.prepend())),n.push(ir("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},function(){const n=[];void 0!==t.prefix&&null!==t.prefix&&n.push(ir("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),void 0!==e.getShadowControl&&!0===e.hasShadow.value&&n.push(e.getShadowControl());void 0!==e.getControl?n.push(e.getControl()):void 0!==o.rawControl?n.push(o.rawControl()):void 0!==o.control&&n.push(ir("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0},o.control(_.value)));return!0===b.value&&n.push(ir("div",{class:y.value},du(o.label,t.label))),void 0!==t.suffix&&null!==t.suffix&&n.push(ir("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),n.concat(du(o.default))}())),!0===c.value&&!1===t.noErrorIcon&&n.push(O("error",[ir(Gu,{name:a.iconSet.field.error,color:"negative"})])),!0===t.loading||!0===e.innerLoading.value?n.push(O("inner-loading-append",void 0!==o.loading?o.loading():[ir(Lc,{color:t.color})])):!0===t.clearable&&!0===e.hasValue.value&&!0===e.editable.value&&n.push(O("inner-clearable-append",[ir(Gu,{class:"q-field__focusable-action",name:t.clearIcon||a.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":a.lang.label.clear,onKeyup:T,onClick:E})])),void 0!==o.append&&n.push(ir("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:za},o.append())),void 0!==e.getInnerAppend&&n.push(O("inner-append",e.getInnerAppend())),void 0!==e.getControlChild&&n.push(e.getControlChild()),n}function q(){let n,l;!0===c.value?null!==d.value?(n=[ir("div",{role:"alert"},d.value)],l=`q--slot-error-${d.value}`):(n=du(o.error),l="q--slot-error"):!0===t.hideHint&&!0!==e.focused.value||(void 0!==t.hint?(n=[ir("div",t.hint)],l=`q--slot-hint-${t.hint}`):(n=du(o.hint),l="q--slot-hint"));const r=!0===t.counter||void 0!==o.counter;if(!0===t.hideBottomSpace&&!1===r&&void 0===n)return;const a=ir("div",{key:l,class:"q-field__messages col"},n);return ir("div",{class:"q-field__bottom row items-start q-field__bottom--"+(!0!==t.hideBottomSpace?"animated":"stale"),onClick:za},[!0===t.hideBottomSpace?a:ir(yr,{name:"q-transition--field-message"},(()=>a)),!0===r?ir("div",{class:"q-field__counter"},void 0!==o.counter?o.counter():e.computedCounter.value):null])}function O(e,t){return null===t?null:ir("div",{key:e,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},t)}let P=!1;return Bn((()=>{P=!0})),$n((()=>{!0===P&&!0===t.autofocus&&r.focus()})),!0===t.autofocus&&Wn((()=>{r.focus()})),Gn((()=>{null!==i&&clearTimeout(i)})),Object.assign(r,{focus:x,blur:function(){var t;t=k,zc=zc.filter((e=>e!==t));const n=document.activeElement;null!==n&&e.rootRef.value.contains(n)&&n.blur()}}),function(){const n=void 0===e.getControl&&void 0===o.control?{...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0,...w.value}:w.value;return ir(e.tag.value,{ref:e.rootRef,class:[m.value,l.class],style:l.style,...n},[void 0!==o.before?ir("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:za},o.before()):null,ir("div",{class:"q-field__inner relative-position col self-stretch"},[ir("div",{ref:e.controlRef,class:g.value,tabindex:-1,...e.controlEvents},L()),!0===v.value?q():null]),void 0!==o.after?ir("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:za},o.after()):null])}}var Jc=Va({name:"QField",inheritAttrs:!1,props:{...Kc,tag:{type:String,default:"label"}},emits:Qc,setup:()=>Yc(Gc({tagProp:!0}))});function Zc(e,t=250){let n,o=!1;return function(){return!1===o&&(o=!0,setTimeout((()=>{o=!1}),t),n=e.apply(this,arguments)),n}}function Xc(e,t,n,o){!0===n.modifiers.stop&&Ba(e);const l=n.modifiers.color;let r=n.modifiers.center;r=!0===r||!0===o;const a=document.createElement("span"),i=document.createElement("span"),s=$a(e),{left:u,top:c,width:d,height:f}=t.getBoundingClientRect(),p=Math.sqrt(d*d+f*f),v=p/2,h=(d-p)/2+"px",m=r?h:s.left-u-v+"px",g=(f-p)/2+"px",b=r?g:s.top-c-v+"px";i.className="q-ripple__inner",gu(i,{height:`${p}px`,width:`${p}px`,transform:`translate3d(${m},${b},0) scale3d(.2,.2,1)`,opacity:0}),a.className="q-ripple"+(l?" text-"+l:""),a.setAttribute("dir","ltr"),a.appendChild(i),t.appendChild(a);const y=()=>{a.remove(),clearTimeout(_)};n.abort.push(y);let _=setTimeout((()=>{i.classList.add("q-ripple__inner--enter"),i.style.transform=`translate3d(${h},${g},0) scale3d(1,1,1)`,i.style.opacity=.2,_=setTimeout((()=>{i.classList.remove("q-ripple__inner--enter"),i.classList.add("q-ripple__inner--leave"),i.style.opacity=0,_=setTimeout((()=>{a.remove(),n.abort.splice(n.abort.indexOf(y),1)}),275)}),250)}),50)}function ed(e,{modifiers:t,value:n,arg:o}){const l=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:!0===l.early,stop:!0===l.stop,center:!0===l.center,color:l.color||o,keyCodes:[].concat(l.keyCodes||13)}}var td=wt({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(!1===n.ripple)return;const o={cfg:n,enabled:!1!==t.value,modifiers:{},abort:[],start(t){!0===o.enabled&&!0!==t.qSkipRipple&&t.type===(!0===o.modifiers.early?"pointerdown":"click")&&Xc(t,e,o,!0===t.qKeyEvent)},keystart:Zc((t=>{!0===o.enabled&&!0!==t.qSkipRipple&&!0===ei(t,o.modifiers.keyCodes)&&t.type==="key"+(!0===o.modifiers.early?"down":"up")&&Xc(t,e,o,!0)}),300)};ed(o,t),e.__qripple=o,ja(o,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;void 0!==n&&(n.enabled=!1!==t.value,!0===n.enabled&&Object(t.value)===t.value&&ed(n,t))}},beforeUnmount(e){const t=e.__qripple;void 0!==t&&(t.abort.forEach((e=>{e()})),Ua(t,"main"),delete e._qripple)}});const nd={xs:8,sm:10,md:14,lg:20,xl:24};var od=Va({name:"QChip",props:{...Yu,...Ru,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:t,emit:n}){const{proxy:{$q:o}}=Ql(),l=Ju(e,o),r=Au(e,nd),a=ar((()=>!0===e.selected||void 0!==e.icon)),i=ar((()=>!0===e.selected?e.iconSelected||o.iconSet.chip.selected:e.icon)),s=ar((()=>e.iconRemove||o.iconSet.chip.remove)),u=ar((()=>!1===e.disable&&(!0===e.clickable||null!==e.selected))),c=ar((()=>{const t=!0===e.outline&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(!1===e.outline&&void 0!==e.color?` bg-${e.color}`:"")+(t?` text-${t} q-chip--colored`:"")+(!0===e.disable?" disabled":"")+(!0===e.dense?" q-chip--dense":"")+(!0===e.outline?" q-chip--outline":"")+(!0===e.selected?" q-chip--selected":"")+(!0===u.value?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(!0===e.square?" q-chip--square":"")+(!0===l.value?" q-chip--dark q-dark":"")})),d=ar((()=>{const t=!0===e.disable?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0};return{chip:t,remove:{...t,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||o.lang.label.remove}}}));function f(e){13===e.keyCode&&p(e)}function p(t){e.disable||(n("update:selected",!e.selected),n("click",t))}function v(t){void 0!==t.keyCode&&13!==t.keyCode||(Da(t),!1===e.disable&&(n("update:modelValue",!1),n("remove")))}return()=>{if(!1===e.modelValue)return;const n={class:c.value,style:r.value};return!0===u.value&&Object.assign(n,d.value.chip,{onClick:p,onKeyup:f}),function(e,t,n,o,l,r){t.key=o+l;const a=ir(e,t,n);return!0===l?an(a,r()):a}("div",n,function(){const n=[];!0===u.value&&n.push(ir("div",{class:"q-focus-helper"})),!0===a.value&&n.push(ir(Gu,{class:"q-chip__icon q-chip__icon--left",name:i.value}));const o=void 0!==e.label?[ir("div",{class:"ellipsis"},[e.label])]:void 0;var l,r;return n.push(ir("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},(l=t.default,r=o,void 0===l?r:void 0!==r?r.concat(l()):l()))),e.iconRight&&n.push(ir(Gu,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),!0===e.removable&&n.push(ir(Gu,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:s.value,...d.value.remove,onClick:v,onKeyup:v})),n}(),"ripple",!1!==e.ripple&&!0!==e.disable,(()=>[[td,e.ripple]]))}}});const ld={...{target:{type:[Boolean,String,Element],default:!0},noParentEvent:Boolean},contextMenu:Boolean};function rd({showing:e,avoidEmit:t,configureAnchorEl:n}){const{props:o,proxy:l,emit:r}=Ql(),a=Ct(null);let i=null;function s(e){return null!==a.value&&(void 0===e||void 0===e.touches||e.touches.length<=1)}const u={};function c(){Ua(u,"anchor")}function d(){if(!1===o.target||""===o.target||null===l.$el.parentNode)a.value=null;else if(!0===o.target)!function(e){for(a.value=e;a.value.classList.contains("q-anchor--skip");)a.value=a.value.parentNode;n()}(l.$el.parentNode);else{let t=o.target;if("string"==typeof o.target)try{t=document.querySelector(o.target)}catch(e){t=void 0}null!=t?(a.value=t.$el||t,n()):(a.value=null,console.error(`Anchor: target "${o.target}" not found`))}}return void 0===n&&(Object.assign(u,{hide(e){l.hide(e)},toggle(e){l.toggle(e),e.qAnchorHandled=!0},toggleKey(e){!0===ei(e,13)&&u.toggle(e)},contextClick(e){l.hide(e),za(e),Qt((()=>{l.show(e),e.qAnchorHandled=!0}))},prevent:za,mobileTouch(e){if(u.mobileCleanup(e),!0!==s(e))return;l.hide(e),a.value.classList.add("non-selectable");const t=e.target;ja(u,"anchor",[[t,"touchmove","mobileCleanup","passive"],[t,"touchend","mobileCleanup","passive"],[t,"touchcancel","mobileCleanup","passive"],[a.value,"contextmenu","prevent","notPassive"]]),i=setTimeout((()=>{i=null,l.show(e),e.qAnchorHandled=!0}),300)},mobileCleanup(t){a.value.classList.remove("non-selectable"),null!==i&&(clearTimeout(i),i=null),!0===e.value&&void 0!==t&&function(){if(void 0!==window.getSelection){const e=window.getSelection();void 0!==e.empty?e.empty():void 0!==e.removeAllRanges&&(e.removeAllRanges(),!0!==Aa.is.mobile&&e.addRange(document.createRange()))}else void 0!==document.selection&&document.selection.empty()}()}}),n=function(e=o.contextMenu){if(!0===o.noParentEvent||null===a.value)return;let t;t=!0===e?!0===l.$q.platform.is.mobile?[[a.value,"touchstart","mobileTouch","passive"]]:[[a.value,"mousedown","hide","passive"],[a.value,"contextmenu","contextClick","notPassive"]]:[[a.value,"click","toggle","passive"],[a.value,"keyup","toggleKey","passive"]],ja(u,"anchor",t)}),Xo((()=>o.contextMenu),(e=>{null!==a.value&&(c(),n(e))})),Xo((()=>o.target),(()=>{null!==a.value&&c(),d()})),Xo((()=>o.noParentEvent),(e=>{null!==a.value&&(!0===e?c():n())})),Wn((()=>{d(),!0!==t&&!0===o.modelValue&&null===a.value&&r("update:modelValue",!1)})),Gn((()=>{null!==i&&clearTimeout(i),c()})),{anchorEl:a,canShow:s,anchorEvents:u}}const ad={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},id=["beforeShow","show","beforeHide","hide"];function sd({showing:e,canShow:t,hideOnRouteChange:n,handleShow:o,handleHide:l,processOnMount:r}){const a=Ql(),{props:i,emit:s,proxy:u}=a;let c;function d(e){if(!0===i.disable||!0===(null==e?void 0:e.qAnchorHandled)||void 0!==t&&!0!==t(e))return;const n=void 0!==i["onUpdate:modelValue"];!0===n&&(s("update:modelValue",!0),c=e,Qt((()=>{c===e&&(c=void 0)}))),null!==i.modelValue&&!1!==n||f(e)}function f(t){!0!==e.value&&(e.value=!0,s("beforeShow",t),void 0!==o?o(t):s("show",t))}function p(e){if(!0===i.disable)return;const t=void 0!==i["onUpdate:modelValue"];!0===t&&(s("update:modelValue",!1),c=e,Qt((()=>{c===e&&(c=void 0)}))),null!==i.modelValue&&!1!==t||v(e)}function v(t){!1!==e.value&&(e.value=!1,s("beforeHide",t),void 0!==l?l(t):s("hide",t))}function h(t){if(!0===i.disable&&!0===t)void 0!==i["onUpdate:modelValue"]&&s("update:modelValue",!1);else if(!0===t!==e.value){(!0===t?f:v)(c)}}Xo((()=>i.modelValue),h),void 0!==n&&!0===mc(a)&&Xo((()=>u.$route.fullPath),(()=>{!0===n.value&&!0===e.value&&p()})),!0===r&&Wn((()=>{h(i.modelValue)}));const m={show:d,hide:p,toggle:function(t){!0===e.value?p(t):d(t)}};return Object.assign(u,m),m}let ud=1,cd=document.body;const dd=[];const fd=Va({name:"QPortal",setup:(e,{slots:t})=>()=>t.default()});function pd(e,t,n,o){const l=Ct(!1),r=Ct(!1);let a=null;const i={},s="dialog"===o&&function(e){for(e=e.parent;null!=e;){if("QGlobalDialog"===e.type.name)return!0;if("QDialog"===e.type.name||"QMenu"===e.type.name)return!1;e=e.parent}return!1}(e);function u(t){if(r.value=!1,!0!==t)return;Uc(i),l.value=!1;const n=dd.indexOf(e.proxy);-1!==n&&dd.splice(n,1),null!==a&&(a.remove(),a=null)}return Yn((()=>{u(!0)})),e.proxy.__qPortal=!0,Ta(e.proxy,"contentEl",(()=>t.value)),{showPortal:function(t){if(!0===t)return Uc(i),void(r.value=!0);var n;r.value=!1,!1===l.value&&(!1===s&&null===a&&(a=function(e,t){const n=document.createElement("div");if(n.id=void 0!==t?`q-portal--${t}--${ud++}`:e,void 0!==pi.globalNodes){const e=pi.globalNodes.class;void 0!==e&&(n.className=e)}return cd.appendChild(n),n}(!1,o)),l.value=!0,dd.push(e.proxy),jc(n=i),Dc.push(n))},hidePortal:u,portalIsActive:l,portalIsAccessible:r,renderPortal:()=>!0===s?n():!0===l.value?[ir(bn,{to:a},ir(fd,n))]:void 0}}const vd={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function hd(e,t=(()=>{}),n=(()=>{})){return{transitionProps:ar((()=>{const o=`q-transition--${e.transitionShow||t()}`,l=`q-transition--${e.transitionHide||n()}`;return{appear:!0,enterFromClass:`${o}-enter-from`,enterActiveClass:`${o}-enter-active`,enterToClass:`${o}-enter-to`,leaveFromClass:`${l}-leave-from`,leaveActiveClass:`${l}-leave-active`,leaveToClass:`${l}-leave-to`}})),transitionStyle:ar((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}function md(){let e;const t=Ql();function n(){e=void 0}return Bn(n),Gn(n),{removeTick:n,registerTick(n){e=n,Qt((()=>{e===n&&(!1===gc(t)&&e(),e=void 0)}))}}}function gd(){let e=null;const t=Ql();function n(){null!==e&&(clearTimeout(e),e=null)}return Bn(n),Gn(n),{removeTimeout:n,registerTimeout(o,l){n(),!1===gc(t)&&(e=setTimeout((()=>{e=null,o()}),l))}}}const bd=[];let yd;function _d(e){yd=27===e.keyCode}function wd(){!0===yd&&(yd=!1)}function kd(e){!0===yd&&(yd=!1,!0===ei(e,27)&&bd[bd.length-1](e))}function xd(e){window[e]("keydown",_d),window[e]("blur",wd),window[e]("keyup",kd),yd=!1}function Sd(e){!0===Ra.is.desktop&&(bd.push(e),1===bd.length&&xd("addEventListener"))}function Cd(e){const t=bd.indexOf(e);-1!==t&&(bd.splice(t,1),0===bd.length&&xd("removeEventListener"))}const Ed=[];function Td(e){Ed[Ed.length-1](e)}function Ld(e){!0===Ra.is.desktop&&(Ed.push(e),1===Ed.length&&document.body.addEventListener("focusin",Td))}function qd(e){const t=Ed.indexOf(e);-1!==t&&(Ed.splice(t,1),0===Ed.length&&document.body.removeEventListener("focusin",Td))}const{notPassiveCapture:Od}=Ia,Pd=[];function Fd(e){const t=e.target;if(void 0===t||8===t.nodeType||!0===t.classList.contains("no-pointer-events"))return;let n=dd.length-1;for(;n>=0;){const e=dd[n].$;if("QTooltip"!==e.type.name){if("QDialog"!==e.type.name)break;if(!0!==e.props.seamless)return;n--}else n--}for(let o=Pd.length-1;o>=0;o--){const n=Pd[o];if(null!==n.anchorEl.value&&!1!==n.anchorEl.value.contains(t)||t!==document.body&&(null===n.innerRef.value||!1!==n.innerRef.value.contains(t)))return;e.qClickOutside=!0,n.onClickOutside(e)}}function Rd(e){const t=Pd.findIndex((t=>t===e));-1!==t&&(Pd.splice(t,1),0===Pd.length&&(document.removeEventListener("mousedown",Fd,Od),document.removeEventListener("touchstart",Fd,Od)))}let Ad,Vd;function Md(e){const t=e.split(" ");return 2===t.length&&(!0!==["top","center","bottom"].includes(t[0])?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):!0===["left","middle","right","start","end"].includes(t[1])||(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1))}const Id={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};function Nd(e,t){const n=e.split(" ");return{vertical:n[0],horizontal:Id[`${n[1]}#${!0===t?"rtl":"ltr"}`]}}function $d(e,t,n,o){return{top:e[n.vertical]-t[o.vertical],left:e[n.horizontal]-t[o.horizontal]}}function Bd(e,t=0){if(null===e.targetEl||null===e.anchorEl||t>5)return;if(0===e.targetEl.offsetHeight||0===e.targetEl.offsetWidth)return void setTimeout((()=>{Bd(e,t+1)}),10);const{targetEl:n,offset:o,anchorEl:l,anchorOrigin:r,selfOrigin:a,absoluteOffset:i,fit:s,cover:u,maxHeight:c,maxWidth:d}=e;if(!0===Ra.is.ios&&void 0!==window.visualViewport){const e=document.body.style,{offsetLeft:t,offsetTop:n}=window.visualViewport;t!==Ad&&(e.setProperty("--q-pe-left",t+"px"),Ad=t),n!==Vd&&(e.setProperty("--q-pe-top",n+"px"),Vd=n)}const{scrollLeft:f,scrollTop:p}=n,v=void 0===i?function(e,t){let{top:n,left:o,right:l,bottom:r,width:a,height:i}=e.getBoundingClientRect();return void 0!==t&&(n-=t[1],o-=t[0],r+=t[1],l+=t[0],a+=t[0],i+=t[1]),{top:n,bottom:r,height:i,left:o,right:l,width:a,middle:o+(l-o)/2,center:n+(r-n)/2}}(l,!0===u?[0,0]:o):function(e,t,n){let{top:o,left:l}=e.getBoundingClientRect();return o+=t.top,l+=t.left,void 0!==n&&(o+=n[1],l+=n[0]),{top:o,bottom:o+1,height:1,left:l,right:l+1,width:1,middle:l,center:o}}(l,i,o);Object.assign(n.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:d,maxHeight:c,visibility:"visible"});const{offsetWidth:h,offsetHeight:m}=n,{elWidth:g,elHeight:b}=!0===s||!0===u?{elWidth:Math.max(v.width,h),elHeight:!0===u?Math.max(v.height,m):m}:{elWidth:h,elHeight:m};let y={maxWidth:d,maxHeight:c};!0!==s&&!0!==u||(y.minWidth=v.width+"px",!0===u&&(y.minHeight=v.height+"px")),Object.assign(n.style,y);const _={top:0,center:(k=b)/2,bottom:k,left:0,middle:(w=g)/2,right:w};var w,k;let x=$d(v,_,r,a);if(void 0===i||void 0===o)zd(x,v,_,r,a);else{const{top:e,left:t}=x;zd(x,v,_,r,a);let n=!1;if(x.top!==e){n=!0;const e=2*o[1];v.center=v.top-=e,v.bottom-=e+2}if(x.left!==t){n=!0;const e=2*o[0];v.middle=v.left-=e,v.right-=e+2}!0===n&&(x=$d(v,_,r,a),zd(x,v,_,r,a))}y={top:x.top+"px",left:x.left+"px"},void 0!==x.maxHeight&&(y.maxHeight=x.maxHeight+"px",v.height>x.maxHeight&&(y.minHeight=y.maxHeight)),void 0!==x.maxWidth&&(y.maxWidth=x.maxWidth+"px",v.width>x.maxWidth&&(y.minWidth=y.maxWidth)),Object.assign(n.style,y),n.scrollTop!==p&&(n.scrollTop=p),n.scrollLeft!==f&&(n.scrollLeft=f)}function zd(e,t,n,o,l){const r=n.bottom,a=n.right,i=Cu(),s=window.innerHeight-i,u=document.body.clientWidth;if(e.top<0||e.top+r>s)if("center"===l.vertical)e.top=t[o.vertical]>s/2?Math.max(0,s-r):0,e.maxHeight=Math.min(r,s);else if(t[o.vertical]>s/2){const n=Math.min(s,"center"===o.vertical?t.center:o.vertical===l.vertical?t.bottom:t.top);e.maxHeight=Math.min(r,n),e.top=Math.max(0,n-r)}else e.top=Math.max(0,"center"===o.vertical?t.center:o.vertical===l.vertical?t.top:t.bottom),e.maxHeight=Math.min(r,s-e.top);if(e.left<0||e.left+a>u)if(e.maxWidth=Math.min(a,u),"middle"===l.horizontal)e.left=t[o.horizontal]>u/2?Math.max(0,u-a):0;else if(t[o.horizontal]>u/2){const n=Math.min(u,"middle"===o.horizontal?t.middle:o.horizontal===l.horizontal?t.right:t.left);e.maxWidth=Math.min(a,n),e.left=Math.max(0,n-e.maxWidth)}else e.left=Math.max(0,"middle"===o.horizontal?t.middle:o.horizontal===l.horizontal?t.left:t.right),e.maxWidth=Math.min(a,u-e.left)}["left","middle","right"].forEach((e=>{Id[`${e}#ltr`]=e,Id[`${e}#rtl`]=e}));var Dd=Va({name:"QMenu",inheritAttrs:!1,props:{...ld,...ad,...Yu,...vd,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noEscDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:Md},self:{type:String,validator:Md},offset:{type:Array,validator:function(e){return!e||2===e.length&&("number"==typeof e[0]&&"number"==typeof e[1])}},scrollTarget:yu,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...id,"click","escapeKey"],setup(e,{slots:t,emit:n,attrs:o}){let l,r,a,i=null;const s=Ql(),{proxy:u}=s,{$q:c}=u,d=Ct(null),f=Ct(!1),p=ar((()=>!0!==e.persistent&&!0!==e.noRouteDismiss)),v=Ju(e,c),{registerTick:h,removeTick:m}=md(),{registerTimeout:g}=gd(),{transitionProps:b,transitionStyle:y}=hd(e),{localScrollTarget:_,changeScrollEvent:w,unconfigureScrollTarget:k}=function(e,t){const n=Ct(null);let o;function l(e,t){const n=(void 0!==t?"add":"remove")+"EventListener",l=void 0!==t?t:o;e!==window&&e[n]("scroll",l,Ia.passive),window[n]("scroll",l,Ia.passive),o=t}function r(){null!==n.value&&(l(n.value),n.value=null)}const a=Xo((()=>e.noParentEvent),(()=>{null!==n.value&&(r(),t())}));return Gn(a),{localScrollTarget:n,unconfigureScrollTarget:r,changeScrollEvent:l}}(e,I),{anchorEl:x,canShow:S}=rd({showing:f}),{hide:C}=sd({showing:f,canShow:S,handleShow:function(t){if(i=!1===e.noRefocus?document.activeElement:null,Ld($),E(),I(),l=void 0,void 0!==t&&(e.touchPosition||e.contextMenu)){const e=$a(t);if(void 0!==e.left){const{top:t,left:n}=x.value.getBoundingClientRect();l={left:e.left-n,top:e.top-t}}}void 0===r&&(r=Xo((()=>c.screen.width+"|"+c.screen.height+"|"+e.self+"|"+e.anchor+"|"+c.lang.rtl),z));!0!==e.noFocus&&document.activeElement.blur();h((()=>{z(),!0!==e.noFocus&&V()})),g((()=>{!0===c.platform.is.ios&&(a=e.autoClose,d.value.click()),z(),E(!0),n("show",t)}),e.transitionDuration)},handleHide:function(t){m(),T(),M(!0),null===i||void 0!==t&&!0===t.qClickOutside||(((0===(null==t?void 0:t.type.indexOf("key"))?i.closest('[tabindex]:not([tabindex^="-"])'):void 0)||i).focus(),i=null);g((()=>{T(!0),n("hide",t)}),e.transitionDuration)},hideOnRouteChange:p,processOnMount:!0}),{showPortal:E,hidePortal:T,renderPortal:L}=pd(s,d,(function(){return ir(yr,b.value,(()=>!0===f.value?ir("div",{role:"menu",...o,ref:d,tabindex:-1,class:["q-menu q-position-engine scroll"+F.value,o.class],style:[o.style,y.value],...R.value},du(t.default)):null))}),"menu"),q={anchorEl:x,innerRef:d,onClickOutside(t){if(!0!==e.persistent&&!0===f.value)return C(t),("touchstart"===t.type||t.target.classList.contains("q-dialog__backdrop"))&&Da(t),!0}},O=ar((()=>Nd(e.anchor||(!0===e.cover?"center middle":"bottom start"),c.lang.rtl))),P=ar((()=>!0===e.cover?O.value:Nd(e.self||"top start",c.lang.rtl))),F=ar((()=>(!0===e.square?" q-menu--square":"")+(!0===v.value?" q-menu--dark q-dark":""))),R=ar((()=>!0===e.autoClose?{onClick:N}:{})),A=ar((()=>!0===f.value&&!0!==e.persistent));function V(){Hc((()=>{let e=d.value;e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||e.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||e.querySelector("[autofocus], [data-autofocus]")||e,e.focus({preventScroll:!0}))}))}function M(e){l=void 0,void 0!==r&&(r(),r=void 0),!0!==e&&!0!==f.value||(qd($),k(),Rd(q),Cd(B)),!0!==e&&(i=null)}function I(){null===x.value&&void 0===e.scrollTarget||(_.value=wu(x.value,e.scrollTarget),w(_.value,z))}function N(e){!0!==a?(!function(e,t){do{if("QMenu"===e.$options.name){if(e.hide(t),!0===e.$props.separateClosePopup)return hc(e)}else if(!0===e.__qPortal){const n=hc(e);return"QPopupProxy"===(null==n?void 0:n.$options.name)?(e.hide(t),n):e}e=hc(e)}while(null!=e)}(u,e),n("click",e)):a=!1}function $(t){!0===A.value&&!0!==e.noFocus&&!0!==bu(d.value,t.target)&&V()}function B(t){!0!==e.noEscDismiss&&(n("escapeKey"),C(t))}function z(){Bd({targetEl:d.value,offset:e.offset,anchorEl:x.value,anchorOrigin:O.value,selfOrigin:P.value,absoluteOffset:l,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}return Xo(A,(e=>{!0===e?(Sd(B),function(e){Pd.push(e),1===Pd.length&&(document.addEventListener("mousedown",Fd,Od),document.addEventListener("touchstart",Fd,Od))}(q)):(Cd(B),Rd(q))})),Gn(M),Object.assign(u,{focus:V,updatePosition:z}),L}});let jd,Ud,Hd,Wd,Kd,Qd,Gd=0,Yd=!1,Jd=null;function Zd(e){(function(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=function(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),"HTML"===n.tagName)return t.push(document),t.push(window),t;n=n.parentElement}}(e),n=e.shiftKey&&!e.deltaX,o=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),l=n||o?e.deltaY:e.deltaX;for(let r=0;r<t.length;r++){const e=t[r];if(Eu(e,o))return o?l<0&&0===e.scrollTop||l>0&&e.scrollTop+e.clientHeight===e.scrollHeight:l<0&&0===e.scrollLeft||l>0&&e.scrollLeft+e.clientWidth===e.scrollWidth}return!0})(e)&&Da(e)}function Xd(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function ef(e){!0!==Yd&&(Yd=!0,requestAnimationFrame((()=>{Yd=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:o}=document.scrollingElement;void 0!==Hd&&t===window.innerHeight||(Hd=n-t,document.scrollingElement.scrollTop=o),o>Hd&&(document.scrollingElement.scrollTop-=Math.ceil((o-Hd)/8))})))}function tf(e){const t=document.body,n=void 0!==window.visualViewport;if("add"===e){const{overflowY:e,overflowX:o}=window.getComputedStyle(t);jd=xu(window),Ud=ku(window),Wd=t.style.left,Kd=t.style.top,Qd=window.location.href,t.style.left=`-${jd}px`,t.style.top=`-${Ud}px`,"hidden"!==o&&("scroll"===o||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),"hidden"!==e&&("scroll"===e||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,!0===Ra.is.ios&&(!0===n?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",ef,Ia.passiveCapture),window.visualViewport.addEventListener("scroll",ef,Ia.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Xd,Ia.passiveCapture))}!0===Ra.is.desktop&&!0===Ra.is.mac&&window[`${e}EventListener`]("wheel",Zd,Ia.notPassive),"remove"===e&&(!0===Ra.is.ios&&(!0===n?(window.visualViewport.removeEventListener("resize",ef,Ia.passiveCapture),window.visualViewport.removeEventListener("scroll",ef,Ia.passiveCapture)):window.removeEventListener("scroll",Xd,Ia.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=Wd,t.style.top=Kd,window.location.href===Qd&&window.scrollTo(jd,Ud),Hd=void 0)}function nf(){let e;return{preventBodyScroll(t){t===e||void 0===e&&!0!==t||(e=t,function(e){let t="add";if(!0===e){if(Gd++,null!==Jd)return clearTimeout(Jd),void(Jd=null);if(Gd>1)return}else{if(0===Gd)return;if(Gd--,Gd>0)return;if(t="remove",!0===Ra.is.ios&&!0===Ra.is.nativeMobile)return null!==Jd&&clearTimeout(Jd),void(Jd=setTimeout((()=>{tf(t),Jd=null}),100))}tf(t)}(t))}}}let of=0;const lf={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},rf={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]};var af=Va({name:"QDialog",inheritAttrs:!1,props:{...ad,...vd,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...id,"shake","click","escapeKey"],setup(e,{slots:t,emit:n,attrs:o}){const l=Ql(),r=Ct(null),a=Ct(!1),i=Ct(!1);let s,u,c=null,d=null;const f=ar((()=>!0!==e.persistent&&!0!==e.noRouteDismiss&&!0!==e.seamless)),{preventBodyScroll:p}=nf(),{registerTimeout:v}=gd(),{registerTick:h,removeTick:m}=md(),{transitionProps:g,transitionStyle:b}=hd(e,(()=>rf[e.position][0]),(()=>rf[e.position][1])),y=ar((()=>b.value+(void 0!==e.backdropFilter?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:""))),{showPortal:_,hidePortal:w,portalIsAccessible:k,renderPortal:x}=pd(l,r,(function(){return ir("div",{role:"dialog","aria-modal":!0===L.value?"true":"false",...o,class:O.value},[ir(yr,{name:"q-transition--fade",appear:!0},(()=>!0===L.value?ir("div",{class:"q-dialog__backdrop fixed-full",style:y.value,"aria-hidden":"true",tabindex:-1,onClick:I}):null)),ir(yr,g.value,(()=>!0===a.value?ir("div",{ref:r,class:T.value,style:b.value,tabindex:-1,...q.value},du(t.default)):null))])}),"dialog"),{hide:S}=sd({showing:a,hideOnRouteChange:f,handleShow:function(t){var o;C(),d=!1===e.noRefocus&&null!==document.activeElement?document.activeElement:null,V(e.maximized),_(),i.value=!0,!0!==e.noFocus?(null==(o=document.activeElement)||o.blur(),h(P)):m();v((()=>{if(!0===l.proxy.$q.platform.is.ios){if(!0!==e.seamless&&document.activeElement){const{top:e,bottom:t}=document.activeElement.getBoundingClientRect(),{innerHeight:n}=window,o=void 0!==window.visualViewport?window.visualViewport.height:n;e>0&&t>o/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-o,t>=n?1/0:Math.ceil(document.scrollingElement.scrollTop+t-o/2))),document.activeElement.scrollIntoView()}u=!0,r.value.click(),u=!1}_(!0),i.value=!1,n("show",t)}),e.transitionDuration)},handleHide:function(t){m(),E(),A(!0),i.value=!0,w(),null!==d&&(((0===(null==t?void 0:t.type.indexOf("key"))?d.closest('[tabindex]:not([tabindex^="-"])'):void 0)||d).focus(),d=null);v((()=>{w(!0),i.value=!1,n("hide",t)}),e.transitionDuration)},processOnMount:!0}),{addToHistory:C,removeFromHistory:E}=function(e,t,n){let o;function l(){void 0!==o&&(ai.remove(o),o=void 0)}return Gn((()=>{!0===e.value&&l()})),{removeFromHistory:l,addToHistory(){o={condition:()=>!0===n.value,handler:t},ai.add(o)}}}(a,S,f),T=ar((()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${!0===e.maximized?"maximized":"minimized"} q-dialog__inner--${e.position} ${lf[e.position]}`+(!0===i.value?" q-dialog__inner--animating":"")+(!0===e.fullWidth?" q-dialog__inner--fullwidth":"")+(!0===e.fullHeight?" q-dialog__inner--fullheight":"")+(!0===e.square?" q-dialog__inner--square":""))),L=ar((()=>!0===a.value&&!0!==e.seamless)),q=ar((()=>!0===e.autoClose?{onClick:M}:{})),O=ar((()=>["q-dialog fullscreen no-pointer-events q-dialog--"+(!0===L.value?"modal":"seamless"),o.class]));function P(e){Hc((()=>{let t=r.value;if(null!==t){if(void 0!==e){const n=t.querySelector(e);if(null!==n)return void n.focus({preventScroll:!0})}!0!==t.contains(document.activeElement)&&(t=t.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||t.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||t.querySelector("[autofocus], [data-autofocus]")||t,t.focus({preventScroll:!0}))}}))}function F(e){e&&"function"==typeof e.focus?e.focus({preventScroll:!0}):P(),n("shake");const t=r.value;null!==t&&(t.classList.remove("q-animate--scale"),t.classList.add("q-animate--scale"),null!==c&&clearTimeout(c),c=setTimeout((()=>{c=null,null!==r.value&&(t.classList.remove("q-animate--scale"),P())}),170))}function R(){!0!==e.seamless&&(!0===e.persistent||!0===e.noEscDismiss?!0!==e.maximized&&!0!==e.noShake&&F():(n("escapeKey"),S()))}function A(t){null!==c&&(clearTimeout(c),c=null),!0!==t&&!0!==a.value||(V(!1),!0!==e.seamless&&(p(!1),qd(N),Cd(R))),!0!==t&&(d=null)}function V(e){!0===e?!0!==s&&(of<1&&document.body.classList.add("q-body--dialog"),of++,s=!0):!0===s&&(of<2&&document.body.classList.remove("q-body--dialog"),of--,s=!1)}function M(e){!0!==u&&(S(e),n("click",e))}function I(t){!0!==e.persistent&&!0!==e.noBackdropDismiss?S(t):!0!==e.noShake&&F()}function N(t){!0!==e.allowFocusOutside&&!0===k.value&&!0!==bu(r.value,t.target)&&P('[tabindex]:not([tabindex="-1"])')}return Xo((()=>e.maximized),(e=>{!0===a.value&&V(e)})),Xo(L,(e=>{p(e),!0===e?(Ld(N),Sd(R)):(qd(N),Cd(R))})),Object.assign(l.proxy,{focus:P,shake:F,__updateRefocusTarget(e){d=e||null}}),Gn(A),x}});let sf=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const t=document.createElement("div");Object.assign(t.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,sf=e.scrollLeft>=0,e.remove()}const uf=["start","center","end","start-force","center-force","end-force"],cf=Array.prototype.filter,df=void 0===window.getComputedStyle(document.body).overflowAnchor?Na:function(e,t){null!==e&&(void 0!==e._qOverflowAnimationFrame&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame((()=>{if(null===e)return;e._qOverflowAnimationFrame=void 0;const n=e.children||[];cf.call(n,(e=>e.dataset&&void 0!==e.dataset.qVsAnchor)).forEach((e=>{delete e.dataset.qVsAnchor}));const o=n[t];(null==o?void 0:o.dataset)&&(o.dataset.qVsAnchor="")})))};function ff(e,t){return e+t}function pf(e,t,n,o,l,r,a,i){const s=e===window?document.scrollingElement||document.documentElement:e,u=!0===l?"offsetWidth":"offsetHeight",c={scrollStart:0,scrollViewSize:-a-i,scrollMaxSize:0,offsetStart:-a,offsetEnd:-i};if(!0===l?(e===window?(c.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,c.scrollViewSize+=document.documentElement.clientWidth):(c.scrollStart=s.scrollLeft,c.scrollViewSize+=s.clientWidth),c.scrollMaxSize=s.scrollWidth,!0===r&&(c.scrollStart=(!0===sf?c.scrollMaxSize-c.scrollViewSize:0)-c.scrollStart)):(e===window?(c.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,c.scrollViewSize+=document.documentElement.clientHeight):(c.scrollStart=s.scrollTop,c.scrollViewSize+=s.clientHeight),c.scrollMaxSize=s.scrollHeight),null!==n)for(let d=n.previousElementSibling;null!==d;d=d.previousElementSibling)!1===d.classList.contains("q-virtual-scroll--skip")&&(c.offsetStart+=d[u]);if(null!==o)for(let d=o.nextElementSibling;null!==d;d=d.nextElementSibling)!1===d.classList.contains("q-virtual-scroll--skip")&&(c.offsetEnd+=d[u]);if(t!==e){const n=s.getBoundingClientRect(),o=t.getBoundingClientRect();!0===l?(c.offsetStart+=o.left-n.left,c.offsetEnd-=o.width):(c.offsetStart+=o.top-n.top,c.offsetEnd-=o.height),e!==window&&(c.offsetStart+=c.scrollStart),c.offsetEnd+=c.scrollMaxSize-c.offsetStart}return c}function vf(e,t,n,o){"end"===t&&(t=(e===window?document.body:e)[!0===n?"scrollWidth":"scrollHeight"]),e===window?!0===n?(!0===o&&(t=(!0===sf?document.body.scrollWidth-document.documentElement.clientWidth:0)-t),window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t):!0===n?(!0===o&&(t=(!0===sf?e.scrollWidth-e.offsetWidth:0)-t),e.scrollLeft=t):e.scrollTop=t}function hf(e,t,n,o){if(n>=o)return 0;const l=t.length,r=Math.floor(n/1e3),a=Math.floor((o-1)/1e3)+1;let i=e.slice(r,a).reduce(ff,0);return n%1e3!=0&&(i-=t.slice(1e3*r,n).reduce(ff,0)),o%1e3!=0&&o!==l&&(i-=t.slice(o,1e3*a).reduce(ff,0)),i}const mf={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},gf=Object.keys(mf),bf={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...mf};function yf({virtualScrollLength:e,getVirtualScrollTarget:t,getVirtualScrollEl:n,virtualScrollItemSizeComputed:o}){const l=Ql(),{props:r,emit:a,proxy:i}=l,{$q:s}=i;let u,c,d,f,p=[];const v=Ct(0),h=Ct(0),m=Ct({}),g=Ct(null),b=Ct(null),y=Ct(null),_=Ct({from:0,to:0}),w=ar((()=>void 0!==r.tableColspan?r.tableColspan:100));void 0===o&&(o=ar((()=>r.virtualScrollItemSize)));const k=ar((()=>o.value+";"+r.virtualScrollHorizontal));function x(){q(c,!0)}function S(e){q(void 0===e?c:e)}function C(o,l){const a=t();if(null==a||8===a.nodeType)return;const i=pf(a,n(),g.value,b.value,r.virtualScrollHorizontal,s.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd);d!==i.scrollViewSize&&O(i.scrollViewSize),E(a,i,Math.min(e.value-1,Math.max(0,parseInt(o,10)||0)),0,-1!==uf.indexOf(l)?l:-1!==c&&o>c?"end":"start")}function E(t,n,o,l,a){const i="string"==typeof a&&-1!==a.indexOf("-force"),c=!0===i?a.replace("-force",""):a,d=void 0!==c?c:"start";let g=Math.max(0,o-m.value[d]),b=g+m.value.total;b>e.value&&(b=e.value,g=Math.max(0,b-m.value.total)),u=n.scrollStart;const w=g!==_.value.from||b!==_.value.to;if(!1===w&&void 0===c)return void P(o);const{activeElement:k}=document,x=y.value;!0===w&&null!==x&&x!==k&&!0===x.contains(k)&&(x.addEventListener("focusout",L),setTimeout((()=>{null==x||x.removeEventListener("focusout",L)}))),df(x,o-g);const S=void 0!==c?f.slice(g,o).reduce(ff,0):0;if(!0===w){const t=b>=_.value.from&&g<=_.value.to?_.value.to:b;_.value={from:g,to:t},v.value=hf(p,f,0,g),h.value=hf(p,f,b,e.value),requestAnimationFrame((()=>{_.value.to!==b&&u===n.scrollStart&&(_.value={from:_.value.from,to:b},h.value=hf(p,f,b,e.value))}))}requestAnimationFrame((()=>{if(u!==n.scrollStart)return;!0===w&&T(g);const e=f.slice(g,o).reduce(ff,0),a=e+n.offsetStart+v.value,d=a+f[o];let p=a+l;if(void 0!==c){const t=e-S,l=n.scrollStart+t;p=!0!==i&&l<a&&d<l+n.scrollViewSize?l:"end"===c?d-n.scrollViewSize:a-("start"===c?0:Math.round((n.scrollViewSize-f[o])/2))}u=p,vf(t,p,r.virtualScrollHorizontal,s.lang.rtl),P(o)}))}function T(e){const t=y.value;if(t){const n=cf.call(t.children,(e=>e.classList&&!1===e.classList.contains("q-virtual-scroll--skip"))),o=n.length,l=!0===r.virtualScrollHorizontal?e=>e.getBoundingClientRect().width:e=>e.offsetHeight;let a,i,s=e;for(let e=0;e<o;){for(a=l(n[e]),e++;e<o&&!0===n[e].classList.contains("q-virtual-scroll--with-prev");)a+=l(n[e]),e++;i=a-f[s],0!==i&&(f[s]+=i,p[Math.floor(s/1e3)]+=i),s++}}}function L(){var e;null==(e=y.value)||e.focus()}function q(t,n){const l=1*o.value;!0!==n&&!1!==Array.isArray(f)||(f=[]);const r=f.length;f.length=e.value;for(let o=e.value-1;o>=r;o--)f[o]=l;const a=Math.floor((e.value-1)/1e3);p=[];for(let o=0;o<=a;o++){let t=0;const n=Math.min(1e3*(o+1),e.value);for(let e=1e3*o;e<n;e++)t+=f[e];p.push(t)}c=-1,u=void 0,v.value=hf(p,f,0,_.value.from),h.value=hf(p,f,_.value.to,e.value),t>=0?(T(_.value.from),Qt((()=>{C(t)}))):F()}function O(e){if(void 0===e&&"undefined"!=typeof window){const o=t();null!=o&&8!==o.nodeType&&(e=pf(o,n(),g.value,b.value,r.virtualScrollHorizontal,s.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd).scrollViewSize)}d=e;const l=parseFloat(r.virtualScrollSliceRatioBefore)||0,a=1+l+(parseFloat(r.virtualScrollSliceRatioAfter)||0),i=void 0===e||e<=0?1:Math.ceil(e/o.value),u=Math.max(1,i,Math.ceil((r.virtualScrollSliceSize>0?r.virtualScrollSliceSize:10)/a));m.value={total:Math.ceil(u*a),start:Math.ceil(u*l),center:Math.ceil(u*(.5+l)),end:Math.ceil(u*(1+l)),view:i}}function P(e){c!==e&&(void 0!==r.onVirtualScroll&&a("virtualScroll",{index:e,from:_.value.from,to:_.value.to-1,direction:e<c?"decrease":"increase",ref:i}),c=e)}Xo(ar((()=>k.value+";"+r.virtualScrollSliceRatioBefore+";"+r.virtualScrollSliceRatioAfter)),(()=>{O()})),Xo(k,x),O();const F=Ha((function(){const o=t();if(null==o||8===o.nodeType)return;const l=pf(o,n(),g.value,b.value,r.virtualScrollHorizontal,s.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd),a=e.value-1,i=l.scrollMaxSize-l.offsetStart-l.offsetEnd-h.value;if(u===l.scrollStart)return;if(l.scrollMaxSize<=0)return void E(o,l,0,0);d!==l.scrollViewSize&&O(l.scrollViewSize),T(_.value.from);const c=Math.floor(l.scrollMaxSize-Math.max(l.scrollViewSize,l.offsetEnd)-Math.min(f[a],l.scrollViewSize/2));if(c>0&&Math.ceil(l.scrollStart)>=c)return void E(o,l,a,l.scrollMaxSize-l.offsetEnd-p.reduce(ff,0));let m=0,y=l.scrollStart-l.offsetStart,w=y;if(y<=i&&y+l.scrollViewSize>=v.value)y-=v.value,m=_.value.from,w=y;else for(let e=0;y>=p[e]&&m<a;e++)y-=p[e],m+=1e3;for(;y>0&&m<a;)y-=f[m],y>-l.scrollViewSize?(m++,w=y):w=f[m]+y;E(o,l,m,w)}),!0===s.platform.is.ios?120:35);Hn((()=>{O()}));let R=!1;return Bn((()=>{R=!0})),$n((()=>{if(!0!==R)return;const e=t();void 0!==u&&null!=e&&8!==e.nodeType?vf(e,u,r.virtualScrollHorizontal,s.lang.rtl):C(c)})),Gn((()=>{F.cancel()})),Object.assign(i,{scrollTo:C,reset:x,refresh:S}),{virtualScrollSliceRange:_,virtualScrollSliceSizeComputed:m,setVirtualScrollSize:O,onVirtualScrollEvt:F,localResetVirtualScroll:q,padVirtualScroll:function(e,t){const n=!0===r.virtualScrollHorizontal?"width":"height",l={["--q-virtual-scroll-item-"+n]:o.value+"px"};return["tbody"===e?ir(e,{class:"q-virtual-scroll__padding",key:"before",ref:g},[ir("tr",[ir("td",{style:{[n]:`${v.value}px`,...l},colspan:w.value})])]):ir(e,{class:"q-virtual-scroll__padding",key:"before",ref:g,style:{[n]:`${v.value}px`,...l}}),ir(e,{class:"q-virtual-scroll__content",key:"content",ref:y,tabindex:-1},t.flat()),"tbody"===e?ir(e,{class:"q-virtual-scroll__padding",key:"after",ref:b},[ir("tr",[ir("td",{style:{[n]:`${h.value}px`,...l},colspan:w.value})])]):ir(e,{class:"q-virtual-scroll__padding",key:"after",ref:b,style:{[n]:`${h.value}px`,...l}})]},scrollTo:C,reset:x,refresh:S}}const _f=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,wf=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,kf=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,xf=/[a-z0-9_ -]$/i;function Sf(e){return function(t){if("compositionend"===t.type||"change"===t.type){if(!0!==t.target.qComposing)return;t.target.qComposing=!1,e(t)}else if("compositionupdate"===t.type&&!0!==t.target.qComposing&&"string"==typeof t.data){!0===(!0===Ra.is.firefox?!1===xf.test(t.data):!0===_f.test(t.data)||!0===wf.test(t.data)||!0===kf.test(t.data))&&(t.target.qComposing=!0)}}}function Cf(e,t,n){if(n<=t)return t;const o=n-t+1;let l=t+(e-t)%o;return l<t&&(l=o+l),0===l?0:l}const Ef=e=>["add","add-unique","toggle"].includes(e),Tf=Object.keys(Kc);function Lf(e,t){if("function"==typeof e)return e;const n=void 0!==e?e:t;return e=>null!==e&&"object"==typeof e&&n in e?e[n]:e}var qf=Va({name:"QSelect",inheritAttrs:!1,props:{...bf,...Zu,...Kc,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:Ef},mapOptions:Boolean,emitValue:Boolean,disableTabSelection:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:bf.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...Qc,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(e,{slots:t,emit:n}){const{proxy:o}=Ql(),{$q:l}=o,r=Ct(!1),a=Ct(!1),i=Ct(-1),s=Ct(""),u=Ct(!1),c=Ct(!1);let d,f,p,v,h,m,g,b=null,y=null,_=null;const w=Ct(null),k=Ct(null),x=Ct(null),S=Ct(null),C=Ct(null),E=Xu(e),T=Sf(xe),L=ar((()=>Array.isArray(e.options)?e.options.length:0)),q=ar((()=>void 0===e.virtualScrollItemSize?!0===e.optionsDense?24:48:e.virtualScrollItemSize)),{virtualScrollSliceRange:O,virtualScrollSliceSizeComputed:P,localResetVirtualScroll:F,padVirtualScroll:R,onVirtualScrollEvt:A,scrollTo:V,setVirtualScrollSize:M}=yf({virtualScrollLength:L,getVirtualScrollTarget:function(){return we()},getVirtualScrollEl:we,virtualScrollItemSizeComputed:q}),I=Gc(),N=ar((()=>{const t=!0===e.mapOptions&&!0!==e.multiple,n=void 0===e.modelValue||null===e.modelValue&&!0!==t?[]:!0===e.multiple&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue];if(!0===e.mapOptions&&!0===Array.isArray(e.options)){const o=!0===e.mapOptions&&void 0!==d?d:[],l=n.map((t=>function(t,n){const o=e=>hi(oe.value(e),t);return e.options.find(o)||n.find(o)||t}(t,o)));return null===e.modelValue&&!0===t?l.filter((e=>null!==e)):l}return n})),$=ar((()=>{const t={};return Tf.forEach((n=>{const o=e[n];void 0!==o&&(t[n]=o)})),t})),B=ar((()=>null===e.optionsDark?I.isDark.value:e.optionsDark)),z=ar((()=>Wc(N.value))),D=ar((()=>{let t="q-field__input q-placeholder col";return!0===e.hideSelected||0===N.value.length?[t,e.inputClass]:(t+=" q-field__input--padding",void 0===e.inputClass?t:[t,e.inputClass])})),j=ar((()=>(!0===e.virtualScrollHorizontal?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:""))),U=ar((()=>0===L.value)),H=ar((()=>N.value.map((e=>le.value(e))).join(", "))),W=ar((()=>void 0!==e.displayValue?e.displayValue:H.value)),K=ar((()=>!0===e.optionsHtml?()=>!0:e=>!0===(null==e?void 0:e.html))),Q=ar((()=>!0===e.displayValueHtml||void 0===e.displayValue&&(!0===e.optionsHtml||N.value.some(K.value)))),G=ar((()=>!0===I.focused.value?e.tabindex:-1)),Y=ar((()=>{const t={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":!0===e.readonly?"true":"false","aria-autocomplete":!0===e.useInput?"list":"none","aria-expanded":!0===r.value?"true":"false","aria-controls":`${I.targetUid.value}_lb`};return i.value>=0&&(t["aria-activedescendant"]=`${I.targetUid.value}_${i.value}`),t})),J=ar((()=>({id:`${I.targetUid.value}_lb`,role:"listbox","aria-multiselectable":!0===e.multiple?"true":"false"}))),Z=ar((()=>N.value.map(((e,t)=>({index:t,opt:e,html:K.value(e),selected:!0,removeAtIndex:ce,toggleOption:fe,tabindex:G.value}))))),X=ar((()=>{if(0===L.value)return[];const{from:t,to:n}=O.value;return e.options.slice(t,n).map(((n,o)=>{const a=!0===re.value(n),s=!0===he(n),u=t+o,c={clickable:!0,active:s,activeClass:ne.value,manualFocus:!0,focused:!1,disable:a,tabindex:-1,dense:e.optionsDense,dark:B.value,role:"option","aria-selected":!0===s?"true":"false",id:`${I.targetUid.value}_${u}`,onClick:()=>{fe(n)}};return!0!==a&&(i.value===u&&(c.focused=!0),!0===l.platform.is.desktop&&(c.onMousemove=()=>{!0===r.value&&pe(u)})),{index:u,opt:n,html:K.value(n),label:le.value(n),selected:c.active,focused:c.focused,toggleOption:fe,setOptionIndex:pe,itemProps:c}}))})),ee=ar((()=>void 0!==e.dropdownIcon?e.dropdownIcon:l.iconSet.arrow.dropdown)),te=ar((()=>!1===e.optionsCover&&!0!==e.outlined&&!0!==e.standout&&!0!==e.borderless&&!0!==e.rounded)),ne=ar((()=>void 0!==e.optionsSelectedClass?e.optionsSelectedClass:void 0!==e.color?`text-${e.color}`:"")),oe=ar((()=>Lf(e.optionValue,"value"))),le=ar((()=>Lf(e.optionLabel,"label"))),re=ar((()=>Lf(e.optionDisable,"disable"))),ae=ar((()=>N.value.map(oe.value))),ie=ar((()=>{const e={onInput:xe,onChange:T,onKeydown:_e,onKeyup:be,onKeypress:ye,onFocus:me,onClick(e){!0===f&&Ba(e)}};return e.onCompositionstart=e.onCompositionupdate=e.onCompositionend=T,e}));function se(t){return!0===e.emitValue?oe.value(t):t}function ue(t){if(-1!==t&&t<N.value.length)if(!0===e.multiple){const o=e.modelValue.slice();n("remove",{index:t,value:o.splice(t,1)[0]}),n("update:modelValue",o)}else n("update:modelValue",null)}function ce(e){ue(e),I.focus()}function de(t,o){const l=se(t);if(!0!==e.multiple)return!0===e.fillInput&&Ce(le.value(t),!0,!0),void n("update:modelValue",l);if(0===N.value.length)return n("add",{index:0,value:l}),void n("update:modelValue",!0===e.multiple?[l]:l);if(!0===o&&!0===he(t))return;if(void 0!==e.maxValues&&e.modelValue.length>=e.maxValues)return;const r=e.modelValue.slice();n("add",{index:r.length,value:l}),r.push(l),n("update:modelValue",r)}function fe(t,o){var l;if(!0!==I.editable.value||void 0===t||!0===re.value(t))return;const r=oe.value(t);if(!0!==e.multiple)return!0!==o&&(Ce(!0===e.fillInput?le.value(t):"",!0,!0),Me()),null==(l=k.value)||l.focus(),void(0!==N.value.length&&!0===hi(oe.value(N.value[0]),r)||n("update:modelValue",!0===e.emitValue?r:t));if(!0===f&&!0!==u.value||I.focus(),me(),0===N.value.length){const o=!0===e.emitValue?r:t;return n("add",{index:0,value:o}),void n("update:modelValue",!0===e.multiple?[o]:o)}const a=e.modelValue.slice(),i=ae.value.findIndex((e=>hi(e,r)));if(-1!==i)n("remove",{index:i,value:a.splice(i,1)[0]});else{if(void 0!==e.maxValues&&a.length>=e.maxValues)return;const o=!0===e.emitValue?r:t;n("add",{index:a.length,value:o}),a.push(o)}n("update:modelValue",a)}function pe(e){if(!0!==l.platform.is.desktop)return;const t=-1!==e&&e<L.value?e:-1;i.value!==t&&(i.value=t)}function ve(t=1,n){if(!0===r.value){let o=i.value;do{o=Cf(o+t,-1,L.value-1)}while(-1!==o&&o!==i.value&&!0===re.value(e.options[o]));i.value!==o&&(pe(o),V(o),!0!==n&&!0===e.useInput&&!0===e.fillInput&&Se(o>=0?le.value(e.options[o]):v,!0))}}function he(e){const t=oe.value(e);return void 0!==ae.value.find((e=>hi(e,t)))}function me(t){!0===e.useInput&&null!==k.value&&(void 0===t||k.value===t.target&&t.target.value===H.value)&&k.value.select()}function ge(e){!0===ei(e,27)&&!0===r.value&&(Ba(e),Me(),Ie()),n("keyup",e)}function be(t){const{value:n}=t.target;if(void 0===t.keyCode)if(t.target.value="",null!==b&&(clearTimeout(b),b=null),null!==y&&(clearTimeout(y),y=null),Ie(),"string"==typeof n&&0!==n.length){const t=n.toLocaleLowerCase(),o=n=>{const o=e.options.find((e=>String(n.value(e)).toLocaleLowerCase()===t));return void 0!==o&&(-1===N.value.indexOf(o)?fe(o):Me(),!0)},l=e=>{!0!==o(oe)&&!0!==e&&!0!==o(le)&&Ee(n,!0,(()=>l(!0)))};l()}else I.clearValue(t);else ge(t)}function ye(e){n("keypress",e)}function _e(t){if(n("keydown",t),!0===Xa(t))return;const o=0!==s.value.length&&(void 0!==e.newValueMode||void 0!==e.onNewValue),l=!0!==t.shiftKey&&!0!==e.disableTabSelection&&!0!==e.multiple&&(-1!==i.value||!0===o);if(27===t.keyCode)return void za(t);if(9===t.keyCode&&!1===l)return void Ae();if(void 0===t.target||t.target.id!==I.targetUid.value||!0!==I.editable.value)return;if(40===t.keyCode&&!0!==I.innerLoading.value&&!1===r.value)return Da(t),void Ve();if(8===t.keyCode&&(!0===e.useChips||!0===e.clearable)&&!0!==e.hideSelected&&0===s.value.length)return void(!0===e.multiple&&!0===Array.isArray(e.modelValue)?ue(e.modelValue.length-1):!0!==e.multiple&&null!==e.modelValue&&n("update:modelValue",null));35!==t.keyCode&&36!==t.keyCode||"string"==typeof s.value&&0!==s.value.length||(Da(t),i.value=-1,ve(36===t.keyCode?1:-1,e.multiple)),33!==t.keyCode&&34!==t.keyCode||void 0===P.value||(Da(t),i.value=Math.max(-1,Math.min(L.value,i.value+(33===t.keyCode?-1:1)*P.value.view)),ve(33===t.keyCode?1:-1,e.multiple)),38!==t.keyCode&&40!==t.keyCode||(Da(t),ve(38===t.keyCode?-1:1,e.multiple));const a=L.value;if((void 0===m||g<Date.now())&&(m=""),a>0&&!0!==e.useInput&&void 0!==t.key&&1===t.key.length&&!1===t.altKey&&!1===t.ctrlKey&&!1===t.metaKey&&(32!==t.keyCode||0!==m.length)){!0!==r.value&&Ve(t);const n=t.key.toLocaleLowerCase(),o=1===m.length&&m[0]===n;g=Date.now()+1500,!1===o&&(Da(t),m+=n);const l=new RegExp("^"+m.split("").map((e=>-1!==".*+?^${}()|[]\\".indexOf(e)?"\\"+e:e)).join(".*"),"i");let s=i.value;if(!0===o||s<0||!0!==l.test(le.value(e.options[s])))do{s=Cf(s+1,-1,a-1)}while(s!==i.value&&(!0===re.value(e.options[s])||!0!==l.test(le.value(e.options[s]))));i.value!==s&&Qt((()=>{pe(s),V(s),s>=0&&!0===e.useInput&&!0===e.fillInput&&Se(le.value(e.options[s]),!0)}))}else if(13===t.keyCode||32===t.keyCode&&!0!==e.useInput&&""===m||9===t.keyCode&&!1!==l)if(9!==t.keyCode&&Da(t),-1!==i.value&&i.value<a)fe(e.options[i.value]);else{if(!0===o){const t=(t,n)=>{var o;if(n){if(!0!==Ef(n))return}else n=e.newValueMode;if(Ce("",!0!==e.multiple,!0),null==t)return;("toggle"===n?fe:de)(t,"add-unique"===n),!0!==e.multiple&&(null==(o=k.value)||o.focus(),Me())};if(void 0!==e.onNewValue?n("newValue",s.value,t):t(s.value),!0!==e.multiple)return}!0===r.value?Ae():!0!==I.innerLoading.value&&Ve()}}function we(){return!0===f?C.value:null!==x.value&&null!==x.value.contentEl?x.value.contentEl:void 0}function ke(){if(!0===U.value)return void 0!==t["no-option"]?t["no-option"]({inputValue:s.value}):void 0;const e=void 0!==t.option?t.option:e=>ir(Cc,{key:e.index,...e.itemProps},(()=>ir(pc,(()=>ir(vc,(()=>ir("span",{[!0===e.html?"innerHTML":"textContent"]:e.label})))))));let n=R("div",X.value.map(e));return void 0!==t["before-options"]&&(n=t["before-options"]().concat(n)),pu(t["after-options"],n)}function xe(t){null!==b&&(clearTimeout(b),b=null),null!==y&&(clearTimeout(y),y=null),t&&t.target&&!0===t.target.qComposing||(Se(t.target.value||""),p=!0,v=s.value,!0===I.focused.value||!0===f&&!0!==u.value||I.focus(),void 0!==e.onFilter&&(b=setTimeout((()=>{b=null,Ee(s.value)}),e.inputDebounce)))}function Se(t,o){s.value!==t&&(s.value=t,!0===o||0===e.inputDebounce||"0"===e.inputDebounce?n("inputValue",t):y=setTimeout((()=>{y=null,n("inputValue",t)}),e.inputDebounce))}function Ce(t,n,o){p=!0!==o,!0===e.useInput&&(Se(t,!0),!0!==n&&!0===o||(v=t),!0!==n&&Ee(t))}function Ee(t,l,a){if(void 0===e.onFilter||!0!==l&&!0!==I.focused.value)return;!0===I.innerLoading.value?n("filterAbort"):(I.innerLoading.value=!0,c.value=!0),""!==t&&!0!==e.multiple&&0!==N.value.length&&!0!==p&&t===le.value(N.value[0])&&(t="");const i=setTimeout((()=>{!0===r.value&&(r.value=!1)}),10);null!==_&&clearTimeout(_),_=i,n("filter",t,((e,t)=>{!0!==l&&!0!==I.focused.value||_!==i||(clearTimeout(_),"function"==typeof e&&e(),c.value=!1,Qt((()=>{I.innerLoading.value=!1,!0===I.editable.value&&(!0===l?!0===r.value&&Me():!0===r.value?Ne(!0):r.value=!0),"function"==typeof t&&Qt((()=>{t(o)})),"function"==typeof a&&Qt((()=>{a(o)}))})))}),(()=>{!0===I.focused.value&&_===i&&(clearTimeout(_),I.innerLoading.value=!1,c.value=!1),!0===r.value&&(r.value=!1)}))}function Te(e){ze(e),Ae()}function Le(){M()}function qe(e){var t;Ba(e),null==(t=k.value)||t.focus(),u.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function Oe(e){Ba(e),Qt((()=>{u.value=!1}))}function Pe(e){ze(e),null!==S.value&&S.value.__updateRefocusTarget(I.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),I.focused.value=!1}function Fe(e){Me(),!1===I.focused.value&&n("blur",e),Ie()}function Re(){const e=document.activeElement;null!==e&&e.id===I.targetUid.value||null===k.value||k.value===e||k.value.focus(),M()}function Ae(){!0!==a.value&&(i.value=-1,!0===r.value&&(r.value=!1),!1===I.focused.value&&(null!==_&&(clearTimeout(_),_=null),!0===I.innerLoading.value&&(n("filterAbort"),I.innerLoading.value=!1,c.value=!1)))}function Ve(n){!0===I.editable.value&&(!0===f?(I.onControlFocusin(n),a.value=!0,Qt((()=>{I.focus()}))):I.focus(),void 0!==e.onFilter?Ee(s.value):!0===U.value&&void 0===t["no-option"]||(r.value=!0))}function Me(){a.value=!1,Ae()}function Ie(){!0===e.useInput&&Ce(!0!==e.multiple&&!0===e.fillInput&&0!==N.value.length&&le.value(N.value[0])||"",!0,!0)}function Ne(t){let n=-1;if(!0===t){if(0!==N.value.length){const t=oe.value(N.value[0]);n=e.options.findIndex((e=>hi(oe.value(e),t)))}F(n)}pe(n)}function $e(){!1===a.value&&null!==x.value&&x.value.updatePosition()}function Be(e){void 0!==e&&Ba(e),n("popupShow",e),I.hasPopupOpen=!0,I.onControlFocusin(e)}function ze(e){void 0!==e&&Ba(e),n("popupHide",e),I.hasPopupOpen=!1,I.onControlFocusout(e)}function De(){f=(!0===l.platform.is.mobile||"dialog"===e.behavior)&&("menu"!==e.behavior&&(!0!==e.useInput||(void 0!==t["no-option"]||void 0!==e.onFilter||!1===U.value))),h=!0===l.platform.is.ios&&!0===f&&!0===e.useInput?"fade":e.transitionShow}return Xo(N,(t=>{d=t,!0===e.useInput&&!0===e.fillInput&&!0!==e.multiple&&!0!==I.innerLoading.value&&(!0!==a.value&&!0!==r.value||!0!==z.value)&&(!0!==p&&Ie(),!0!==a.value&&!0!==r.value||Ee(""))}),{immediate:!0}),Xo((()=>e.fillInput),Ie),Xo(r,Ne),Xo(L,(function(e,t){!0===r.value&&!1===I.innerLoading.value&&(F(-1,!0),Qt((()=>{!0===r.value&&!1===I.innerLoading.value&&(e>t?F():Ne(!0))})))})),Kn(De),Qn($e),De(),Gn((()=>{null!==b&&clearTimeout(b),null!==y&&clearTimeout(y)})),Object.assign(o,{showPopup:Ve,hidePopup:Me,removeAtIndex:ue,add:de,toggleOption:fe,getOptionIndex:()=>i.value,setOptionIndex:pe,moveOptionSelection:ve,filter:Ee,updateMenuPosition:$e,updateInputValue:Ce,isOptionSelected:he,getEmittingOptionValue:se,isOptionDisabled:(...e)=>!0===re.value.apply(null,e),getOptionValue:(...e)=>oe.value.apply(null,e),getOptionLabel:(...e)=>le.value.apply(null,e)}),Object.assign(I,{innerValue:N,fieldClass:ar((()=>`q-select q-field--auto-height q-select--with${!0!==e.useInput?"out":""}-input q-select--with${!0!==e.useChips?"out":""}-chips q-select--${!0===e.multiple?"multiple":"single"}`)),inputRef:w,targetRef:k,hasValue:z,showPopup:Ve,floatingLabel:ar((()=>!0!==e.hideSelected&&!0===z.value||"number"==typeof s.value||0!==s.value.length||Wc(e.displayValue))),getControlChild:()=>{if(!1!==I.editable.value&&(!0===a.value||!0!==U.value||void 0!==t["no-option"]))return!0===f?function(){const n=[ir(Jc,{class:`col-auto ${I.fieldClass.value}`,...$.value,for:I.targetUid.value,dark:B.value,square:!0,loading:c.value,itemAligned:!1,filled:!0,stackLabel:0!==s.value.length,...I.splitAttrs.listeners.value,onFocus:qe,onBlur:Oe},{...t,rawControl:()=>I.getControl(!0),before:void 0,after:void 0})];return!0===r.value&&n.push(ir("div",{ref:C,class:j.value+" scroll",style:e.popupContentStyle,...J.value,onClick:za,onScrollPassive:A},ke())),ir(af,{ref:S,modelValue:a.value,position:!0===e.useInput?"top":void 0,transitionShow:h,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,noRouteDismiss:e.popupNoRouteDismiss,onBeforeShow:Be,onBeforeHide:Pe,onHide:Fe,onShow:Re},(()=>ir("div",{class:"q-select__dialog"+(!0===B.value?" q-select__dialog--dark q-dark":"")+(!0===u.value?" q-select__dialog--focused":"")},n)))}():ir(Dd,{ref:x,class:j.value,style:e.popupContentStyle,modelValue:r.value,fit:!0!==e.menuShrink,cover:!0===e.optionsCover&&!0!==U.value&&!0!==e.useInput,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:B.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:e.popupNoRouteDismiss,square:te.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...J.value,onScrollPassive:A,onBeforeShow:Be,onBeforeHide:Te,onShow:Le},ke);!0===I.hasPopupOpen&&(I.hasPopupOpen=!1)},controlEvents:{onFocusin(e){I.onControlFocusin(e)},onFocusout(e){I.onControlFocusout(e,(()=>{Ie(),Ae()}))},onClick(e){var t;if(za(e),!0!==f&&!0===r.value)return Ae(),void(null==(t=k.value)||t.focus());Ve(e)}},getControl:n=>{const o=!0===e.hideSelected?[]:void 0!==t["selected-item"]?Z.value.map((e=>t["selected-item"](e))).slice():void 0!==t.selected?[].concat(t.selected()):!0===e.useChips?Z.value.map(((t,n)=>ir(od,{key:"option-"+n,removable:!0===I.editable.value&&!0!==re.value(t.opt),dense:!0,textColor:e.color,tabindex:G.value,onRemove(){t.removeAtIndex(n)}},(()=>ir("span",{class:"ellipsis",[!0===t.html?"innerHTML":"textContent"]:le.value(t.opt)}))))):[ir("span",{class:"ellipsis",[!0===Q.value?"innerHTML":"textContent"]:W.value})],l=!0===n||!0!==a.value||!0!==f;if(!0===e.useInput)o.push(function(t,n){const o=!0===n?{...Y.value,...I.splitAttrs.attributes.value}:void 0,l={ref:!0===n?k:void 0,key:"i_t",class:D.value,style:e.inputStyle,value:void 0!==s.value?s.value:"",type:"search",...o,id:!0===n?I.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":!0===t||!0===e.autofocus||void 0,disabled:!0===e.disable,readonly:!0===e.readonly,...ie.value};return!0!==t&&!0===f&&(!0===Array.isArray(l.class)?l.class=[...l.class,"no-pointer-events"]:l.class+=" no-pointer-events"),ir("input",l)}(n,l));else if(!0===I.editable.value){const t=!0===l?Y.value:void 0;o.push(ir("input",{ref:!0===l?k:void 0,key:"d_t",class:"q-select__focus-target",id:!0===l?I.targetUid.value:void 0,value:W.value,readonly:!0,"data-autofocus":!0===n||!0===e.autofocus||void 0,...t,onKeydown:_e,onKeyup:ge,onKeypress:ye})),!0===l&&"string"==typeof e.autocomplete&&0!==e.autocomplete.length&&o.push(ir("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:be}))}if(void 0!==E.value&&!0!==e.disable&&0!==ae.value.length){const t=ae.value.map((e=>ir("option",{value:e,selected:!0})));o.push(ir("select",{class:"hidden",name:E.value,multiple:e.multiple},t))}return ir("div",{class:"q-field__native row items-center",...!0===e.useInput||!0!==l?void 0:I.splitAttrs.attributes.value,...I.splitAttrs.listeners.value},o)},getInnerAppend:()=>!0!==e.loading&&!0!==c.value&&!0!==e.hideDropdownIcon?[ir(Gu,{class:"q-select__dropdown-icon"+(!0===r.value?" rotate-180":""),name:ee.value})]:null}),Yc(I)}});
/*!
  * shared v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Of="undefined"!=typeof window,Pf=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Ff=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Rf=e=>"number"==typeof e&&isFinite(e),Af=e=>"[object RegExp]"===Yf(e),Vf=e=>Jf(e)&&0===Object.keys(e).length,Mf=Object.assign,If=Object.create,Nf=(e=null)=>If(e);let $f;const Bf=()=>$f||($f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:Nf());function zf(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Df=Object.prototype.hasOwnProperty;function jf(e,t){return Df.call(e,t)}const Uf=Array.isArray,Hf=e=>"function"==typeof e,Wf=e=>"string"==typeof e,Kf=e=>"boolean"==typeof e,Qf=e=>null!==e&&"object"==typeof e,Gf=Object.prototype.toString,Yf=e=>Gf.call(e),Jf=e=>{if(!Qf(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function Zf(e){let t=e;return()=>++t}function Xf(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ep=e=>!Qf(e)||Uf(e);function tp(e,t){if(ep(e)||ep(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((o=>{"__proto__"!==o&&(Qf(e[o])&&!Qf(t[o])&&(t[o]=Array.isArray(e[o])?[]:Nf()),ep(t[o])||ep(e[o])?t[o]=e[o]:n.push({src:e[o],des:t[o]}))}))}}
/*!
  * message-compiler v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function np(e,t,n){const o={start:e,end:t};return null!=n&&(o.source=n),o}const op=/\{([0-9a-zA-Z]+)\}/g;function lp(e,...t){return 1===t.length&&ip(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(op,((e,n)=>t.hasOwnProperty(n)?t[n]:""))}const rp=Object.assign,ap=e=>"string"==typeof e,ip=e=>null!==e&&"object"==typeof e;function sp(e,t=""){return e.reduce(((e,n,o)=>0===o?e+n:e+t+n),"")}const up=1,cp=2,dp={[up]:"Use modulo before '{{0}}'."};const fp=1,pp=2,vp=3,hp=4,mp=5,gp=6,bp=7,yp=8,_p=9,wp=10,kp=11,xp=12,Sp=13,Cp=14,Ep=15,Tp=16,Lp=17,qp={[fp]:"Expected token: '{0}'",[pp]:"Invalid token in placeholder: '{0}'",[vp]:"Unterminated single quote in placeholder",[hp]:"Unknown escape sequence: \\{0}",[mp]:"Invalid unicode escape sequence: {0}",[gp]:"Unbalanced closing brace",[bp]:"Unterminated closing brace",[yp]:"Empty placeholder",[_p]:"Not allowed nest placeholder",[wp]:"Invalid linked format",[kp]:"Plural must have messages",[xp]:"Unexpected empty linked modifier",[Sp]:"Unexpected empty linked key",[Cp]:"Unexpected lexical analysis in token: '{0}'",[Ep]:"unhandled codegen node type: '{0}'",[Tp]:"unhandled mimifier node type: '{0}'"};function Op(e,t,n={}){const{domain:o,messages:l,args:r}=n,a=lp((l||qp)[e]||"",...r||[]),i=new SyntaxError(String(a));return i.code=e,t&&(i.location=t),i.domain=o,i}function Pp(e){throw e}const Fp=String.fromCharCode(8232),Rp=String.fromCharCode(8233);function Ap(e){const t=e;let n=0,o=1,l=1,r=0;const a=e=>"\r"===t[e]&&"\n"===t[e+1],i=e=>t[e]===Rp,s=e=>t[e]===Fp,u=e=>a(e)||(e=>"\n"===t[e])(e)||i(e)||s(e),c=e=>a(e)||i(e)||s(e)?"\n":t[e];function d(){return r=0,u(n)&&(o++,l=0),a(n)&&n++,n++,l++,t[n]}return{index:()=>n,line:()=>o,column:()=>l,peekOffset:()=>r,charAt:c,currentChar:()=>c(n),currentPeek:()=>c(n+r),next:d,peek:function(){return a(n+r)&&r++,r++,t[n+r]},reset:function(){n=0,o=1,l=1,r=0},resetPeek:function(e=0){r=e},skipToPeek:function(){const e=n+r;for(;e!==n;)d();r=0}}}const Vp=void 0;function Mp(e,t={}){const n=!1!==t.location,o=Ap(e),l=()=>o.index(),r=()=>{return e=o.line(),t=o.column(),n=o.index(),{line:e,column:t,offset:n};var e,t,n},a=r(),i=l(),s={currentType:14,offset:i,startLoc:a,endLoc:a,lastType:14,lastOffset:i,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},u=()=>s,{onError:c}=t;function d(e,t,o,...l){const r=u();if(t.column+=o,t.offset+=o,c){const o=Op(e,n?np(r.startLoc,t):null,{domain:"tokenizer",args:l});c(o)}}function f(e,t,o){e.endLoc=r(),e.currentType=t;const l={type:t};return n&&(l.loc=np(e.startLoc,e.endLoc)),null!=o&&(l.value=o),l}const p=e=>f(e,14);function v(e,t){return e.currentChar()===t?(e.next(),t):(d(fp,r(),0,t),"")}function h(e){let t="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)t+=e.currentPeek(),e.peek();return t}function m(e){const t=h(e);return e.skipToPeek(),t}function g(e){if(e===Vp)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function b(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o=function(e){if(e===Vp)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),o}function y(e){h(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function _(e,t=!0){const n=(t=!1,o="",l=!1)=>{const r=e.currentPeek();return"{"===r?"%"!==o&&t:"@"!==r&&r?"%"===r?(e.peek(),n(t,"%",!0)):"|"===r?!("%"!==o&&!l)||!(" "===o||"\n"===o):" "===r?(e.peek(),n(!0," ",l)):"\n"!==r||(e.peek(),n(!0,"\n",l)):"%"===o||t},o=n();return t&&e.resetPeek(),o}function w(e,t){const n=e.currentChar();return n===Vp?Vp:t(n)?(e.next(),n):null}function k(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function x(e){return w(e,k)}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function C(e){return w(e,S)}function E(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function T(e){return w(e,E)}function L(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function q(e){return w(e,L)}function O(e){let t="",n="";for(;t=T(e);)n+=t;return n}function P(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!_(e))break;t+=n,e.next()}else if(" "===n||"\n"===n)if(_(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function F(e){return"'"!==e&&"\n"!==e}function R(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return A(e,t,4);case"U":return A(e,t,6);default:return d(hp,r(),0,t),""}}function A(e,t,n){v(e,t);let o="";for(let l=0;l<n;l++){const n=q(e);if(!n){d(mp,r(),0,`\\${t}${o}${e.currentChar()}`);break}o+=n}return`\\${t}${o}`}function V(e){return"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e}function M(e){m(e);const t=v(e,"|");return m(e),t}function I(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&d(_p,r(),0),e.next(),n=f(t,2,"{"),m(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&d(yp,r(),0),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&m(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&d(bp,r(),0),n=N(e,t)||p(t),t.braceNest=0,n;default:{let o=!0,l=!0,a=!0;if(y(e))return t.braceNest>0&&d(bp,r(),0),n=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return d(bp,r(),0),t.braceNest=0,$(e,t);if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o=g(e.currentPeek());return e.resetPeek(),o}(e,t))return n=f(t,5,function(e){m(e);let t="",n="";for(;t=C(e);)n+=t;return e.currentChar()===Vp&&d(bp,r(),0),n}(e)),m(e),n;if(l=b(e,t))return n=f(t,6,function(e){m(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${O(e)}`):t+=O(e),e.currentChar()===Vp&&d(bp,r(),0),t}(e)),m(e),n;if(a=function(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o="'"===e.currentPeek();return e.resetPeek(),o}(e,t))return n=f(t,7,function(e){m(e),v(e,"'");let t="",n="";for(;t=w(e,F);)n+="\\"===t?R(e):t;const o=e.currentChar();return"\n"===o||o===Vp?(d(vp,r(),0),"\n"===o&&(e.next(),v(e,"'")),n):(v(e,"'"),n)}(e)),m(e),n;if(!o&&!l&&!a)return n=f(t,13,function(e){m(e);let t="",n="";for(;t=w(e,V);)n+=t;return n}(e)),d(pp,r(),0,n.value),m(e),n;break}}return n}function N(e,t){const{currentType:n}=t;let o=null;const l=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||"\n"!==l&&" "!==l||d(wp,r(),0),l){case"@":return e.next(),o=f(t,8,"@"),t.inLinked=!0,o;case".":return m(e),e.next(),f(t,9,".");case":":return m(e),e.next(),f(t,10,":");default:return y(e)?(o=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,o):function(e,t){const{currentType:n}=t;if(8!==n)return!1;h(e);const o="."===e.currentPeek();return e.resetPeek(),o}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;h(e);const o=":"===e.currentPeek();return e.resetPeek(),o}(e,t)?(m(e),N(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;h(e);const o=g(e.currentPeek());return e.resetPeek(),o}(e,t)?(m(e),f(t,12,function(e){let t="",n="";for(;t=x(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const o=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||" "===t||!t)&&("\n"===t?(e.peek(),o()):_(e,!1))},l=o();return e.resetPeek(),l}(e,t)?(m(e),"{"===l?I(e,t)||o:f(t,11,function(e){const t=n=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&"("!==o&&")"!==o&&o?" "===o?n:(n+=o,e.next(),t(n)):n};return t("")}(e))):(8===n&&d(wp,r(),0),t.braceNest=0,t.inLinked=!1,$(e,t))}}function $(e,t){let n={type:14};if(t.braceNest>0)return I(e,t)||p(t);if(t.inLinked)return N(e,t)||p(t);switch(e.currentChar()){case"{":return I(e,t)||p(t);case"}":return d(gp,r(),0),e.next(),f(t,3,"}");case"@":return N(e,t)||p(t);default:{if(y(e))return n=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:o,hasSpace:l}=function(e){const t=h(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(o)return l?f(t,0,P(e)):f(t,4,function(e){m(e);const t=e.currentChar();return"%"!==t&&d(fp,r(),0,t),e.next(),"%"}(e));if(_(e))return f(t,0,P(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:a}=s;return s.lastType=e,s.lastOffset=t,s.lastStartLoc=n,s.lastEndLoc=a,s.offset=l(),s.startLoc=r(),o.currentChar()===Vp?f(s,14):$(o,s)},currentOffset:l,currentPosition:r,context:u}}const Ip=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Np(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function $p(e={}){const t=!1!==e.location,{onError:n,onWarn:o}=e;function l(e,o,l,r,...a){const i=e.currentPosition();if(i.offset+=r,i.column+=r,n){const e=Op(o,t?np(l,i):null,{domain:"parser",args:a});n(e)}}function r(e,n,l,r,...a){const i=e.currentPosition();if(i.offset+=r,i.column+=r,o){const e=t?np(l,i):null;o(function(e,t,...n){const o=lp(dp[e]||"",...n||[]),l={message:String(o),code:e};return t&&(l.location=t),l}(n,e,a))}}function a(e,n,o){const l={type:e};return t&&(l.start=n,l.end=n,l.loc={start:o,end:o}),l}function i(e,n,o,l){l&&(e.type=l),t&&(e.end=n,e.loc&&(e.loc.end=o))}function s(e,t){const n=e.context(),o=a(3,n.offset,n.startLoc);return o.value=t,i(o,e.currentOffset(),e.currentPosition()),o}function u(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:l}=n,r=a(5,o,l);return r.index=parseInt(t,10),e.nextToken(),i(r,e.currentOffset(),e.currentPosition()),r}function c(e,t,n){const o=e.context(),{lastOffset:l,lastStartLoc:r}=o,s=a(4,l,r);return s.key=t,!0===n&&(s.modulo=!0),e.nextToken(),i(s,e.currentOffset(),e.currentPosition()),s}function d(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:l}=n,r=a(9,o,l);return r.value=t.replace(Ip,Np),e.nextToken(),i(r,e.currentOffset(),e.currentPosition()),r}function f(e){const t=e.context(),n=a(6,t.offset,t.startLoc);let o=e.nextToken();if(9===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:r}=n,s=a(8,o,r);return 12!==t.type?(l(e,xp,n.lastStartLoc,0),s.value="",i(s,o,r),{nextConsumeToken:t,node:s}):(null==t.value&&l(e,Cp,n.lastStartLoc,0,Bp(t)),s.value=t.value||"",i(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(10!==o.type&&l(e,Cp,t.lastStartLoc,0,Bp(o)),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 11:null==o.value&&l(e,Cp,t.lastStartLoc,0,Bp(o)),n.key=function(e,t){const n=e.context(),o=a(7,n.offset,n.startLoc);return o.value=t,i(o,e.currentOffset(),e.currentPosition()),o}(e,o.value||"");break;case 5:null==o.value&&l(e,Cp,t.lastStartLoc,0,Bp(o)),n.key=c(e,o.value||"");break;case 6:null==o.value&&l(e,Cp,t.lastStartLoc,0,Bp(o)),n.key=u(e,o.value||"");break;case 7:null==o.value&&l(e,Cp,t.lastStartLoc,0,Bp(o)),n.key=d(e,o.value||"");break;default:{l(e,Sp,t.lastStartLoc,0);const r=e.context(),s=a(7,r.offset,r.startLoc);return s.value="",i(s,r.offset,r.startLoc),n.key=s,i(n,r.offset,r.startLoc),{nextConsumeToken:o,node:n}}}return i(n,e.currentOffset(),e.currentPosition()),{node:n}}function p(e){const t=e.context(),n=a(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let o=null,p=null;do{const a=o||e.nextToken();switch(o=null,a.type){case 0:null==a.value&&l(e,Cp,t.lastStartLoc,0,Bp(a)),n.items.push(s(e,a.value||""));break;case 6:null==a.value&&l(e,Cp,t.lastStartLoc,0,Bp(a)),n.items.push(u(e,a.value||""));break;case 4:p=!0;break;case 5:null==a.value&&l(e,Cp,t.lastStartLoc,0,Bp(a)),n.items.push(c(e,a.value||"",!!p)),p&&(r(e,up,t.lastStartLoc,0,Bp(a)),p=null);break;case 7:null==a.value&&l(e,Cp,t.lastStartLoc,0,Bp(a)),n.items.push(d(e,a.value||""));break;case 8:{const t=f(e);n.items.push(t.node),o=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return i(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function v(e){const t=e.context(),{offset:n,startLoc:o}=t,r=p(e);return 14===t.currentType?r:function(e,t,n,o){const r=e.context();let s=0===o.items.length;const u=a(1,t,n);u.cases=[],u.cases.push(o);do{const t=p(e);s||(s=0===t.items.length),u.cases.push(t)}while(14!==r.currentType);return s&&l(e,kp,n,0),i(u,e.currentOffset(),e.currentPosition()),u}(e,n,o,r)}return{parse:function(n){const o=Mp(n,rp({},e)),r=o.context(),s=a(0,r.offset,r.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=v(o),e.onCacheKey&&(s.cacheKey=e.onCacheKey(n)),14!==r.currentType&&l(o,Cp,r.lastStartLoc,0,n[r.offset]||""),i(s,o.currentOffset(),o.currentPosition()),s}}}function Bp(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function zp(e,t){for(let n=0;n<e.length;n++)Dp(e[n],t)}function Dp(e,t){switch(e.type){case 1:zp(e.cases,t),t.helper("plural");break;case 2:zp(e.items,t);break;case 6:Dp(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function jp(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&Dp(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function Up(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const o=e.items[n];if(3!==o.type&&9!==o.type)break;if(null==o.value)break;t.push(o.value)}if(t.length===e.items.length){e.static=sp(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function Hp(e){switch(e.t=e.type,e.type){case 0:{const t=e;Hp(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)Hp(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)Hp(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Hp(t.key),t.k=t.key,delete t.key,t.modifier&&(Hp(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw Op(Tp,null,{domain:"minifier",args:[e.type]})}delete e.type}function Wp(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Wp(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const l=t.cases.length;for(let n=0;n<l&&(Wp(e,t.cases[n]),n!==l-1);n++)e.push(", ");e.deindent(o()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const l=t.items.length;for(let r=0;r<l&&(Wp(e,t.items[r]),r!==l-1);r++)e.push(", ");e.deindent(o()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Wp(e,t.key),t.modifier?(e.push(", "),Wp(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;default:throw Op(Ep,null,{domain:"parser",args:[t.type]})}}function Kp(e,t={}){const n=rp({},t),o=!!n.jit,l=!!n.minify,r=null==n.optimize||n.optimize,a=$p(n).parse(e);return o?(r&&function(e){const t=e.body;2===t.type?Up(t):t.cases.forEach((e=>Up(e)))}(a),l&&Hp(a),{ast:a,code:""}):(jp(a,n),((e,t={})=>{const n=ap(t.mode)?t.mode:"normal",o=ap(t.filename)?t.filename:"message.intl",l=!!t.sourceMap,r=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==n,i=e.helpers||[],s=function(e,t){const{sourceMap:n,filename:o,breakLineCode:l,needIndent:r}=t,a=!1!==t.location,i={filename:o,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:l,needIndent:r,indentLevel:0};function s(e,t){i.code+=e}function u(e,t=!0){const n=t?l:"";s(r?n+"  ".repeat(e):n)}return a&&e.loc&&(i.source=e.loc.source),{context:()=>i,push:s,indent:function(e=!0){const t=++i.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--i.indentLevel;e&&u(t)},newline:function(){u(i.indentLevel)},helper:e=>`_${e}`,needIndent:()=>i.needIndent}}(e,{mode:n,filename:o,sourceMap:l,breakLineCode:r,needIndent:a});s.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(a),i.length>0&&(s.push(`const { ${sp(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),s.newline()),s.push("return "),Wp(s,e),s.deindent(a),s.push("}"),delete e.helpers;const{code:u,map:c}=s.context();return{ast:e,code:u,map:c?c.toJSON():void 0}})(a,n))}
/*!
  * core-base v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Qp=[];Qp[0]={w:[0],i:[3,0],"[":[4],o:[7]},Qp[1]={w:[1],".":[2],"[":[4],o:[7]},Qp[2]={w:[2],i:[3,0],0:[3,0]},Qp[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Qp[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Qp[5]={"'":[4,0],o:8,l:[5,0]},Qp[6]={'"':[4,0],o:8,l:[6,0]};const Gp=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Yp(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Jp(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Gp.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Zp=new Map;function Xp(e,t){return Qf(e)?e[t]:null}const ev=e=>e,tv=e=>"",nv=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,n,o)=>0===o?e+n:e+t+n),"")}(e),ov=e=>null==e?"":Uf(e)||Jf(e)&&e.toString===Gf?JSON.stringify(e,null,2):String(e);function lv(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function rv(e={}){const t=e.locale,n=function(e){const t=Rf(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Rf(e.named.count)||Rf(e.named.n))?Rf(e.named.count)?e.named.count:Rf(e.named.n)?e.named.n:t:t}(e),o=Qf(e.pluralRules)&&Wf(t)&&Hf(e.pluralRules[t])?e.pluralRules[t]:lv,l=Qf(e.pluralRules)&&Wf(t)&&Hf(e.pluralRules[t])?lv:void 0,r=e.list||[],a=e.named||Nf();Rf(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function i(t){const n=Hf(e.messages)?e.messages(t):!!Qf(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):tv)}const s=Jf(e.processor)&&Hf(e.processor.normalize)?e.processor.normalize:nv,u=Jf(e.processor)&&Hf(e.processor.interpolate)?e.processor.interpolate:ov,c={list:e=>r[e],named:e=>a[e],plural:e=>e[o(n,e.length,l)],linked:(t,...n)=>{const[o,l]=n;let r="text",a="";1===n.length?Qf(o)?(a=o.modifier||a,r=o.type||r):Wf(o)&&(a=o||a):2===n.length&&(Wf(o)&&(a=o||a),Wf(l)&&(r=l||r));const s=i(t)(c),u="vnode"===r&&Uf(s)&&a?s[0]:s;return a?(d=a,e.modifiers?e.modifiers[d]:ev)(u,r):u;var d},message:i,type:Jf(e.processor)&&Wf(e.processor.type)?e.processor.type:"text",interpolate:u,normalize:s,values:Mf(Nf(),r,a)};return c}const av=cp,iv=Zf(av),sv={NOT_FOUND_KEY:av,FALLBACK_TO_TRANSLATE:iv(),CANNOT_FORMAT_NUMBER:iv(),FALLBACK_TO_NUMBER_FORMAT:iv(),CANNOT_FORMAT_DATE:iv(),FALLBACK_TO_DATE_FORMAT:iv(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:iv(),__EXTEND_POINT__:iv()},uv=Lp,cv=Zf(uv),dv={INVALID_ARGUMENT:uv,INVALID_DATE_ARGUMENT:cv(),INVALID_ISO_DATE_ARGUMENT:cv(),NOT_SUPPORT_NON_STRING_MESSAGE:cv(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:cv(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:cv(),NOT_SUPPORT_LOCALE_TYPE:cv(),__EXTEND_POINT__:cv()};function fv(e){return Op(e,null,void 0)}function pv(e,t){return null!=t.locale?hv(t.locale):hv(e.locale)}let vv;function hv(e){if(Wf(e))return e;if(Hf(e)){if(e.resolvedOnce&&null!=vv)return vv;if("Function"===e.constructor.name){const n=e();if(Qf(t=n)&&Hf(t.then)&&Hf(t.catch))throw fv(dv.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return vv=n}throw fv(dv.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw fv(dv.NOT_SUPPORT_LOCALE_TYPE);var t}function mv(e,t,n){return[...new Set([n,...Uf(t)?t:Qf(t)?Object.keys(t):Wf(t)?[t]:[n]])]}function gv(e,t,n){const o=Wf(n)?n:wv,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let r=l.__localeChainCache.get(o);if(!r){r=[];let e=[n];for(;Uf(e);)e=bv(r,e,t);const a=Uf(t)||!Jf(t)?t:t.default?t.default:null;e=Wf(a)?[a]:a,Uf(e)&&bv(r,e,!1),l.__localeChainCache.set(o,r)}return r}function bv(e,t,n){let o=!0;for(let l=0;l<t.length&&Kf(o);l++){const r=t[l];Wf(r)&&(o=yv(e,t[l],n))}return o}function yv(e,t,n){let o;const l=t.split("-");do{o=_v(e,l.join("-"),n),l.splice(-1,1)}while(l.length&&!0===o);return o}function _v(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o="!"!==t[t.length-1];const l=t.replace(/!/g,"");e.push(l),(Uf(n)||Jf(n))&&n[l]&&(o=n[l])}return o}const wv="en-US",kv=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let xv,Sv,Cv;let Ev=null;const Tv=e=>{Ev=e};let Lv=0;function qv(e={}){const t=Hf(e.onWarn)?e.onWarn:Xf,n=Wf(e.version)?e.version:"9.14.3",o=Wf(e.locale)||Hf(e.locale)?e.locale:wv,l=Hf(o)?wv:o,r=Uf(e.fallbackLocale)||Jf(e.fallbackLocale)||Wf(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l,a=Jf(e.messages)?e.messages:Ov(l),i=Jf(e.datetimeFormats)?e.datetimeFormats:Ov(l),s=Jf(e.numberFormats)?e.numberFormats:Ov(l),u=Mf(Nf(),e.modifiers,{upper:(e,t)=>"text"===t&&Wf(e)?e.toUpperCase():"vnode"===t&&Qf(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&Wf(e)?e.toLowerCase():"vnode"===t&&Qf(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&Wf(e)?kv(e):"vnode"===t&&Qf(e)&&"__v_isVNode"in e?kv(e.children):e}),c=e.pluralRules||Nf(),d=Hf(e.missing)?e.missing:null,f=!Kf(e.missingWarn)&&!Af(e.missingWarn)||e.missingWarn,p=!Kf(e.fallbackWarn)&&!Af(e.fallbackWarn)||e.fallbackWarn,v=!!e.fallbackFormat,h=!!e.unresolving,m=Hf(e.postTranslation)?e.postTranslation:null,g=Jf(e.processor)?e.processor:null,b=!Kf(e.warnHtmlMessage)||e.warnHtmlMessage,y=!!e.escapeParameter,_=Hf(e.messageCompiler)?e.messageCompiler:xv,w=Hf(e.messageResolver)?e.messageResolver:Sv||Xp,k=Hf(e.localeFallbacker)?e.localeFallbacker:Cv||mv,x=Qf(e.fallbackContext)?e.fallbackContext:void 0,S=e,C=Qf(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,E=Qf(S.__numberFormatters)?S.__numberFormatters:new Map,T=Qf(S.__meta)?S.__meta:{};Lv++;const L={version:n,cid:Lv,locale:o,fallbackLocale:r,messages:a,modifiers:u,pluralRules:c,missing:d,missingWarn:f,fallbackWarn:p,fallbackFormat:v,unresolving:h,postTranslation:m,processor:g,warnHtmlMessage:b,escapeParameter:y,messageCompiler:_,messageResolver:w,localeFallbacker:k,fallbackContext:x,onWarn:t,__meta:T};return L.datetimeFormats=i,L.numberFormats=s,L.__datetimeFormatters=C,L.__numberFormatters=E,L}const Ov=e=>({[e]:Nf()});function Pv(e,t,n,o,l){const{missing:r,onWarn:a}=e;if(null!==r){const o=r(e,n,t,l);return Wf(o)?o:t}return t}function Fv(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Rv(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(o=e,l=t[r],o!==l&&o.split("-")[0]===l.split("-")[0])return!0;var o,l;return!1}function Av(e){return t=>function(e,t){const n=(o=t,Kv(o,Vv));var o;if(null==n)throw Qv(0);if(1===Dv(n)){const t=function(e){return Kv(e,Mv,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,Iv(e,n)]),[]))}return Iv(e,n)}(t,e)}const Vv=["b","body"];const Mv=["c","cases"];function Iv(e,t){const n=function(e){return Kv(e,Nv)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return Kv(e,$v,[])}(t).reduce(((t,n)=>[...t,Bv(e,n)]),[]);return e.normalize(n)}}const Nv=["s","static"];const $v=["i","items"];function Bv(e,t){const n=Dv(t);switch(n){case 3:case 9:case 7:case 8:return Uv(t,n);case 4:{const o=t;if(jf(o,"k")&&o.k)return e.interpolate(e.named(o.k));if(jf(o,"key")&&o.key)return e.interpolate(e.named(o.key));throw Qv(n)}case 5:{const o=t;if(jf(o,"i")&&Rf(o.i))return e.interpolate(e.list(o.i));if(jf(o,"index")&&Rf(o.index))return e.interpolate(e.list(o.index));throw Qv(n)}case 6:{const n=t,o=function(e){return Kv(e,Hv)}(n),l=function(e){const t=Kv(e,Wv);if(t)return t;throw Qv(6)}(n);return e.linked(Bv(e,l),o?Bv(e,o):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const zv=["t","type"];function Dv(e){return Kv(e,zv)}const jv=["v","value"];function Uv(e,t){const n=Kv(e,jv);if(n)return n;throw Qv(t)}const Hv=["m","modifier"];const Wv=["k","key"];function Kv(e,t,n){for(let o=0;o<t.length;o++){const n=t[o];if(jf(e,n)&&null!=e[n])return e[n]}return n}function Qv(e){return new Error(`unhandled node type: ${e}`)}const Gv=e=>e;let Yv=Nf();function Jv(e){return Qf(e)&&0===Dv(e)&&(jf(e,"b")||jf(e,"body"))}const Zv=()=>"",Xv=e=>Hf(e);function eh(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:l,messageCompiler:r,fallbackLocale:a,messages:i}=e,[s,u]=oh(...t),c=Kf(u.missingWarn)?u.missingWarn:e.missingWarn,d=Kf(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,f=Kf(u.escapeParameter)?u.escapeParameter:e.escapeParameter,p=!!u.resolvedMessage,v=Wf(u.default)||Kf(u.default)?Kf(u.default)?r?s:()=>s:u.default:n?r?s:()=>s:"",h=n||""!==v,m=pv(e,u);f&&function(e){Uf(e.list)?e.list=e.list.map((e=>Wf(e)?zf(e):e)):Qf(e.named)&&Object.keys(e.named).forEach((t=>{Wf(e.named[t])&&(e.named[t]=zf(e.named[t]))}))}(u);let[g,b,y]=p?[s,m,i[m]||Nf()]:th(e,s,m,a,d,c),_=g,w=s;if(p||Wf(_)||Jv(_)||Xv(_)||h&&(_=v,w=_),!(p||(Wf(_)||Jv(_)||Xv(_))&&Wf(b)))return l?-1:s;let k=!1;const x=Xv(_)?_:nh(e,s,b,_,w,(()=>{k=!0}));if(k)return _;const S=function(e,t,n,o){const{modifiers:l,pluralRules:r,messageResolver:a,fallbackLocale:i,fallbackWarn:s,missingWarn:u,fallbackContext:c}=e,d=o=>{let l=a(n,o);if(null==l&&c){const[,,e]=th(c,o,t,i,s,u);l=a(e,o)}if(Wf(l)||Jv(l)){let n=!1;const r=nh(e,o,t,l,o,(()=>{n=!0}));return n?Zv:r}return Xv(l)?l:Zv},f={locale:t,modifiers:l,pluralRules:r,messages:d};e.processor&&(f.processor=e.processor);o.list&&(f.list=o.list);o.named&&(f.named=o.named);Rf(o.plural)&&(f.pluralIndex=o.plural);return f}(e,b,y,u),C=function(e,t,n){return t(n)}(0,x,rv(S));return o?o(C,s):C}function th(e,t,n,o,l,r){const{messages:a,onWarn:i,messageResolver:s,localeFallbacker:u}=e,c=u(e,o,n);let d,f=Nf(),p=null;for(let v=0;v<c.length&&(d=c[v],f=a[d]||Nf(),null===(p=s(f,t))&&(p=f[t]),!(Wf(p)||Jv(p)||Xv(p)));v++)if(!Rv(d,c)){const n=Pv(e,t,d,0,"translate");n!==t&&(p=n)}return[p,d,f]}function nh(e,t,n,o,l,r){const{messageCompiler:a,warnHtmlMessage:i}=e;if(Xv(o)){const e=o;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==a){const e=()=>o;return e.locale=n,e.key=t,e}const s=a(o,function(e,t,n,o,l,r){return{locale:t,key:n,warnHtmlMessage:l,onError:e=>{throw r&&r(e),e},onCacheKey:e=>((e,t,n)=>Ff({l:e,k:t,s:n}))(t,n,e)}}(0,n,l,0,i,r));return s.locale=n,s.key=t,s.source=o,s}function oh(...e){const[t,n,o]=e,l=Nf();if(!(Wf(t)||Rf(t)||Xv(t)||Jv(t)))throw fv(dv.INVALID_ARGUMENT);const r=Rf(t)?String(t):(Xv(t),t);return Rf(n)?l.plural=n:Wf(n)?l.default=n:Jf(n)&&!Vf(n)?l.named=n:Uf(n)&&(l.list=n),Rf(o)?l.plural=o:Wf(o)?l.default=o:Jf(o)&&Mf(l,o),[r,l]}function lh(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:l,onWarn:r,localeFallbacker:a}=e,{__datetimeFormatters:i}=e,[s,u,c,d]=ah(...t);Kf(c.missingWarn)?c.missingWarn:e.missingWarn;Kf(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const f=!!c.part,p=pv(e,c),v=a(e,l,p);if(!Wf(s)||""===s)return new Intl.DateTimeFormat(p,d).format(u);let h,m={},g=null;for(let _=0;_<v.length&&(h=v[_],m=n[h]||{},g=m[s],!Jf(g));_++)Pv(e,s,h,0,"datetime format");if(!Jf(g)||!Wf(h))return o?-1:s;let b=`${h}__${s}`;Vf(d)||(b=`${b}__${JSON.stringify(d)}`);let y=i.get(b);return y||(y=new Intl.DateTimeFormat(h,Mf({},g,d)),i.set(b,y)),f?y.formatToParts(u):y.format(u)}const rh=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ah(...e){const[t,n,o,l]=e,r=Nf();let a,i=Nf();if(Wf(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw fv(dv.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();a=new Date(n);try{a.toISOString()}catch(hy){throw fv(dv.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===Yf(t)){if(isNaN(t.getTime()))throw fv(dv.INVALID_DATE_ARGUMENT);a=t}else{if(!Rf(t))throw fv(dv.INVALID_ARGUMENT);a=t}return Wf(n)?r.key=n:Jf(n)&&Object.keys(n).forEach((e=>{rh.includes(e)?i[e]=n[e]:r[e]=n[e]})),Wf(o)?r.locale=o:Jf(o)&&(i=o),Jf(l)&&(i=l),[r.key||"",a,r,i]}function ih(e,t,n){const o=e;for(const l in n){const e=`${t}__${l}`;o.__datetimeFormatters.has(e)&&o.__datetimeFormatters.delete(e)}}function sh(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:l,onWarn:r,localeFallbacker:a}=e,{__numberFormatters:i}=e,[s,u,c,d]=ch(...t);Kf(c.missingWarn)?c.missingWarn:e.missingWarn;Kf(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const f=!!c.part,p=pv(e,c),v=a(e,l,p);if(!Wf(s)||""===s)return new Intl.NumberFormat(p,d).format(u);let h,m={},g=null;for(let _=0;_<v.length&&(h=v[_],m=n[h]||{},g=m[s],!Jf(g));_++)Pv(e,s,h,0,"number format");if(!Jf(g)||!Wf(h))return o?-1:s;let b=`${h}__${s}`;Vf(d)||(b=`${b}__${JSON.stringify(d)}`);let y=i.get(b);return y||(y=new Intl.NumberFormat(h,Mf({},g,d)),i.set(b,y)),f?y.formatToParts(u):y.format(u)}const uh=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ch(...e){const[t,n,o,l]=e,r=Nf();let a=Nf();if(!Rf(t))throw fv(dv.INVALID_ARGUMENT);const i=t;return Wf(n)?r.key=n:Jf(n)&&Object.keys(n).forEach((e=>{uh.includes(e)?a[e]=n[e]:r[e]=n[e]})),Wf(o)?r.locale=o:Jf(o)&&(a=o),Jf(l)&&(a=l),[r.key||"",i,r,a]}function dh(e,t,n){const o=e;for(const l in n){const e=`${t}__${l}`;o.__numberFormatters.has(e)&&o.__numberFormatters.delete(e)}}"boolean"!=typeof __INTLIFY_JIT_COMPILATION__&&(Bf().__INTLIFY_JIT_COMPILATION__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(Bf().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const fh=sv.__EXTEND_POINT__,ph=Zf(fh);ph(),ph(),ph(),ph(),ph(),ph(),ph(),ph(),ph();const vh=dv.__EXTEND_POINT__,hh=Zf(vh),mh={UNEXPECTED_RETURN_TYPE:vh,INVALID_ARGUMENT:hh(),MUST_BE_CALL_SETUP_TOP:hh(),NOT_INSTALLED:hh(),NOT_AVAILABLE_IN_LEGACY_MODE:hh(),REQUIRED_VALUE:hh(),INVALID_VALUE:hh(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:hh(),NOT_INSTALLED_WITH_PROVIDE:hh(),UNEXPECTED_ERROR:hh(),NOT_COMPATIBLE_LEGACY_VUE_I18N:hh(),BRIDGE_SUPPORT_VUE_2_ONLY:hh(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:hh(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:hh(),__EXTEND_POINT__:hh()};function gh(e,...t){return Op(e,null,void 0)}const bh=Pf("__translateVNode"),yh=Pf("__datetimeParts"),_h=Pf("__numberParts"),wh=Pf("__setPluralRules");Pf("__intlifyMeta");const kh=Pf("__injectWithOption"),xh=Pf("__dispose");function Sh(e){if(!Qf(e))return e;for(const t in e)if(jf(e,t))if(t.includes(".")){const n=t.split("."),o=n.length-1;let l=e,r=!1;for(let e=0;e<o;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in l||(l[n[e]]=Nf()),!Qf(l[n[e]])){r=!0;break}l=l[n[e]]}r||(l[n[o]]=e[t],delete e[t]),Qf(l[n[o]])&&Sh(l[n[o]])}else Qf(e[t])&&Sh(e[t]);return e}function Ch(e,t){const{messages:n,__i18n:o,messageResolver:l,flatJson:r}=t,a=Jf(n)?n:Uf(o)?Nf():{[e]:Nf()};if(Uf(o)&&o.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(a[t]=a[t]||Nf(),tp(n,a[t])):tp(n,a)}else Wf(e)&&tp(JSON.parse(e),a)})),null==l&&r)for(const i in a)jf(a,i)&&Sh(a[i]);return a}function Eh(e){return e.type}function Th(e){return Ml(yl,null,e,0)}const Lh=()=>[],qh=()=>!1;let Oh=0;function Ph(e){return(t,n,o,l)=>e(n,o,Ql()||void 0,l)}function Fh(e={},t){const{__root:n,__injectWithOption:o}=e,l=void 0===n,r=e.flatJson,a=Of?Ct:Et,i=!!e.translateExistCompatible;let s=!Kf(e.inheritLocale)||e.inheritLocale;const u=a(n&&s?n.locale.value:Wf(e.locale)?e.locale:wv),c=a(n&&s?n.fallbackLocale.value:Wf(e.fallbackLocale)||Uf(e.fallbackLocale)||Jf(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:u.value),d=a(Ch(u.value,e)),f=a(Jf(e.datetimeFormats)?e.datetimeFormats:{[u.value]:{}}),p=a(Jf(e.numberFormats)?e.numberFormats:{[u.value]:{}});let v=n?n.missingWarn:!Kf(e.missingWarn)&&!Af(e.missingWarn)||e.missingWarn,h=n?n.fallbackWarn:!Kf(e.fallbackWarn)&&!Af(e.fallbackWarn)||e.fallbackWarn,m=n?n.fallbackRoot:!Kf(e.fallbackRoot)||e.fallbackRoot,g=!!e.fallbackFormat,b=Hf(e.missing)?e.missing:null,y=Hf(e.missing)?Ph(e.missing):null,_=Hf(e.postTranslation)?e.postTranslation:null,w=n?n.warnHtmlMessage:!Kf(e.warnHtmlMessage)||e.warnHtmlMessage,k=!!e.escapeParameter;const x=n?n.modifiers:Jf(e.modifiers)?e.modifiers:{};let S,C=e.pluralRules||n&&n.pluralRules;S=(()=>{l&&Tv(null);const t={version:"9.14.3",locale:u.value,fallbackLocale:c.value,messages:d.value,modifiers:x,pluralRules:C,missing:null===y?void 0:y,missingWarn:v,fallbackWarn:h,fallbackFormat:g,unresolving:!0,postTranslation:null===_?void 0:_,warnHtmlMessage:w,escapeParameter:k,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=f.value,t.numberFormats=p.value,t.__datetimeFormatters=Jf(S)?S.__datetimeFormatters:void 0,t.__numberFormatters=Jf(S)?S.__numberFormatters:void 0;const n=qv(t);return l&&Tv(n),n})(),Fv(S,u.value,c.value);const E=ar({get:()=>u.value,set:e=>{u.value=e,S.locale=u.value}}),T=ar({get:()=>c.value,set:e=>{c.value=e,S.fallbackLocale=c.value,Fv(S,u.value,e)}}),L=ar((()=>d.value)),q=ar((()=>f.value)),O=ar((()=>p.value));const P=(e,t,o,r,a,i)=>{let s;u.value,c.value,d.value,f.value,p.value;try{0,l||(S.fallbackContext=n?Ev:void 0),s=e(S)}finally{l||(S.fallbackContext=void 0)}if("translate exists"!==o&&Rf(s)&&-1===s||"translate exists"===o&&!s){const[e,o]=t();return n&&m?r(n):a(e)}if(i(s))return s;throw gh(mh.UNEXPECTED_RETURN_TYPE)};function F(...e){return P((t=>Reflect.apply(eh,null,[t,...e])),(()=>oh(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>Wf(e)))}const R={normalize:function(e){return e.map((e=>Wf(e)||Rf(e)||Kf(e)?Th(String(e)):e))},interpolate:e=>e,type:"vnode"};function A(e){return d.value[e]||{}}Oh++,n&&Of&&(Xo(n.locale,(e=>{s&&(u.value=e,S.locale=e,Fv(S,u.value,c.value))})),Xo(n.fallbackLocale,(e=>{s&&(c.value=e,S.fallbackLocale=e,Fv(S,u.value,c.value))})));const V={id:Oh,locale:E,fallbackLocale:T,get inheritLocale(){return s},set inheritLocale(e){s=e,e&&n&&(u.value=n.locale.value,c.value=n.fallbackLocale.value,Fv(S,u.value,c.value))},get availableLocales(){return Object.keys(d.value).sort()},messages:L,get modifiers(){return x},get pluralRules(){return C||{}},get isGlobal(){return l},get missingWarn(){return v},set missingWarn(e){v=e,S.missingWarn=v},get fallbackWarn(){return h},set fallbackWarn(e){h=e,S.fallbackWarn=h},get fallbackRoot(){return m},set fallbackRoot(e){m=e},get fallbackFormat(){return g},set fallbackFormat(e){g=e,S.fallbackFormat=g},get warnHtmlMessage(){return w},set warnHtmlMessage(e){w=e,S.warnHtmlMessage=e},get escapeParameter(){return k},set escapeParameter(e){k=e,S.escapeParameter=e},t:F,getLocaleMessage:A,setLocaleMessage:function(e,t){if(r){const n={[e]:t};for(const e in n)jf(n,e)&&Sh(n[e]);t=n[e]}d.value[e]=t,S.messages=d.value},mergeLocaleMessage:function(e,t){d.value[e]=d.value[e]||{};const n={[e]:t};if(r)for(const o in n)jf(n,o)&&Sh(n[o]);tp(t=n[e],d.value[e]),S.messages=d.value},getPostTranslationHandler:function(){return Hf(_)?_:null},setPostTranslationHandler:function(e){_=e,S.postTranslation=e},getMissingHandler:function(){return b},setMissingHandler:function(e){null!==e&&(y=Ph(e)),b=e,S.missing=y},[wh]:function(e){C=e,S.pluralRules=C}};return V.datetimeFormats=q,V.numberFormats=O,V.rt=function(...e){const[t,n,o]=e;if(o&&!Qf(o))throw gh(mh.INVALID_ARGUMENT);return F(t,n,Mf({resolvedMessage:!0},o||{}))},V.te=function(e,t){return P((()=>{if(!e)return!1;const n=A(Wf(t)?t:u.value),o=S.messageResolver(n,e);return i?null!=o:Jv(o)||Xv(o)||Wf(o)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),qh,(e=>Kf(e)))},V.tm=function(e){const t=function(e){let t=null;const n=gv(S,c.value,u.value);for(let o=0;o<n.length;o++){const l=d.value[n[o]]||{},r=S.messageResolver(l,e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:n&&n.tm(e)||{}},V.d=function(...e){return P((t=>Reflect.apply(lh,null,[t,...e])),(()=>ah(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>Wf(e)))},V.n=function(...e){return P((t=>Reflect.apply(sh,null,[t,...e])),(()=>ch(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>Wf(e)))},V.getDateTimeFormat=function(e){return f.value[e]||{}},V.setDateTimeFormat=function(e,t){f.value[e]=t,S.datetimeFormats=f.value,ih(S,e,t)},V.mergeDateTimeFormat=function(e,t){f.value[e]=Mf(f.value[e]||{},t),S.datetimeFormats=f.value,ih(S,e,t)},V.getNumberFormat=function(e){return p.value[e]||{}},V.setNumberFormat=function(e,t){p.value[e]=t,S.numberFormats=p.value,dh(S,e,t)},V.mergeNumberFormat=function(e,t){p.value[e]=Mf(p.value[e]||{},t),S.numberFormats=p.value,dh(S,e,t)},V[kh]=o,V[bh]=function(...e){return P((t=>{let n;const o=t;try{o.processor=R,n=Reflect.apply(eh,null,[o,...e])}finally{o.processor=null}return n}),(()=>oh(...e)),"translate",(t=>t[bh](...e)),(e=>[Th(e)]),(e=>Uf(e)))},V[yh]=function(...e){return P((t=>Reflect.apply(lh,null,[t,...e])),(()=>ah(...e)),"datetime format",(t=>t[yh](...e)),Lh,(e=>Wf(e)||Uf(e)))},V[_h]=function(...e){return P((t=>Reflect.apply(sh,null,[t,...e])),(()=>ch(...e)),"number format",(t=>t[_h](...e)),Lh,(e=>Wf(e)||Uf(e)))},V}const Rh={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Ah(e){return bl}const Vh=An({name:"i18n-t",props:Mf({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Rf(e)||!isNaN(e)}},Rh),setup(e,t){const{slots:n,attrs:o}=t,l=e.i18n||Uh({useScope:e.scope,__useComponent:!0});return()=>{const r=Object.keys(n).filter((e=>"_"!==e)),a=Nf();e.locale&&(a.locale=e.locale),void 0!==e.plural&&(a.plural=Wf(e.plural)?+e.plural:e.plural);const i=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===bl?t.children:[t]]),[]);return t.reduce(((t,n)=>{const o=e[n];return o&&(t[n]=o()),t}),Nf())}(t,r),s=l[bh](e.keypath,i,a),u=Mf(Nf(),o);return ir(Wf(e.tag)||Qf(e.tag)?e.tag:Ah(),u,s)}}});function Mh(e,t,n,o){const{slots:l,attrs:r}=t;return()=>{const t={part:!0};let a=Nf();e.locale&&(t.locale=e.locale),Wf(e.format)?t.key=e.format:Qf(e.format)&&(Wf(e.format.key)&&(t.key=e.format.key),a=Object.keys(e.format).reduce(((t,o)=>n.includes(o)?Mf(Nf(),t,{[o]:e.format[o]}):t),Nf()));const i=o(e.value,t,a);let s=[t.key];Uf(i)?s=i.map(((e,t)=>{const n=l[e.type],o=n?n({[e.type]:e.value,index:t,parts:i}):[e.value];var r;return Uf(r=o)&&!Wf(r[0])&&(o[0].key=`${e.type}-${t}`),o})):Wf(i)&&(s=[i]);const u=Mf(Nf(),r);return ir(Wf(e.tag)||Qf(e.tag)?e.tag:Ah(),u,s)}}const Ih=An({name:"i18n-n",props:Mf({value:{type:Number,required:!0},format:{type:[String,Object]}},Rh),setup(e,t){const n=e.i18n||Uh({useScope:e.scope,__useComponent:!0});return Mh(e,t,uh,((...e)=>n[_h](...e)))}}),Nh=An({name:"i18n-d",props:Mf({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Rh),setup(e,t){const n=e.i18n||Uh({useScope:e.scope,__useComponent:!0});return Mh(e,t,rh,((...e)=>n[yh](...e)))}});function $h(e){if(Wf(e))return{path:e};if(Jf(e)){if(!("path"in e))throw gh(mh.REQUIRED_VALUE);return e}throw gh(mh.INVALID_VALUE)}function Bh(e){const{path:t,locale:n,args:o,choice:l,plural:r}=e,a={},i=o||{};return Wf(n)&&(a.locale=n),Rf(l)&&(a.plural=l),Rf(r)&&(a.plural=r),[t,i,a]}function zh(e,t,...n){const o=Jf(n[0])?n[0]:{},l=!!o.useI18nComponentName;(!Kf(o.globalInstall)||o.globalInstall)&&([l?"i18n":Vh.name,"I18nT"].forEach((t=>e.component(t,Vh))),[Ih.name,"I18nN"].forEach((t=>e.component(t,Ih))),[Nh.name,"I18nD"].forEach((t=>e.component(t,Nh)))),e.directive("t",function(e){const t=t=>{const{instance:n,modifiers:o,value:l}=t;if(!n||!n.$)throw gh(mh.UNEXPECTED_ERROR);const r=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return null!=o?o.__composer:e.global.__composer}}(e,n.$),a=$h(l);return[Reflect.apply(r.t,r,[...Bh(a)]),r]};return{created:(n,o)=>{const[l,r]=t(o);Of&&e.global===r&&(n.__i18nWatcher=Xo(r.locale,(()=>{o.instance&&o.instance.$forceUpdate()}))),n.__composer=r,n.textContent=l},unmounted:e=>{Of&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,o=$h(t);e.textContent=Reflect.apply(n.t,n,[...Bh(o)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}(t))}const Dh=Pf("global-vue-i18n");function jh(e={},t){const n=!Kf(e.globalInjection)||e.globalInjection,o=new Map,[l,r]=function(e,t,n){const o=re();{const t=o.run((()=>Fh(e)));if(null==t)throw gh(mh.UNEXPECTED_ERROR);return[o,t]}}(e),a=Pf("");{const e={get mode(){return"composition"},get allowComposition(){return true},async install(t,...o){if(t.__VUE_I18N_SYMBOL__=a,t.provide(t.__VUE_I18N_SYMBOL__,e),Jf(o[0])){const t=o[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let l=null;n&&(l=function(e,t){const n=Object.create(null);Hh.forEach((e=>{const o=Object.getOwnPropertyDescriptor(t,e);if(!o)throw gh(mh.UNEXPECTED_ERROR);const l=St(o.value)?{get:()=>o.value.value,set(e){o.value.value=e}}:{get:()=>o.get&&o.get()};Object.defineProperty(n,e,l)})),e.config.globalProperties.$i18n=n,Wh.forEach((n=>{const o=Object.getOwnPropertyDescriptor(t,n);if(!o||!o.value)throw gh(mh.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,o)}));return()=>{delete e.config.globalProperties.$i18n,Wh.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}(t,e.global)),zh(t,e,...o);const r=t.unmount;t.unmount=()=>{l&&l(),e.dispose(),r()}},get global(){return r},dispose(){l.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return e}}function Uh(e={}){const t=Ql();if(null==t)throw gh(mh.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw gh(mh.NOT_INSTALLED);const n=function(e){{const t=Oo(e.isCE?Dh:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw gh(e.isCE?mh.NOT_INSTALLED_WITH_PROVIDE:mh.UNEXPECTED_ERROR);return t}}(t),o=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),l=Eh(t),r=function(e,t){return Vf(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("global"===r)return function(e,t,n){let o=Qf(t.messages)?t.messages:Nf();"__i18nGlobal"in n&&(o=Ch(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const l=Object.keys(o);if(l.length&&l.forEach((t=>{e.mergeLocaleMessage(t,o[t])})),Qf(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(Qf(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}(o,e,l),o;if("parent"===r){let l=function(e,t,n=!1){let o=null;const l=t.root;let r=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=r;){const t=e;if("composition"===e.mode&&(o=t.__getInstance(r)),null!=o)break;if(l===r)break;r=r.parent}return o}(n,t,e.__useComponent);return null==l&&(l=o),l}const a=n;let i=a.__getInstance(t);if(null==i){const n=Mf({},e);"__i18n"in l&&(n.__i18n=l.__i18n),o&&(n.__root=o),i=Fh(n),a.__composerExtend&&(i[xh]=a.__composerExtend(i)),function(e,t,n){Wn((()=>{}),t),Yn((()=>{const o=n;e.__deleteInstance(t);const l=o[xh];l&&(l(),delete o[xh])}),t)}(a,t,i),a.__setInstance(t,i)}return i}const Hh=["locale","fallbackLocale","availableLocales"],Wh=["t","rt","d","n","tm","te"];"boolean"!=typeof __INTLIFY_JIT_COMPILATION__&&(Bf().__INTLIFY_JIT_COMPILATION__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(Bf().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),__INTLIFY_JIT_COMPILATION__&&(xv=function(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&Wf(e)){!Kf(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Gv)(e),o=Yv[n];if(o)return o;const{ast:l,detectError:r}=function(e,t={}){let n=!1;const o=t.onError||Pp;return t.onError=e=>{n=!0,o(e)},{...Kp(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),a=Av(l);return r?a:Yv[n]=a}{const t=e.cacheKey;return t?Yv[t]||(Yv[t]=Av(e)):Av(e)}}),Sv=function(e,t){if(!Qf(e))return null;let n=Zp.get(t);if(n||(n=function(e){const t=[];let n,o,l,r,a,i,s,u=-1,c=0,d=0;const f=[];function p(){const t=e[u+1];if(5===c&&"'"===t||6===c&&'"'===t)return u++,l="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===o?o=l:o+=l},f[1]=()=>{void 0!==o&&(t.push(o),o=void 0)},f[2]=()=>{f[0](),d++},f[3]=()=>{if(d>0)d--,c=4,f[0]();else{if(d=0,void 0===o)return!1;if(o=Jp(o),!1===o)return!1;f[1]()}};null!==c;)if(u++,n=e[u],"\\"!==n||!p()){if(r=Yp(n),s=Qp[c],a=s[r]||s.l||8,8===a)return;if(c=a[0],void 0!==a[1]&&(i=f[a[1]],i&&(l=n,!1===i())))return;if(7===c)return t}}(t),n&&Zp.set(t,n)),!n)return null;const o=n.length;let l=e,r=0;for(;r<o;){const e=l[n[r]];if(void 0===e)return null;if(Hf(l))return null;l=e,r++}return l},Cv=gv;var Kh={"en-US":{balance:e=>{const{normalize:t}=e;return t(["Balance"])},apiKey:e=>{const{normalize:t}=e;return t(["Api Key"])},getKey:e=>{const{normalize:t}=e;return t(["Get a key"])},inputKey:e=>{const{normalize:t}=e;return t(["Please input your API key"])},addFounds:e=>{const{normalize:t}=e;return t(["Add Funds"])},enabled:e=>{const{normalize:t}=e;return t(["Enabled / Solve automatically"])},setting:e=>{const{normalize:t}=e;return t(["Settings"])},proxy:e=>{const{normalize:t}=e;return t(["Proxy"])},proxyType:e=>{const{normalize:t}=e;return t(["Proxy Type"])},port:e=>{const{normalize:t}=e;return t(["Port"])},login:e=>{const{normalize:t}=e;return t(["Login"])},password:e=>{const{normalize:t}=e;return t(["Password"])},loginName:e=>{const{normalize:t}=e;return t(["Password Name"])},blackControl:e=>{const{normalize:t}=e;return t(["Blacklist control"])},blackTip:e=>{const{normalize:t}=e;return t(["Captcha solving on added websites will be disabled"])},add:e=>{const{normalize:t}=e;return t(["Add"])},guide:e=>{const{normalize:t}=e;return t(["Guide"])},close:e=>{const{normalize:t}=e;return t(["Close"])},delay:e=>{const{normalize:t}=e;return t(["Delay between start solve captcha(ms):"])},repeat:e=>{const{normalize:t}=e;return t(["Repeat captcha solving in case of an error:"])},copySuccess:e=>{const{normalize:t}=e;return t(["Copy Succeed!"])},manualSolving:e=>{const{normalize:t}=e;return t(["Manual Solving"])},solvedCallback:e=>{const{normalize:t}=e;return t(["Solved Callback"])},solvedCallbackPlaceholder:e=>{const{normalize:t}=e;return t(["Solved success callback"])},taskType:e=>{const{normalize:t}=e;return t(["Task Type:"])}},"zh-CN":{balance:e=>{const{normalize:t}=e;return t(["余额"])},apiKey:e=>{const{normalize:t}=e;return t(["API密钥"])},getKey:e=>{const{normalize:t}=e;return t(["获取Key"])},inputKey:e=>{const{normalize:t}=e;return t(["请输入您的API密钥"])},addFounds:e=>{const{normalize:t}=e;return t(["添加资金"])},enabled:e=>{const{normalize:t}=e;return t(["启用 / 自动解决"])},setting:e=>{const{normalize:t}=e;return t(["设置"])},proxy:e=>{const{normalize:t}=e;return t(["代理"])},proxyType:e=>{const{normalize:t}=e;return t(["代理类型"])},port:e=>{const{normalize:t}=e;return t(["端口"])},login:e=>{const{normalize:t}=e;return t(["账号"])},password:e=>{const{normalize:t}=e;return t(["密码"])},loginName:e=>{const{normalize:t}=e;return t(["账号名称"])},blackControl:e=>{const{normalize:t}=e;return t(["黑名单控制"])},blackTip:e=>{const{normalize:t}=e;return t(["CapSolver将在添加的网站中被禁用"])},add:e=>{const{normalize:t}=e;return t(["添加"])},guide:e=>{const{normalize:t}=e;return t(["指南"])},close:e=>{const{normalize:t}=e;return t(["关闭"])},delay:e=>{const{normalize:t}=e;return t(["开始解决验证码的延迟时间（ms）："])},repeat:e=>{const{normalize:t}=e;return t(["验证失败重试次数："])},copySuccess:e=>{const{normalize:t}=e;return t(["复制成功！"])},manualSolving:e=>{const{normalize:t}=e;return t(["手动触发"])},solvedCallback:e=>{const{normalize:t}=e;return t(["回调名称"])},solvedCallbackPlaceholder:e=>{const{normalize:t}=e;return t(["成功回调函数名"])},taskType:e=>{const{normalize:t}=e;return t(["任务类型："])}},es:{balance:e=>{const{normalize:t}=e;return t(["Saldo"])},apiKey:e=>{const{normalize:t}=e;return t(["Clave API"])},getKey:e=>{const{normalize:t}=e;return t(["Obtener una clave"])},inputKey:e=>{const{normalize:t}=e;return t(["Por favor ingrese su clave API"])},addFounds:e=>{const{normalize:t}=e;return t(["Agregar fondos"])},enabled:e=>{const{normalize:t}=e;return t(["Habilitado / Resolver automáticamente"])},setting:e=>{const{normalize:t}=e;return t(["Configuración"])},proxy:e=>{const{normalize:t}=e;return t(["Proxy"])},proxyType:e=>{const{normalize:t}=e;return t(["Tipo de proxy"])},port:e=>{const{normalize:t}=e;return t(["Puerto"])},login:e=>{const{normalize:t}=e;return t(["Iniciar sesión"])},password:e=>{const{normalize:t}=e;return t(["Contraseña"])},loginName:e=>{const{normalize:t}=e;return t(["Nombre de usuario"])},blackControl:e=>{const{normalize:t}=e;return t(["Control de lista negra"])},blackTip:e=>{const{normalize:t}=e;return t(["Se desactivará la resolución de captchas en los sitios web agregados"])},add:e=>{const{normalize:t}=e;return t(["Agregar"])},guide:e=>{const{normalize:t}=e;return t(["Guía"])},close:e=>{const{normalize:t}=e;return t(["Cerrar"])},delay:e=>{const{normalize:t}=e;return t(["Retraso entre el inicio de la resolución del captcha(ms):"])},repeat:e=>{const{normalize:t}=e;return t(["Repetir la resolución del captcha en caso de un error:"])},copySuccess:e=>{const{normalize:t}=e;return t(["Replicar el éxito!"])},manualSolving:e=>{const{normalize:t}=e;return t(["Solución manual"])},solvedCallback:e=>{const{normalize:t}=e;return t(["Llamar de vuelta"])},solvedCallbackPlaceholder:e=>{const{normalize:t}=e;return t(["Llamar de vuelta"])},taskType:e=>{const{normalize:t}=e;return t(["Tipo de tarea:"])}},ru:{balance:e=>{const{normalize:t}=e;return t(["Баланс"])},apiKey:e=>{const{normalize:t}=e;return t(["Ключ API"])},getKey:e=>{const{normalize:t}=e;return t(["Получить ключ"])},inputKey:e=>{const{normalize:t}=e;return t(["Пожалуйста"])},addFounds:e=>{const{normalize:t}=e;return t(["Добавить средства"])},enabled:e=>{const{normalize:t}=e;return t(["Включено / Автоматически решать"])},setting:e=>{const{normalize:t}=e;return t(["Настройки"])},proxy:e=>{const{normalize:t}=e;return t(["Прокси"])},proxyType:e=>{const{normalize:t}=e;return t(["Тип прокси"])},port:e=>{const{normalize:t}=e;return t(["Порт"])},login:e=>{const{normalize:t}=e;return t(["Войти"])},password:e=>{const{normalize:t}=e;return t(["Пароль"])},loginName:e=>{const{normalize:t}=e;return t(["Имя пользователя"])},blackControl:e=>{const{normalize:t}=e;return t(["Управление черным списком"])},blackTip:e=>{const{normalize:t}=e;return t(["Решение капчи на добавленных сайтах будет отключено"])},add:e=>{const{normalize:t}=e;return t(["Добавить"])},guide:e=>{const{normalize:t}=e;return t(["Руководство"])},close:e=>{const{normalize:t}=e;return t(["Закрыть"])},delay:e=>{const{normalize:t}=e;return t(["Задержка между началом решения капчи (мс):"])},repeat:e=>{const{normalize:t}=e;return t(["Repetir la resolución del captcha en caso de un error:"])},copySuccess:e=>{const{normalize:t}=e;return t(["Копировать успешно!"])},manualSolving:e=>{const{normalize:t}=e;return t(["Вручную"])},solvedCallback:e=>{const{normalize:t}=e;return t(["Перезвонить"])},solvedCallbackPlaceholder:e=>{const{normalize:t}=e;return t(["Перезвонить"])},taskType:e=>{const{normalize:t}=e;return t(["Тип задачи:"])}}};const Qh=[{value:"en-US",label:"English",flag:"us"},{value:"zh-CN",label:"简体中文",flag:"cn"},{value:"es",label:"España",flag:"es"},{value:"ru",label:"Россия",flag:"ru"}];let Gh;function Yh(e){var t;if(!Qh.some((t=>t.value===e)))return;(null!=(t=window.browser)?t:window.chrome).storage.local.set({i18n:{locale:e}})}var Jh=async({app:e})=>{var t,n,o;const l=null!=(t=window.browser)?t:window.chrome,r=await l.storage.local.get("i18n"),a=(i=null==(n=null==r?void 0:r.i18n)?void 0:n.locale,Gh=jh({locale:null!=i?i:"en-US",legacy:!1,messages:{"en-US":Kh["en-US"],"zh-CN":Kh["zh-CN"],es:Kh.es,ru:Kh.ru}}),Gh);var i;document.documentElement.setAttribute("lang",null==(o=null==r?void 0:r.i18n)?void 0:o.locale),e.use(a)},Zh=Object.freeze(Object.defineProperty({__proto__:null,cacheLang:Yh,default:Jh},Symbol.toStringTag,{value:"Module"}));const Xh=["src"],em=["src"];var tm=ki(An({__name:"I18nTrigger",setup(e){const{locale:t}=Uh(),n=Ct("en-US"),o=[{value:"en-US",label:"English",icon:"assets/en-US.553867d3.svg"},{value:"zh-CN",label:"简体中文",icon:"assets/zh-CN.c1f22841.svg"},{value:"es",label:"España",icon:"assets/es.6fe80291.svg"},{value:"ru",label:"Россия",icon:"assets/ru.6c62f886.svg"}];function l(){t.value=n.value,Yh(n.value)}const r=ar((()=>o.find((e=>e.value===n.value)).icon));return Wn((async()=>{var e,t;const o=null!=(e=globalThis.browser)?e:globalThis.chrome,l=await o.storage.local.get("i18n");n.value=(null==(t=null==l?void 0:l.i18n)?void 0:t.locale)||"en-US"})),(e,t)=>(Sl(),Ol(qf,{outlined:"",modelValue:n.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>n.value=e),l],options:o,"option-value":"value","emit-value":"","map-options":""},{prepend:rn((()=>[Vl("img",{src:r.value,alt:""},null,8,Xh)])),option:rn((e=>[Ml(Cc,jl({class:"cap-option"},e.itemProps),{default:rn((()=>[Ml(pc,null,{default:rn((()=>[Vl("img",{src:e.opt.icon,alt:""},null,8,em)])),_:2},1024),Ml(pc,null,{default:rn((()=>[Ml(vc,null,{default:rn((()=>[Nl(X(e.opt.label),1)])),_:2},1024)])),_:2},1024)])),_:2},1040)])),_:1},8,["modelValue"]))}}),[["__scopeId","data-v-f1514bac"]]);const nm={class:"header-box"},om=["src"];var lm=ki(An({__name:"Header",setup(e){const t=Ct({});async function n(){await fc.set(_t(t.value))}return Wn((async()=>{const e=await fc.getAll();t.value=e})),(e,o)=>(Sl(),ql("div",nm,[Ml(rc,{class:"toolbar"},{default:rn((()=>[Vl("img",{src:qt("assets/logo-text.e47c19eb.svg"),alt:""},null,8,om),Ml(Pu),Ml(tm),Ml(lc,{modelValue:t.value.useCapsolver,"onUpdate:modelValue":[o[0]||(o[0]=e=>t.value.useCapsolver=e),n],size:"36px"},null,8,["modelValue"])])),_:1})]))}}),[["__scopeId","data-v-3c53565e"]]);var rm=ki(An({__name:"PopupLayout",setup:e=>(e,t)=>{const n=to("router-view");return Sl(),Ol(Ou,{view:"hhh Lpr fff",class:"popup-layout"},{default:rn((()=>[Ml(vu,{class:"bg-white"},{default:rn((()=>[Ml(lm)])),_:1}),Ml(mu,null,{default:rn((()=>[Ml(hu,null,{default:rn((()=>[Ml(n)])),_:1})])),_:1})])),_:1})}}),[["__scopeId","data-v-77b8ca20"]]);const am={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},im={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},sm=Object.keys(im);sm.forEach((e=>{im[e].regex=new RegExp(im[e].pattern)}));const um=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+sm.join("")+"])|(.)","g"),cm=/[.*+?^${}()|[\]\\]/g,dm=String.fromCharCode(1),fm={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function pm(e,t,n,o){let l,r,a,i,s,u;const c=Ct(null),d=Ct(function(){if(p(),!0===c.value){const t=m(g(e.modelValue));return!1!==e.fillMask?b(t):t}return e.modelValue}());function f(e){if(e<l.length)return l.slice(-e);let t="",n=l;const o=n.indexOf(dm);if(-1!==o){for(let o=e-n.length;o>0;o--)t+=dm;n=n.slice(0,o)+t+n.slice(o)}return n}function p(){if(c.value=void 0!==e.mask&&0!==e.mask.length&&(!0===e.autogrow||["textarea","text","search","url","tel","password"].includes(e.type)),!1===c.value)return i=void 0,l="",void(r="");const t=void 0===am[e.mask]?e.mask:am[e.mask],n="string"==typeof e.fillMask&&0!==e.fillMask.length?e.fillMask.slice(0,1):"_",o=n.replace(cm,"\\$&"),s=[],u=[],d=[];let f=!0===e.reverseFillMask,p="",v="";t.replace(um,((e,t,n,o,l)=>{if(void 0!==o){const e=im[o];d.push(e),v=e.negate,!0===f&&(u.push("(?:"+v+"+)?("+e.pattern+"+)?(?:"+v+"+)?("+e.pattern+"+)?"),f=!1),u.push("(?:"+v+"+)?("+e.pattern+")?")}else if(void 0!==n)p="\\"+("\\"===n?"":n),d.push(n),s.push("([^"+p+"]+)?"+p+"?");else{const e=void 0!==t?t:l;p="\\"===e?"\\\\\\\\":e.replace(cm,"\\\\$&"),d.push(e),s.push("([^"+p+"]+)?"+p+"?")}}));const h=new RegExp("^"+s.join("")+"("+(""===p?".":"[^"+p+"]")+"+)?"+(""===p?"":"["+p+"]*")+"$"),m=u.length-1,g=u.map(((t,n)=>0===n&&!0===e.reverseFillMask?new RegExp("^"+o+"*"+t):n===m?new RegExp("^"+t+"("+(""===v?".":v)+"+)?"+(!0===e.reverseFillMask?"$":o+"*")):new RegExp("^"+t)));a=d,i=t=>{const n=h.exec(!0===e.reverseFillMask?t:t.slice(0,d.length+1));null!==n&&(t=n.slice(1).join(""));const o=[],l=g.length;for(let e=0,r=t;e<l;e++){const t=g[e].exec(r);if(null===t)break;r=r.slice(t.shift().length),o.push(...t)}return 0!==o.length?o.join(""):t},l=d.map((e=>"string"==typeof e?e:dm)).join(""),r=l.split(dm).join(n)}function v(t,a,i){const u=o.value,c=u.selectionEnd,f=u.value.length-c,v=g(t);!0===a&&p();const y=m(v),_=!1!==e.fillMask?b(y):y,w=d.value!==_;u.value!==_&&(u.value=_),!0===w&&(d.value=_),document.activeElement===u&&Qt((()=>{if(_!==r)if("insertFromPaste"!==i||!0===e.reverseFillMask)if(-1===["deleteContentBackward","deleteContentForward"].indexOf(i))if(!0===e.reverseFillMask)if(!0===w){const e=Math.max(0,_.length-(_===r?0:Math.min(y.length,f+1)));1===e&&1===c?u.setSelectionRange(e,e,"forward"):h.rightReverse(u,e)}else{const e=_.length-f;u.setSelectionRange(e,e,"backward")}else if(!0===w){const e=Math.max(0,l.indexOf(dm),Math.min(y.length,c)-1);h.right(u,e)}else{const e=c-1;h.right(u,e)}else{const t=!0===e.reverseFillMask?0===c?_.length>y.length?1:0:Math.max(0,_.length-(_===r?0:Math.min(y.length,f)+1))+1:c;u.setSelectionRange(t,t,"forward")}else{const e=u.selectionEnd;let t=c-1;for(let n=s;n<=t&&n<e;n++)l[n]!==dm&&t++;h.right(u,t)}else{const t=!0===e.reverseFillMask?r.length:0;u.setSelectionRange(t,t,"forward")}}));const k=!0===e.unmaskedValue?g(_):_;String(e.modelValue)===k||null===e.modelValue&&""===k||n(k,!0)}Xo((()=>e.type+e.autogrow),p),Xo((()=>e.mask),(n=>{if(void 0!==n)v(d.value,!0);else{const n=g(d.value);p(),e.modelValue!==n&&t("update:modelValue",n)}})),Xo((()=>e.fillMask+e.reverseFillMask),(()=>{!0===c.value&&v(d.value,!0)})),Xo((()=>e.unmaskedValue),(()=>{!0===c.value&&v(d.value)}));const h={left(e,t){const n=-1===l.slice(t-1).indexOf(dm);let o=Math.max(0,t-1);for(;o>=0;o--)if(l[o]===dm){t=o,!0===n&&t++;break}if(o<0&&void 0!==l[t]&&l[t]!==dm)return h.right(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},right(e,t){const n=e.value.length;let o=Math.min(n,t+1);for(;o<=n;o++){if(l[o]===dm){t=o;break}l[o-1]===dm&&(t=o)}if(o>n&&void 0!==l[t-1]&&l[t-1]!==dm)return h.left(e,n);e.setSelectionRange(t,t,"forward")},leftReverse(e,t){const n=f(e.value.length);let o=Math.max(0,t-1);for(;o>=0;o--){if(n[o-1]===dm){t=o;break}if(n[o]===dm&&(t=o,0===o))break}if(o<0&&void 0!==n[t]&&n[t]!==dm)return h.rightReverse(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},rightReverse(e,t){const n=e.value.length,o=f(n),l=-1===o.slice(0,t+1).indexOf(dm);let r=Math.min(n,t+1);for(;r<=n;r++)if(o[r-1]===dm){(t=r)>0&&!0===l&&t--;break}if(r>n&&void 0!==o[t-1]&&o[t-1]!==dm)return h.leftReverse(e,n);e.setSelectionRange(t,t,"forward")}};function m(t){if(null==t||""===t)return"";if(!0===e.reverseFillMask)return function(e){const t=a,n=l.indexOf(dm);let o=e.length-1,r="";for(let l=t.length-1;l>=0&&-1!==o;l--){const a=t[l];let i=e[o];if("string"==typeof a)r=a+r,i===a&&o--;else{if(void 0===i||!a.regex.test(i))return r;do{r=(void 0!==a.transform?a.transform(i):i)+r,o--,i=e[o]}while(n===l&&void 0!==i&&a.regex.test(i))}}return r}(t);const n=a;let o=0,r="";for(let e=0;e<n.length;e++){const l=t[o],a=n[e];if("string"==typeof a)r+=a,l===a&&o++;else{if(void 0===l||!a.regex.test(l))return r;r+=void 0!==a.transform?a.transform(l):l,o++}}return r}function g(e){return"string"!=typeof e||void 0===i?"number"==typeof e?i(""+e):e:i(e)}function b(t){return r.length-t.length<=0?t:!0===e.reverseFillMask&&0!==t.length?r.slice(0,-t.length)+t:t+r.slice(t.length)}return{innerValue:d,hasMask:c,moveCursorForPaste:function(e,t,n){const o=m(g(e.value));t=Math.max(0,l.indexOf(dm),Math.min(o.length,t)),s=t,e.setSelectionRange(t,n,"forward")},updateMaskValue:v,onMaskedKeydown:function(n){if(t("keydown",n),!0===Xa(n)||!0===n.altKey)return;const l=o.value,r=l.selectionStart,a=l.selectionEnd;if(n.shiftKey||(u=void 0),37===n.keyCode||39===n.keyCode){n.shiftKey&&void 0===u&&(u="forward"===l.selectionDirection?r:a);const t=h[(39===n.keyCode?"right":"left")+(!0===e.reverseFillMask?"Reverse":"")];if(n.preventDefault(),t(l,u===r?a:r),n.shiftKey){const e=l.selectionStart;l.setSelectionRange(Math.min(u,e),Math.max(u,e),"forward")}}else 8===n.keyCode&&!0!==e.reverseFillMask&&r===a?(h.left(l,r),l.setSelectionRange(l.selectionStart,a,"backward")):46===n.keyCode&&!0===e.reverseFillMask&&r===a&&(h.rightReverse(l,a),l.setSelectionRange(r,l.selectionEnd,"forward"))},onMaskedClick:function(e){t("click",e),u=void 0}}}var vm=Va({name:"QInput",inheritAttrs:!1,props:{...Kc,...fm,...Zu,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...Qc,"paste","change","keydown","click","animationend"],setup(e,{emit:t,attrs:n}){const{proxy:o}=Ql(),{$q:l}=o,r={};let a,i,s,u=NaN,c=null;const d=Ct(null),f=Xu(e),{innerValue:p,hasMask:v,moveCursorForPaste:h,updateMaskValue:m,onMaskedKeydown:g,onMaskedClick:b}=pm(e,t,O,d),y=function(e,t){function n(){const t=e.modelValue;try{const e="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(t)===t&&("length"in t?Array.from(t):[t]).forEach((t=>{e.items.add(t)})),{files:e.files}}catch(hy){return{files:void 0}}}return ar(!0===t?()=>{if("file"===e.type)return n()}:n)}(e,!0),_=ar((()=>Wc(p.value))),w=Sf(L),k=Gc({changeEvent:!0}),x=ar((()=>"textarea"===e.type||!0===e.autogrow)),S=ar((()=>!0===x.value||["text","search","url","tel","password"].includes(e.type))),C=ar((()=>{const t={...k.splitAttrs.listeners.value,onInput:L,onPaste:T,onChange:F,onBlur:R,onFocus:Ba};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=w,!0===v.value&&(t.onKeydown=g,t.onClick=b),!0===e.autogrow&&(t.onAnimationend=q),t})),E=ar((()=>{const t={tabindex:0,"data-autofocus":!0===e.autofocus||void 0,rows:"textarea"===e.type?6:void 0,"aria-label":e.label,name:f.value,...k.splitAttrs.attributes.value,id:k.targetUid.value,maxlength:e.maxlength,disabled:!0===e.disable,readonly:!0===e.readonly};return!1===x.value&&(t.type=e.type),!0===e.autogrow&&(t.rows=1),t}));function T(n){if(!0===v.value&&!0!==e.reverseFillMask){const e=n.target;h(e,e.selectionStart,e.selectionEnd)}t("paste",n)}function L(n){if(!n||!n.target)return;if("file"===e.type)return void t("update:modelValue",n.target.files);const o=n.target.value;if(!0!==n.target.qComposing){if(!0===v.value)m(o,!1,n.inputType);else if(O(o),!0===S.value&&n.target===document.activeElement){const{selectionStart:e,selectionEnd:t}=n.target;void 0!==e&&void 0!==t&&Qt((()=>{n.target===document.activeElement&&0===o.indexOf(n.target.value)&&n.target.setSelectionRange(e,t)}))}!0===e.autogrow&&P()}else r.value=o}function q(e){t("animationend",e),P()}function O(n,o){s=()=>{c=null,"number"!==e.type&&!0===r.hasOwnProperty("value")&&delete r.value,e.modelValue!==n&&u!==n&&(u=n,!0===o&&(i=!0),t("update:modelValue",n),Qt((()=>{u===n&&(u=NaN)}))),s=void 0},"number"===e.type&&(a=!0,r.value=n),void 0!==e.debounce?(null!==c&&clearTimeout(c),r.value=n,c=setTimeout(s,e.debounce)):s()}function P(){requestAnimationFrame((()=>{const e=d.value;if(null!==e){const t=e.parentNode.style,{scrollTop:n}=e,{overflowY:o,maxHeight:r}=!0===l.platform.is.firefox?{}:window.getComputedStyle(e),a=void 0!==o&&"scroll"!==o;!0===a&&(e.style.overflowY="hidden"),t.marginBottom=e.scrollHeight-1+"px",e.style.height="1px",e.style.height=e.scrollHeight+"px",!0===a&&(e.style.overflowY=parseInt(r,10)<e.scrollHeight?"auto":"hidden"),t.marginBottom="",e.scrollTop=n}}))}function F(e){w(e),null!==c&&(clearTimeout(c),c=null),null==s||s(),t("change",e.target.value)}function R(t){void 0!==t&&Ba(t),null!==c&&(clearTimeout(c),c=null),null==s||s(),a=!1,i=!1,delete r.value,"file"!==e.type&&setTimeout((()=>{null!==d.value&&(d.value.value=void 0!==p.value?p.value:"")}))}function A(){return!0===r.hasOwnProperty("value")?r.value:void 0!==p.value?p.value:""}Xo((()=>e.type),(()=>{d.value&&(d.value.value=e.modelValue)})),Xo((()=>e.modelValue),(t=>{if(!0===v.value){if(!0===i&&(i=!1,String(t)===u))return;m(t)}else p.value!==t&&(p.value=t,"number"===e.type&&!0===r.hasOwnProperty("value")&&(!0===a?a=!1:delete r.value));!0===e.autogrow&&Qt(P)})),Xo((()=>e.autogrow),(e=>{!0===e?Qt(P):null!==d.value&&n.rows>0&&(d.value.style.height="auto")})),Xo((()=>e.dense),(()=>{!0===e.autogrow&&Qt(P)})),Gn((()=>{R()})),Wn((()=>{!0===e.autogrow&&P()})),Object.assign(k,{innerValue:p,fieldClass:ar((()=>"q-"+(!0===x.value?"textarea":"input")+(!0===e.autogrow?" q-textarea--autogrow":""))),hasShadow:ar((()=>"file"!==e.type&&"string"==typeof e.shadowText&&0!==e.shadowText.length)),inputRef:d,emitValue:O,hasValue:_,floatingLabel:ar((()=>!0===_.value&&("number"!==e.type||!1===isNaN(p.value))||Wc(e.displayValue))),getControl:()=>ir(!0===x.value?"textarea":"input",{ref:d,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...E.value,...C.value,..."file"!==e.type?{value:A()}:y.value}),getShadowControl:()=>ir("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(!0===x.value?"":" text-no-wrap")},[ir("span",{class:"invisible"},A()),ir("span",e.shadowText)])});const V=Yc(k);return Object.assign(o,{focus:function(){Hc((()=>{const e=document.activeElement;null===d.value||d.value===e||null!==e&&e.id===k.targetUid.value||d.value.focus({preventScroll:!0})}))},select:function(){var e;null==(e=d.value)||e.select()},getNativeElement:()=>d.value}),Ta(o,"nativeEl",(()=>d.value)),V}});var hm=Va({name:"QSpinnerIos",props:Ec,setup(e){const{cSize:t,classes:n}=Tc(e);return()=>ir("svg",{class:n.value,width:t.value,height:t.value,stroke:"currentColor",fill:"currentColor",viewBox:"0 0 64 64",innerHTML:'<g stroke-width="4" stroke-linecap="round"><line y1="17" y2="29" transform="translate(32,32) rotate(180)"><animate attributeName="stroke-opacity" dur="750ms" values="1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(210)"><animate attributeName="stroke-opacity" dur="750ms" values="0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(240)"><animate attributeName="stroke-opacity" dur="750ms" values=".1;0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(270)"><animate attributeName="stroke-opacity" dur="750ms" values=".15;.1;0;1;.85;.7;.65;.55;.45;.35;.25;.15" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(300)"><animate attributeName="stroke-opacity" dur="750ms" values=".25;.15;.1;0;1;.85;.7;.65;.55;.45;.35;.25" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(330)"><animate attributeName="stroke-opacity" dur="750ms" values=".35;.25;.15;.1;0;1;.85;.7;.65;.55;.45;.35" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(0)"><animate attributeName="stroke-opacity" dur="750ms" values=".45;.35;.25;.15;.1;0;1;.85;.7;.65;.55;.45" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(30)"><animate attributeName="stroke-opacity" dur="750ms" values=".55;.45;.35;.25;.15;.1;0;1;.85;.7;.65;.55" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(60)"><animate attributeName="stroke-opacity" dur="750ms" values=".65;.55;.45;.35;.25;.15;.1;0;1;.85;.7;.65" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(90)"><animate attributeName="stroke-opacity" dur="750ms" values=".7;.65;.55;.45;.35;.25;.15;.1;0;1;.85;.7" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(120)"><animate attributeName="stroke-opacity" dur="750ms" values=".85;.7;.65;.55;.45;.35;.25;.15;.1;0;1;.85" repeatCount="indefinite"></animate></line><line y1="17" y2="29" transform="translate(32,32) rotate(150)"><animate attributeName="stroke-opacity" dur="750ms" values="1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1" repeatCount="indefinite"></animate></line></g>'})}});const mm={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},gm=Object.keys(mm),bm={type:String,validator:e=>gm.includes(e)};const ym={none:0,xs:4,sm:8,md:16,lg:24,xl:32},_m={xs:8,sm:10,md:14,lg:20,xl:24},wm=["button","submit","reset"],km=/[^\s]\/[^\s]/;const xm={...{...Ru,...kc,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...["flat","outline","push","unelevated"].reduce(((e,t)=>(e[t]=Boolean)&&e),{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...bm,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},round:Boolean};function Sm(e){const t=Au(e,_m),n=function(e){return ar((()=>{const t=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${mm[t]}`}))}(e),{hasRouterLink:o,hasLink:l,linkTag:r,linkAttrs:a,navigateOnClick:i}=Sc({fallbackTag:"button"}),s=ar((()=>{const n=!1===e.fab&&!1===e.fabMini?t.value:{};return void 0!==e.padding?Object.assign({},n,{padding:e.padding.split(/\s+/).map((e=>e in ym?ym[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):n})),u=ar((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),c=ar((()=>!0!==e.disable&&!0!==e.loading)),d=ar((()=>!0===c.value?e.tabindex||0:-1)),f=ar((()=>function(e,t){return!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":t}(e,"standard"))),p=ar((()=>{const t={tabindex:d.value};return!0===l.value?Object.assign(t,a.value):!0===wm.includes(e.type)&&(t.type=e.type),"a"===r.value?(!0===e.disable?t["aria-disabled"]="true":void 0===t.href&&(t.role="button"),!0!==o.value&&!0===km.test(e.type)&&(t.type=e.type)):!0===e.disable&&(t.disabled="",t["aria-disabled"]="true"),!0===e.loading&&void 0!==e.percentage&&Object.assign(t,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),t}));return{classes:ar((()=>{let t;void 0!==e.color?t=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(t=`text-${e.textColor}`);const n=!0===e.round?"round":"rectangle"+(!0===u.value?" q-btn--rounded":!0===e.square?" q-btn--square":"");return`q-btn--${f.value} q-btn--${n}`+(void 0!==t?" "+t:"")+(!0===c.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")+(e.square?" q-btn--square":"")})),style:s,innerClasses:ar((()=>n.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":""))),attributes:p,hasLink:l,linkTag:r,navigateOnClick:i,isActionable:c}}const{passiveCapture:Cm}=Ia;let Em=null,Tm=null,Lm=null;var qm=Va({name:"QBtn",props:{...xm,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:o}=Ql(),{classes:l,style:r,innerClasses:a,attributes:i,hasLink:s,linkTag:u,navigateOnClick:c,isActionable:d}=Sm(e),f=Ct(null),p=Ct(null);let v,h=null,m=null;const g=ar((()=>void 0!==e.label&&null!==e.label&&""!==e.label)),b=ar((()=>!0!==e.disable&&!1!==e.ripple&&{keyCodes:!0===s.value?[13,32]:[13],...!0===e.ripple?{}:e.ripple})),y=ar((()=>({center:e.round}))),_=ar((()=>{const t=Math.max(0,Math.min(100,e.percentage));return t>0?{transition:"transform 0.6s",transform:`translateX(${t-100}%)`}:{}})),w=ar((()=>{if(!0===e.loading)return{onMousedown:q,onTouchstart:q,onClick:q,onKeydown:q,onKeyup:q};if(!0===d.value){const t={onClick:x,onKeydown:S,onMousedown:E};if(!0===o.$q.platform.has.touch){t[`onTouchstart${void 0!==e.onTouchstart?"":"Passive"}`]=C}return t}return{onClick:Da}})),k=ar((()=>({ref:f,class:"q-btn q-btn-item non-selectable no-outline "+l.value,style:r.value,...i.value,...w.value})));function x(t){if(null!==f.value){if(void 0!==t){if(!0===t.defaultPrevented)return;const n=document.activeElement;if("submit"===e.type&&n!==document.body&&!1===f.value.contains(n)&&!1===n.contains(f.value)){!0!==t.qAvoidFocus&&f.value.focus();const e=()=>{var t;document.removeEventListener("keydown",Da,!0),document.removeEventListener("keyup",e,Cm),null==(t=f.value)||t.removeEventListener("blur",e,Cm)};document.addEventListener("keydown",Da,!0),document.addEventListener("keyup",e,Cm),f.value.addEventListener("blur",e,Cm)}}c(t)}}function S(e){null!==f.value&&(n("keydown",e),!0===ei(e,[13,32])&&Tm!==f.value&&(null!==Tm&&L(),!0!==e.defaultPrevented&&(!0!==e.qAvoidFocus&&f.value.focus(),Tm=f.value,f.value.classList.add("q-btn--active"),document.addEventListener("keyup",T,!0),f.value.addEventListener("blur",T,Cm)),Da(e)))}function C(e){null!==f.value&&(n("touchstart",e),!0!==e.defaultPrevented&&(Em!==f.value&&(null!==Em&&L(),Em=f.value,h=e.target,h.addEventListener("touchcancel",T,Cm),h.addEventListener("touchend",T,Cm)),v=!0,null!==m&&clearTimeout(m),m=setTimeout((()=>{m=null,v=!1}),200)))}function E(e){null!==f.value&&(e.qSkipRipple=!0===v,n("mousedown",e),!0!==e.defaultPrevented&&Lm!==f.value&&(null!==Lm&&L(),Lm=f.value,f.value.classList.add("q-btn--active"),document.addEventListener("mouseup",T,Cm)))}function T(e){if(null!==f.value&&("blur"!==(null==e?void 0:e.type)||document.activeElement!==f.value)){if("keyup"===(null==e?void 0:e.type)){if(Tm===f.value&&!0===ei(e,[13,32])){const t=new MouseEvent("click",e);t.qKeyEvent=!0,!0===e.defaultPrevented&&za(t),!0===e.cancelBubble&&Ba(t),f.value.dispatchEvent(t),Da(e),e.qKeyEvent=!0}n("keyup",e)}L()}}function L(e){var t,n;const o=p.value;!0===e||Em!==f.value&&Lm!==f.value||null===o||o===document.activeElement||(o.setAttribute("tabindex",-1),o.focus()),Em===f.value&&(null!==h&&(h.removeEventListener("touchcancel",T,Cm),h.removeEventListener("touchend",T,Cm)),Em=h=null),Lm===f.value&&(document.removeEventListener("mouseup",T,Cm),Lm=null),Tm===f.value&&(document.removeEventListener("keyup",T,!0),null==(t=f.value)||t.removeEventListener("blur",T,Cm),Tm=null),null==(n=f.value)||n.classList.remove("q-btn--active")}function q(e){Da(e),e.qSkipRipple=!0}return Gn((()=>{L(!0)})),Object.assign(o,{click:e=>{!0===d.value&&x(e)}}),()=>{let n=[];void 0!==e.icon&&n.push(ir(Gu,{name:e.icon,left:!0!==e.stack&&!0===g.value,role:"img"})),!0===g.value&&n.push(ir("span",{class:"block"},[e.label])),n=pu(t.default,n),void 0!==e.iconRight&&!1===e.round&&n.push(ir(Gu,{name:e.iconRight,right:!0!==e.stack&&!0===g.value,role:"img"}));const o=[ir("span",{class:"q-focus-helper",ref:p})];return!0===e.loading&&void 0!==e.percentage&&o.push(ir("span",{class:"q-btn__progress absolute-full overflow-hidden"+(!0===e.darkPercentage?" q-btn__progress--dark":"")},[ir("span",{class:"q-btn__progress-indicator fit block",style:_.value})])),o.push(ir("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+a.value},n)),null!==e.loading&&o.push(ir(yr,{name:"q-transition--fade"},(()=>!0===e.loading?[ir("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==t.loading?t.loading():[ir(Lc)])]:null))),an(ir(u.value,k.value,o),[[td,b.value,void 0,y.value]])}}});const Om=["ul","ol"];var Pm,Fm=Va({name:"QList",props:{...Yu,bordered:Boolean,dense:Boolean,separator:Boolean,padding:Boolean,tag:{type:String,default:"div"}},setup(e,{slots:t}){const n=Ql(),o=Ju(e,n.proxy.$q),l=ar((()=>Om.includes(e.tag)?null:"list")),r=ar((()=>"q-list"+(!0===e.bordered?" q-list--bordered":"")+(!0===e.dense?" q-list--dense":"")+(!0===e.separator?" q-list--separator":"")+(!0===o.value?" q-list--dark":"")+(!0===e.padding?" q-list--padding":"")));return()=>ir(e.tag,{class:r.value,role:l.value},du(t.default))}}),Rm={exports:{}};
/*!
 * js-logger - http://github.com/jonnyreeves/js-logger
 * Jonny Reeves, http://jonnyreeves.co.uk/
 * js-logger may be freely distributed under the MIT license.
 */
Pm=Rm,function(e){var t,n={VERSION:"1.6.1"},o={},l=function(e,t){return function(){return t.apply(e,arguments)}},r=function(){var e,t,n=arguments,o=n[0];for(t=1;t<n.length;t++)for(e in n[t])!(e in o)&&n[t].hasOwnProperty(e)&&(o[e]=n[t][e]);return o},a=function(e,t){return{value:e,name:t}};n.TRACE=a(1,"TRACE"),n.DEBUG=a(2,"DEBUG"),n.INFO=a(3,"INFO"),n.TIME=a(4,"TIME"),n.WARN=a(5,"WARN"),n.ERROR=a(8,"ERROR"),n.OFF=a(99,"OFF");var i=function(e){this.context=e,this.setLevel(e.filterLevel),this.log=this.info};i.prototype={setLevel:function(e){e&&"value"in e&&(this.context.filterLevel=e)},getLevel:function(){return this.context.filterLevel},enabledFor:function(e){var t=this.context.filterLevel;return e.value>=t.value},trace:function(){this.invoke(n.TRACE,arguments)},debug:function(){this.invoke(n.DEBUG,arguments)},info:function(){this.invoke(n.INFO,arguments)},warn:function(){this.invoke(n.WARN,arguments)},error:function(){this.invoke(n.ERROR,arguments)},time:function(e){"string"==typeof e&&e.length>0&&this.invoke(n.TIME,[e,"start"])},timeEnd:function(e){"string"==typeof e&&e.length>0&&this.invoke(n.TIME,[e,"end"])},invoke:function(e,n){t&&this.enabledFor(e)&&t(n,r({level:e},this.context))}};var s,u=new i({filterLevel:n.OFF});(s=n).enabledFor=l(u,u.enabledFor),s.trace=l(u,u.trace),s.debug=l(u,u.debug),s.time=l(u,u.time),s.timeEnd=l(u,u.timeEnd),s.info=l(u,u.info),s.warn=l(u,u.warn),s.error=l(u,u.error),s.log=s.info,n.setHandler=function(e){t=e},n.setLevel=function(e){for(var t in u.setLevel(e),o)o.hasOwnProperty(t)&&o[t].setLevel(e)},n.getLevel=function(){return u.getLevel()},n.get=function(e){return o[e]||(o[e]=new i(r({name:e},u.context)))},n.createDefaultHandler=function(e){(e=e||{}).formatter=e.formatter||function(e,t){t.name&&e.unshift("["+t.name+"]")};var t={},o=function(e,t){Function.prototype.apply.call(e,console,t)};return"undefined"==typeof console?function(){}:function(l,r){l=Array.prototype.slice.call(l);var a,i=console.log;r.level===n.TIME?(a=(r.name?"["+r.name+"] ":"")+l[0],"start"===l[1]?console.time?console.time(a):t[a]=(new Date).getTime():console.timeEnd?console.timeEnd(a):o(i,[a+": "+((new Date).getTime()-t[a])+"ms"])):(r.level===n.WARN&&console.warn?i=console.warn:r.level===n.ERROR&&console.error?i=console.error:r.level===n.INFO&&console.info?i=console.info:r.level===n.DEBUG&&console.debug?i=console.debug:r.level===n.TRACE&&console.trace&&(i=console.trace),e.formatter(l,r),o(i,l))}},n.useDefaults=function(e){n.setLevel(e&&e.defaultLevel||n.DEBUG),n.setHandler(n.createDefaultHandler(e))},n.setDefaults=n.useDefaults,Pm.exports?Pm.exports=n:(n._prevLogger=e.Logger,n.noConflict=function(){return e.Logger=n._prevLogger,n},e.Logger=n)}(sa);var Am=Rm.exports;const Vm={telegram:"https://t.me/+2S60dFyiS4VmNzc1",discord:"https://discord.gg/wQKktx9gar",dashboard:"https://dashboard.captchaai.io",addFunds:"https://dashboard.capsolver.com/overview/add-funds",policy:"",getKey:"https://dashboard.capsolver.com/dashboard/overview",guide:"http://www.capsolver.com/",callbackInstructions:"https://docs.capsolver.com/en/guide/extension/introductions/#36-how-to-use-callback",img2textIntroduce:"https://docs.capsolver.com/en/guide/extension/image-to-text/",funCaptchaUnsolved:"https://www.capsolver.com/blog/FunCaptcha/certain-FunCaptcha-images-unsolved-solution"};class Mm{constructor(e){n(this,"baseURL"),this.baseURL=e}async post(e,t,n){const o=await fetch(this.getURL(e),{method:"POST",body:JSON.stringify(t),headers:{"Content-Type":"application/json"},...n});return{status:o.status,statusText:o.statusText,data:await o.json(),headers:o.headers}}getURL(e){return this.baseURL+e}}class Im{constructor(e){n(this,"options",{apiKey:"",service:"https://api.capsolver.com",defaultTimeout:120,pollingInterval:5,recaptchaTimeout:600}),n(this,"http");for(let t in this.options)this.options[t]=void 0===e[t]?this.options[t]:e[t];this.http=new Mm(this.options.service)}static async API(e){const t=await fc.getAll();if(!(null==e?void 0:e.apiKey)&&!(null==t?void 0:t.apiKey))throw new Error("Capsover: No API Kye set up yet!");return new Im({apiKey:t.apiKey,...e})}async getProxyParams(e){const t=await fc.getAll();return{proxyType:t.proxyType,proxyAddress:t.hostOrIp,proxyPort:t.port,proxyLogin:t.proxyLogin,proxyPassword:t.proxyPassword,type:e.type.replace("ProxyLess","")}}async getBalance(){var e,t,n;const o=await this.http.post("/getBalance",{clientKey:this.options.apiKey});if(200!==o.status||(null==(e=o.data)?void 0:e.errorCode)||(null==(t=o.data)?void 0:t.errorId))throw new Error((null==(n=o.data)?void 0:n.errorDescription)||"createTask fail！");return o.data}async createTaskResult(e,t){t||(t={timeout:this.options.defaultTimeout,pollingInterval:this.options.pollingInterval});const n=await fc.getAll();if(n.appId&&(e.appId=n.appId),n.useProxy){const t=await this.getProxyParams(e.task);Object.assign(e.task,t)}const o=await this.createTask(e),{taskId:l}=o;let r=this.getTime(),a=void 0===t.timeout?this.options.defaultTimeout:t.timeout,i=void 0===t.pollingInterval?this.options.pollingInterval:t.pollingInterval;for(;!(this.getTime()-r>a);){await new Promise((e=>setTimeout(e,1e3*i)));const e=await this.getTaskSolution({taskId:l});if("ready"===e.status)return e}throw new Error("Timeout "+a+" seconds reached")}async createTask(e){var t,n,o,l;const r=null!=(t=globalThis.browser)?t:globalThis.chrome,a=await r.storage.local.get("platform"),i=await r.storage.local.get("version"),s=await this.http.post("/createTask",{clientKey:this.options.apiKey,source:a.platform,version:i.version,...e});if(200!==s.status||(null==(n=s.data)?void 0:n.errorCode)||(null==(o=s.data)?void 0:o.errorId))throw new Error((null==(l=s.data)?void 0:l.errorCode)||"createTask fail！");if(!s.data.taskId)throw new Error("taskIs is empty!");return s.data}async getTaskSolution({taskId:e}){var t,n,o;const l=await this.http.post("/getTaskResult",{clientKey:this.options.apiKey,taskId:e});if(200!==l.status||(null==(t=l.data)?void 0:t.errorCode)||(null==(n=l.data)?void 0:n.errorId))throw new Error((null==(o=l.data)?void 0:o.errorCode)||"getTaskResult fail！");return l.data}async createRecognitionTask(e){var t,n,o,l;const r=await fc.getAll(),a=null!=(t=globalThis.browser)?t:globalThis.chrome,i=await a.storage.local.get("platform"),s=await a.storage.local.get("version");r.appId&&(e.appId=r.appId);const u=await this.http.post("/createTask",{clientKey:this.options.apiKey,source:i.platform,version:s.version,...e});if(200!==u.status||(null==(n=u.data)?void 0:n.errorCode)||0!==(null==(o=u.data)?void 0:o.errorId))throw new Error((null==(l=u.data)?void 0:l.errorCode)||"createTask fail！");if(!u.data.taskId)throw new Error("taskIs is empty!");return u.data}getTime(){return parseInt(String(Date.now()/1e3))}}const Nm=[{label:"ReCaptcha v2",key:"reCaptcha",enabledName:"enabledForRecaptcha",disabled:!1,isCollapse:!0,repeatTimes:!0,delayTime:!0},{label:"ReCaptcha v3",key:"reCaptcha3",enabledName:"enabledForRecaptchaV3",disabled:!1,isCollapse:!1,captchaMode:"onlyToken",supportSelectType:!1,supportTypeList:["ReCaptchaV3TaskProxyLess","ReCaptchaV3M1TaskProxyLess"]},{label:"Text Captcha",key:"textCaptcha",enabledName:"enabledForImageToText",disabled:!1,captchaMode:"onlyClick",questionIntroduce:!0,questionIntroduceLink:Vm.img2textIntroduce},{label:"AWS Captcha",key:"aws",enabledName:"enabledForAwsCaptcha",disabled:!1,isCollapse:!1,captchaMode:"onlyClick"},{label:"Cloudflare Turnstile",key:"cloudflare",enabledName:"enabledForCloudflare",disabled:!1,captchaMode:"onlyToken"},{label:"GeeTest",key:"geetest",enabledName:"enabledForGeetestV4",disabled:!0,isCollapse:!1,captchaMode:"comingSoon"}];var $m=ki(An({__name:"Message",props:{type:{},message:{},duration:{}},setup(e,{expose:t}){const n=e,o=Ct(!0);return t({close:function(e,t){let l=setTimeout((()=>{o.value=!1;let n=setTimeout((()=>{e.removeChild(t),clearTimeout(l),clearTimeout(n),l=null,n=null}),500)}),n.duration)}}),(e,t)=>(Sl(),ql("div",{class:G(["capsolver-message",{"capsolver-message--close":!o.value}])},[t[0]||(t[0]=Vl("img",{src:"assets/success.42815aad.svg",alt:""},null,-1)),Vl("span",null,X(e.message),1)],2))}}),[["__scopeId","data-v-d6001888"]]);function Bm(e,t){const n=ir($m,e);return((...e)=>{ta().render(...e)})(n,t),n}function zm(e){const t=document.body,n=function(){const e=document.createElement("div");return e.classList.add("capsolver-message-container"),e}(),o={vNode:Bm(e,n),container:n};t.appendChild(n),o.vNode.component.exposed.close(t,n)}const Dm={class:"link"},jm=["href","target"];var Um=ki(An({__name:"Link",props:{href:{},target:{default:"_blank"},refresh:{type:Boolean}},setup(e){const t=e,n=ar((()=>{var e,n;return(null==(e=t.href)?void 0:e.toLowerCase().includes("http://"))||(null==(n=t.href)?void 0:n.toLowerCase().includes("https://"))})),o=Oo(Gs);function l(e){t.refresh&&(e.preventDefault(),o.go(0))}return(e,t)=>{const o=to("router-link");return Sl(),ql("div",Dm,[e.href?(Sl(),ql(bl,{key:0},[n.value||e.refresh?(Sl(),ql("a",{key:0,class:"row items-center",href:e.href,target:e.target,onClick:Zr(l,["stop"])},[ro(e.$slots,"default",{},void 0,!0)],8,jm)):(Sl(),Ol(o,{key:1,to:e.href},{default:rn((()=>[ro(e.$slots,"default",{},void 0,!0)])),_:3},8,["to"]))],64)):ro(e.$slots,"default",{key:1},void 0,!0)])}}}),[["__scopeId","data-v-a62aa848"]]);const Hm={class:"cap-collapse"},Wm=An({__name:"CapsolverCollapse",props:{collapse:{type:Boolean}},setup:e=>(e,t)=>an((Sl(),ql("div",Hm,[ro(e.$slots,"default")],512)),[[Rr,e.collapse]])});var Km=Va({name:"QCheckbox",props:tc,emits:nc,setup(e){const t=ir("div",{key:"svg",class:"q-checkbox__bg absolute"},[ir("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[ir("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),ir("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]);return oc("checkbox",(function(n,o){const l=ar((()=>(!0===n.value?e.checkedIcon:!0===o.value?e.indeterminateIcon:e.uncheckedIcon)||null));return()=>null!==l.value?[ir("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[ir(Gu,{class:"q-checkbox__icon",name:l.value})])]:[t]}))}});const Qm={class:"cap-radio"};var Gm=ki(An({__name:"CapsolverRadio",props:{mode:{}},emits:["update:mode","modeChange"],setup(e,{emit:t}){const n=t;function o(e){n("update:mode",e),n("modeChange")}return(e,t)=>(Sl(),ql("div",Qm,[Vl("div",{class:G(["cap-radio--item",{active:"token"===e.mode}]),onClick:t[0]||(t[0]=e=>o("token"))},t[2]||(t[2]=[Vl("span",null,"Token",-1)]),2),Vl("div",{class:G(["cap-radio--item",{active:"click"===e.mode}]),onClick:t[1]||(t[1]=e=>o("click"))},t[3]||(t[3]=[Vl("span",null,"Click",-1)]),2)]))}}),[["__scopeId","data-v-153fad5c"]]);const Ym=["src"];var Jm=ki(An({__name:"CapsolverArrow",props:{up:{type:Boolean}},emits:["update:up"],setup(e,{emit:t}){const n=e,o=t;function l(){o("update:up",!n.up)}return(e,t)=>(Sl(),ql("div",{class:G(["captcha-arrow",{"captcha-arrow--up":e.up}]),onClick:l},[Vl("img",{src:qt("assets/arrow.1ab57550.svg"),alt:""},null,8,Ym)],2))}}),[["__scopeId","data-v-147a886e"]]),Zm="assets/reCaptcha.63436d93.svg";const Xm={class:"captcha-checkbox"},eg=["src"],tg={class:"captcha-name"},ng={class:"row items-center justify-end"},og={class:"only-click"},lg={key:0,class:"row items-center"},rg={key:2,class:"w-placeholder"},ag={key:0,class:"captcha-collapse-item"},ig={key:1,class:"captcha-collapse-item"},sg={key:2,class:"captcha-collapse-item solver-type"};var ug=ki(An({__name:"CaptchaItem",props:{captcha:{},captchaName:{},enabledForCaptcha:{},label:{},disabled:{type:Boolean,default:!1},repeatTimes:{type:Boolean,default:!1},delayTime:{type:Boolean,default:!1},isCollapse:{type:Boolean,default:!1},onlyClick:{type:Boolean,default:!1},onlyToken:{type:Boolean,default:!1},comingSoon:{type:Boolean,default:!1},captchaMode:{default:"radio"},supportSelectType:{type:Boolean,default:!1},supportTypeList:{}},emits:["update:captcha","captchaChange"],setup(e,{emit:t}){const{t:n}=Uh(),o=ft({geetest:"assets/geetest.5dfc422c.svg",reCaptcha:Zm,hCaptcha:"assets/hCaptcha.0406a4eb.svg",funCaptcha:"assets/funCaptcha.4f6d4ba4.svg",textCaptcha:"assets/textToImage.8dbe0bf9.svg",reCaptcha3:Zm,cloudflare:"assets/cloudflare.a164bb78.svg",datadome:"assets/dataDome.047813e4.svg",aws:"assets/aws.08ef8f27.svg"}),l=e,r=t,a=Ct(),i=Ct(!1),s=ar((()=>!["onlyClick","onlyToken","comingSoon"].includes(l.captchaMode)));var u;el((()=>{a.value=l.captcha}),null,u);const c=ar((()=>{let e="";switch(l.captchaMode){case"radio":default:e="";break;case"onlyClick":e="Only Click";break;case"onlyToken":e="Only Token";break;case"comingSoon":e="Coming Soon"}return e}));function d(){r("update:captcha",a.value),r("captchaChange")}function f(e){a.value[e]=Math.floor(a.value[e]),Number(a.value[e])<0&&(a.value[e]=0),d(),r("captchaChange")}function p(){d()}return(e,t)=>(Sl(),ql("div",{class:G(["captcha-container",{"captcha-coming-soon":"comingSoon"===e.captchaMode}])},[Ml(Cc,null,{default:rn((()=>[Vl("div",Xm,[Ml(Km,{modelValue:a.value[e.enabledForCaptcha],"onUpdate:modelValue":[t[0]||(t[0]=t=>a.value[e.enabledForCaptcha]=t),d],disable:e.disabled},null,8,["modelValue","disable"])]),Ml(pc,null,{default:rn((()=>[Ml(vc,{class:"captcha-label-item"},{default:rn((()=>[Vl("img",{class:"captcha-logo",src:o[e.captchaName],alt:""},null,8,eg),Vl("span",tg,X(e.label),1),ro(e.$slots,"tip",{},void 0,!0)])),_:3})])),_:3}),Vl("div",ng,[Vl("div",og,[Vl("span",null,X(c.value),1)]),e.isCollapse?(Sl(),ql("div",lg,[s.value?(Sl(),Ol(Gm,{key:0,mode:a.value[`${e.captchaName}Mode`],"onUpdate:mode":t[1]||(t[1]=t=>a.value[`${e.captchaName}Mode`]=t),onModeChange:d},null,8,["mode"])):$l("",!0),e.isCollapse?(Sl(),Ol(Jm,{key:1,up:i.value,"onUpdate:up":t[2]||(t[2]=e=>i.value=e)},null,8,["up"])):(Sl(),ql("div",rg))])):$l("",!0)])])),_:3}),e.isCollapse?(Sl(),Ol(Wm,{key:0,collapse:i.value},{default:rn((()=>[e.delayTime?(Sl(),ql("div",ag,[Vl("span",null,X(qt(n)("delay")),1),Ml(vm,{modelValue:a.value[`${e.captchaName}DelayTime`],"onUpdate:modelValue":t[3]||(t[3]=t=>a.value[`${e.captchaName}DelayTime`]=t),outlined:"",type:"number",onBlur:t[4]||(t[4]=()=>{f(`${e.captchaName}DelayTime`)})},null,8,["modelValue"])])):$l("",!0),e.repeatTimes?(Sl(),ql("div",ig,[Vl("span",null,X(qt(n)("repeat")),1),Ml(vm,{modelValue:a.value[`${e.captchaName}RepeatTimes`],"onUpdate:modelValue":t[5]||(t[5]=t=>a.value[`${e.captchaName}RepeatTimes`]=t),outlined:"",type:"number",onBlur:t[6]||(t[6]=()=>{f(`${e.captchaName}RepeatTimes`)})},null,8,["modelValue"])])):$l("",!0),e.supportSelectType?(Sl(),ql("div",sg,[Vl("span",null,X(qt(n)("taskType")),1),Ml(qf,{outlined:"",modelValue:a.value[`${e.captchaName}TaskType`],"onUpdate:modelValue":[t[7]||(t[7]=t=>a.value[`${e.captchaName}TaskType`]=t),p],options:e.supportTypeList},null,8,["modelValue","options"])])):$l("",!0),ro(e.$slots,"collapse",{},void 0,!0)])),_:3},8,["collapse"])):$l("",!0)],2))}}),[["__scopeId","data-v-43531eaa"]]);const cg={class:"mt12 capsolver-card"},dg={class:"api-title"},fg={class:"row items-center mt16"},pg={class:"text-title"},vg={key:1,class:"text-balance ml12"},hg={class:"mt12 capsolver-card"},mg={class:"text-title"},gg={key:1,class:"captcha-beta"},bg={class:"mt12 captcha-settings capsolver-card"},yg={class:"text-title"},_g={class:"setting-item"},wg={class:"setting-item"},kg={class:"captcha-collapse-item"},xg={class:"captcha-proxy-type"},Sg={class:"captcha-proxy-host"},Cg={class:"captcha-proxy-port"},Eg={class:"captcha-collapse-item"},Tg={class:"captcha-proxy-login"},Lg={class:"captcha-proxy-password"},qg={class:"setting-item"},Og={class:"captcha-collapse-item"},Pg={style:{color:"#999"}},Fg={class:"captcha-black-list mb16"},Rg={class:"captcha-black-url"},Ag=["onClick"],Vg={class:"setting-item callback-fn"},Mg={class:"mt12 captcha-footer"},Ig={class:"captcha-support"},Ng={class:"version"};var $g=ki(An({__name:"Config",async setup(e){let t,n;const{t:o,locale:l}=Uh(),r=([t,n]=function(e){const t=Ql();let n=e();return Zl(),S(n)&&(n=n.catch((e=>{throw Jl(t),e}))),[n,()=>Jl(t)]}((()=>fc.getAll())),t=await t,n(),t),a=Ct(r),{captchaList:i}={captchaList:Nm};async function s(){await fc.set(_t(a.value))}const u=Ct((null==r?void 0:r.apiKey)||""),c=Ct(!1),d=Ct(""),f=Ct(""),p=Ct({balance:0,packages:[]});async function v(){if(a.value.apiKey!==u.value){if(a.value.apiKey=u.value,c.value=!0,await s(),!a.value.apiKey)return p.value={balance:0,packages:[]},a.value=dc,void(c.value=!1);await h(),c.value=!1}}async function h(){c.value=!0;try{const e=await Im.API();p.value=await e.getBalance(),Am.info("balance: ",p.value)}catch(hy){Am.error(hy)}finally{c.value=!1}}async function m(){d.value&&(a.value.blackUrlList.unshift(d.value),await s(),d.value="")}function g(){window.close()}function b(){0!==u.value.length&&(navigator.clipboard.writeText(u.value),zm({type:"success",message:o("copySuccess"),duration:2e3}))}async function y(e,t){chrome.runtime.sendMessage({action:"ga",key:e}),window.open(t)}return Wn((()=>{h(),async function(){var e;const t=null!=(e=globalThis.browser)?e:globalThis.chrome,n=await t.storage.local.get("version");f.value=n.version||""}()})),(e,t)=>{var n;return Sl(),ql(bl,null,[Vl("div",cg,[Vl("div",dg,[t[21]||(t[21]=Vl("div",{class:"text-title"},[Vl("img",{src:"assets/key.201fc3f4.svg",alt:""}),Vl("span",null,"API Key")],-1)),Vl("span",{class:"text-primary text-out-link",onClick:t[0]||(t[0]=e=>y("ExtensionGetKey",qt(Vm).getKey))},X(qt(o)("getKey")),1)]),Ml(vm,{modelValue:u.value,"onUpdate:modelValue":t[1]||(t[1]=e=>u.value=e),outlined:"",class:"mt8 api-key",placeholder:qt(o)("inputKey"),onBlur:t[2]||(t[2]=()=>{v()})},{append:rn((()=>[Vl("img",{src:"assets/copy.b3d46815.svg",class:G(["copy-key",{"copy-key--drop":0===u.value.length}]),onClick:b,alt:""},null,2)])),_:1},8,["modelValue","placeholder"]),Vl("div",fg,[Vl("div",pg,[t[22]||(t[22]=Vl("img",{src:"assets/balance.ec909fe5.svg",alt:""},null,-1)),Vl("span",null,X(qt(o)("balance"))+":",1),c.value?(Sl(),Ol(hm,{key:0,color:"primary",class:"ml12"})):(Sl(),ql("span",vg,"$"+X(((null==(n=p.value)?void 0:n.balance)||0).toFixed(4)),1))]),Ml(Pu),Ml(qm,{class:"btn-primary","no-caps":"",unelevated:"",onClick:t[3]||(t[3]=e=>y("ExtensionAddCredits",qt(Vm).addFunds))},{default:rn((()=>[Nl("+ "+X(qt(o)("addFounds")),1)])),_:1})])]),Vl("div",hg,[Vl("div",mg,[t[23]||(t[23]=Vl("img",{src:"assets/lock.8b188c3a.svg",alt:""},null,-1)),Vl("span",null,X(qt(o)("enabled")),1)]),Ml(Fm,{dense:"",class:"m-list"},{default:rn((()=>[(Sl(!0),ql(bl,null,lo(qt(i),(e=>(Sl(),Ol(ug,{key:e.key,captcha:a.value,"onUpdate:captcha":t[4]||(t[4]=e=>a.value=e),label:e.label,"captcha-name":e.key,"enabled-for-captcha":e.enabledName,disabled:e.disabled,"is-collapse":e.isCollapse,"captcha-mode":e.captchaMode,"delay-time":e.delayTime,"repeat-times":e.repeatTimes,"support-select-type":e.supportSelectType,"support-type-list":e.supportTypeList,onCaptchaChange:s},{tip:rn((()=>[e.questionIntroduce?(Sl(),Ol(Um,{key:0,href:e.questionIntroduceLink},{default:rn((()=>t[24]||(t[24]=[Vl("img",{src:"assets/question.6085c9ed.svg",alt:""},null,-1)]))),_:2},1032,["href"])):$l("",!0),e.isBeta?(Sl(),ql("span",gg,"Beta")):$l("",!0),e.unsolved?(Sl(),Ol(Um,{key:2,href:e.questionIntroduceLink},{default:rn((()=>t[25]||(t[25]=[Vl("span",{class:"captcha-unsolved"},"Unsolved Solution",-1)]))),_:2},1032,["href"])):$l("",!0)])),_:2},1032,["captcha","label","captcha-name","enabled-for-captcha","disabled","is-collapse","captcha-mode","delay-time","repeat-times","support-select-type","support-type-list"])))),128))])),_:1})]),Vl("div",bg,[Vl("div",yg,[t[26]||(t[26]=Vl("img",{src:"assets/settings.8bf367a7.svg",alt:""},null,-1)),Vl("span",null,X(qt(o)("setting")),1)]),Ml(Fm,{dense:"",class:"m-list"},{default:rn((()=>[Vl("div",_g,[Ml(Cc,null,{default:rn((()=>[Ml(pc,null,{default:rn((()=>[Ml(vc,null,{default:rn((()=>[Nl(X(qt(o)("manualSolving")),1)])),_:1})])),_:1}),Ml(pc,{side:""},{default:rn((()=>[Ml(lc,{modelValue:a.value.manualSolving,"onUpdate:modelValue":[t[5]||(t[5]=e=>a.value.manualSolving=e),s]},null,8,["modelValue"])])),_:1})])),_:1})]),Vl("div",wg,[Ml(Cc,null,{default:rn((()=>[Ml(pc,null,{default:rn((()=>[Ml(vc,null,{default:rn((()=>[Nl(X(qt(o)("proxy")),1)])),_:1})])),_:1}),Ml(pc,{side:""},{default:rn((()=>[Ml(lc,{modelValue:a.value.useProxy,"onUpdate:modelValue":[t[6]||(t[6]=e=>a.value.useProxy=e),s]},null,8,["modelValue"])])),_:1})])),_:1}),Ml(Wm,{collapse:a.value.useProxy},{default:rn((()=>[Vl("div",kg,[Vl("div",xg,[Vl("span",null,X(qt(o)("proxyType")),1),Ml(qf,{outlined:"",modelValue:a.value.proxyType,"onUpdate:modelValue":[t[7]||(t[7]=e=>a.value.proxyType=e),s],options:["http","https","socks4","socks5"]},null,8,["modelValue"])]),Vl("div",Sg,[t[27]||(t[27]=Vl("span",null,"IP/Host",-1)),Ml(vm,{modelValue:a.value.hostOrIp,"onUpdate:modelValue":t[8]||(t[8]=e=>a.value.hostOrIp=e),outlined:"",placeholder:"Ip/Host",onBlur:t[9]||(t[9]=()=>{s()})},null,8,["modelValue"])]),Vl("div",Cg,[Vl("span",null,X(qt(o)("port")),1),Ml(vm,{modelValue:a.value.port,"onUpdate:modelValue":t[10]||(t[10]=e=>a.value.port=e),outlined:"",type:"number",placeholder:qt(o)("port"),onBlur:t[11]||(t[11]=()=>{!async function(e){a.value[e]=Math.floor(a.value[e]),Number(a.value[e])<0&&(a.value[e]="port"===e?"":0),await s()}("port")})},null,8,["modelValue","placeholder"])])]),Vl("div",Eg,[Vl("div",Tg,[Vl("span",null,X(qt(o)("login")),1),Ml(vm,{modelValue:a.value.proxyLogin,"onUpdate:modelValue":t[12]||(t[12]=e=>a.value.proxyLogin=e),outlined:"",placeholder:qt(o)("loginName"),onBlur:t[13]||(t[13]=()=>{s()})},null,8,["modelValue","placeholder"])]),Vl("div",Lg,[Vl("span",null,X(qt(o)("password")),1),Ml(vm,{modelValue:a.value.proxyPassword,"onUpdate:modelValue":t[14]||(t[14]=e=>a.value.proxyPassword=e),outlined:"",placeholder:qt(o)("password"),onBlur:t[15]||(t[15]=()=>{s()})},null,8,["modelValue","placeholder"])])])])),_:1},8,["collapse"])]),Vl("div",qg,[Ml(Cc,null,{default:rn((()=>[Ml(pc,null,{default:rn((()=>[Ml(vc,null,{default:rn((()=>[Nl(X(qt(o)("blackControl")),1)])),_:1})])),_:1}),Ml(pc,{side:""},{default:rn((()=>[Ml(lc,{modelValue:a.value.enabledForBlacklistControl,"onUpdate:modelValue":[t[16]||(t[16]=e=>a.value.enabledForBlacklistControl=e),s]},null,8,["modelValue"])])),_:1})])),_:1}),Ml(Wm,{collapse:a.value.enabledForBlacklistControl},{default:rn((()=>[Vl("div",Og,[Vl("span",Pg,X(qt(o)("blackTip")),1)]),Vl("div",{class:G(["captcha-collapse-item captcha-black",`captcha-black--${qt(l)}`])},[Ml(vm,{modelValue:d.value,"onUpdate:modelValue":t[17]||(t[17]=e=>d.value=e),outlined:"",placeholder:"https://*.example.com"},null,8,["modelValue"]),Ml(qm,{class:"btn-primary","no-caps":"",unelevated:"",onClick:m},{default:rn((()=>[Nl(X(qt(o)("add")),1)])),_:1})],2),Vl("div",Fg,[(Sl(!0),ql(bl,null,lo(a.value.blackUrlList,((e,n)=>(Sl(),ql("div",{class:"captcha-black-urls",key:e+Date.now()},[Vl("div",Rg,X(e),1),Vl("div",{class:"delete",onClick:e=>async function(e){a.value.blackUrlList.splice(e,1),await s()}(n)},t[28]||(t[28]=[Vl("img",{src:"assets/Union.e4f5e32d.svg",alt:""},null,-1)]),8,Ag)])))),128))])])),_:1},8,["collapse"])]),Vl("div",Vg,[Ml(Cc,null,{default:rn((()=>[Ml(pc,null,{default:rn((()=>[Ml(vc,null,{default:rn((()=>[Nl(X(qt(o)("solvedCallback")),1)])),_:1}),Ml(Um,{href:qt(Vm).callbackInstructions},{default:rn((()=>t[29]||(t[29]=[Vl("img",{src:"assets/question.6085c9ed.svg",alt:""},null,-1)]))),_:1},8,["href"])])),_:1}),Ml(pc,{side:""},{default:rn((()=>[Ml(vm,{modelValue:a.value.solvedCallback,"onUpdate:modelValue":t[18]||(t[18]=e=>a.value.solvedCallback=e),outlined:"",placeholder:qt(o)("solvedCallbackPlaceholder"),onBlur:t[19]||(t[19]=()=>{s()})},null,8,["modelValue","placeholder"])])),_:1})])),_:1})])])),_:1})]),Vl("div",Mg,[Vl("div",Ig,[t[30]||(t[30]=Vl("img",{src:"assets/tips.e99d9ebe.svg",alt:""},null,-1)),Vl("span",{class:"guide",onClick:t[20]||(t[20]=e=>y("ExtensionGuide",qt(Vm).guide))},X(qt(o)("guide")),1),Vl("p",Ng,"v"+X(f.value),1)]),Vl("div",null,[Ml(qm,{class:"btn-primary","no-caps":"",unelevated:"",style:{"background-color":"#fff",border:"none"},onClick:g},{default:rn((()=>[Vl("span",null,X(qt(o)("close")),1)])),_:1})])])],64)}}}),[["__scopeId","data-v-2e1eac83"]]);const Bg=An({__name:"index",setup:e=>(eo((e=>{console.error("configError: ",e)})),(e,t)=>(Sl(),Ol(pl,null,{fallback:rn((()=>t[0]||(t[0]=[Nl(" Loading... ")]))),default:rn((()=>[Ml($g)])),_:1})))});var zg=Va({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(e,{slots:t}){const n=Ql(),o=ar((()=>"q-td"+(!0===e.autoWidth?" q-table--col-auto-width":"")+(!0===e.noHover?" q-td--no-hover":"")+" "));return()=>{if(void 0===e.props)return ir("td",{class:o.value},du(t.default));const l=n.vnode.key,r=(void 0!==e.props.colsMap?e.props.colsMap[l]:null)||e.props.col;if(void 0===r)return;const{row:a}=e.props;return ir("td",{class:o.value+r.__tdClass(a),style:r.__tdStyle(a)},du(t.default))}}}),Dg=Va({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:t,emit:n}){const o=Ql(),{proxy:{$q:l}}=o,r=e=>{n("click",e)};return()=>{if(void 0===e.props)return ir("th",{class:!0===e.autoWidth?"q-table--col-auto-width":"",onClick:r},du(t.default));let n,a;const i=o.vnode.key;if(i){if(n=e.props.colsMap[i],void 0===n)return}else n=e.props.col;if(!0===n.sortable){const e="right"===n.align?"unshift":"push";a=fu(t.default,[]),a[e](ir(Gu,{class:n.__iconClass,name:l.iconSet.table.arrowUp}))}else a=du(t.default);return ir("th",{class:n.__thClass+(!0===e.autoWidth?" q-table--col-auto-width":""),style:n.headerStyle,onClick:t=>{!0===n.sortable&&e.props.sort(n),r(t)}},a)}}});const jg={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},Ug={xs:2,sm:4,md:8,lg:16,xl:24};var Hg=Va({name:"QSeparator",props:{...Yu,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=Ql(),n=Ju(e,t.proxy.$q),o=ar((()=>!0===e.vertical?"vertical":"horizontal")),l=ar((()=>` q-separator--${o.value}`)),r=ar((()=>!1!==e.inset?`${l.value}-${jg[e.inset]}`:"")),a=ar((()=>`q-separator${l.value}${r.value}`+(void 0!==e.color?` bg-${e.color}`:"")+(!0===n.value?" q-separator--dark":""))),i=ar((()=>{const t={};if(void 0!==e.size&&(t[!0===e.vertical?"width":"height"]=e.size),!1!==e.spaced){const n=!0===e.spaced?`${Ug.md}px`:e.spaced in Ug?`${Ug[e.spaced]}px`:e.spaced,o=!0===e.vertical?["Left","Right"]:["Top","Bottom"];t[`margin${o[0]}`]=t[`margin${o[1]}`]=n}return t}));return()=>ir("hr",{class:a.value,style:i.value,"aria-orientation":o.value})}});const Wg=["horizontal","vertical","cell","none"];var Kg=Va({name:"QMarkupTable",props:{...Yu,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>Wg.includes(e)}},setup(e,{slots:t}){const n=Ql(),o=Ju(e,n.proxy.$q),l=ar((()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(!0===o.value?" q-table--dark q-table__card--dark q-dark":"")+(!0===e.dense?" q-table--dense":"")+(!0===e.flat?" q-table--flat":"")+(!0===e.bordered?" q-table--bordered":"")+(!0===e.square?" q-table--square":"")+(!1===e.wrapCells?" q-table--no-wrap":"")));return()=>ir("div",{class:l.value},[ir("table",{class:"q-table"},du(t.default))])}});function Qg(e,t){return ir("div",e,[ir("table",{class:"q-table"},t)])}const Gg={list:Fm,table:Kg},Yg=["list","table","__qtable"];var Jg=Va({name:"QVirtualScroll",props:{...bf,type:{type:String,default:"list",validator:e=>Yg.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:yu},setup(e,{slots:t,attrs:n}){let o;const l=Ct(null),r=ar((()=>e.itemsSize>=0&&void 0!==e.itemsFn?parseInt(e.itemsSize,10):Array.isArray(e.items)?e.items.length:0)),{virtualScrollSliceRange:a,localResetVirtualScroll:i,padVirtualScroll:s,onVirtualScrollEvt:u}=yf({virtualScrollLength:r,getVirtualScrollTarget:function(){return o},getVirtualScrollEl:p}),c=ar((()=>{if(0===r.value)return[];const t=(e,t)=>({index:a.value.from+t,item:e});return void 0===e.itemsFn?e.items.slice(a.value.from,a.value.to).map(t):e.itemsFn(a.value.from,a.value.to-a.value.from).map(t)})),d=ar((()=>"q-virtual-scroll q-virtual-scroll"+(!0===e.virtualScrollHorizontal?"--horizontal":"--vertical")+(void 0!==e.scrollTarget?"":" scroll"))),f=ar((()=>void 0!==e.scrollTarget?{}:{tabindex:0}));function p(){return l.value.$el||l.value}function v(){o=wu(p(),e.scrollTarget),o.addEventListener("scroll",u,Ia.passive)}function h(){void 0!==o&&(o.removeEventListener("scroll",u,Ia.passive),o=void 0)}function m(){let n=s("list"===e.type?"div":"tbody",c.value.map(t.default));return void 0!==t.before&&(n=t.before().concat(n)),pu(t.after,n)}return Xo(r,(()=>{i()})),Xo((()=>e.scrollTarget),(()=>{h(),v()})),Hn((()=>{i()})),Wn((()=>{v()})),$n((()=>{v()})),Bn((()=>{h()})),Gn((()=>{h()})),()=>{if(void 0!==t.default)return"__qtable"===e.type?Qg({ref:l,class:"q-table__middle "+d.value},m()):ir(Gg[e.type],{...n,ref:l,class:[n.class,d.value],...f.value},m);console.error("QVirtualScroll: default scoped slot is required for rendering")}}});const Zg={xs:2,sm:4,md:6,lg:10,xl:14};function Xg(e,t,n){return{transform:!0===t?`translateX(${!0===n.lang.rtl?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}var eb=Va({name:"QLinearProgress",props:{...Yu,...Ru,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:t}){const{proxy:n}=Ql(),o=Ju(e,n.$q),l=Au(e,Zg),r=ar((()=>!0===e.indeterminate||!0===e.query)),a=ar((()=>e.reverse!==e.query)),i=ar((()=>({...null!==l.value?l.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`}))),s=ar((()=>"q-linear-progress"+(void 0!==e.color?` text-${e.color}`:"")+(!0===e.reverse||!0===e.query?" q-linear-progress--reverse":"")+(!0===e.rounded?" rounded-borders":""))),u=ar((()=>Xg(void 0!==e.buffer?e.buffer:1,a.value,n.$q))),c=ar((()=>`with${!0===e.instantFeedback?"out":""}-transition`)),d=ar((()=>`q-linear-progress__track absolute-full q-linear-progress__track--${c.value} q-linear-progress__track--${!0===o.value?"dark":"light"}`+(void 0!==e.trackColor?` bg-${e.trackColor}`:""))),f=ar((()=>Xg(!0===r.value?1:e.value,a.value,n.$q))),p=ar((()=>`q-linear-progress__model absolute-full q-linear-progress__model--${c.value} q-linear-progress__model--${!0===r.value?"in":""}determinate`)),v=ar((()=>({width:100*e.value+"%"}))),h=ar((()=>`q-linear-progress__stripe absolute-${!0===e.reverse?"right":"left"} q-linear-progress__stripe--${c.value}`));return()=>{const n=[ir("div",{class:d.value,style:u.value}),ir("div",{class:p.value,style:f.value})];return!0===e.stripe&&!1===r.value&&n.push(ir("div",{class:h.value,style:v.value})),ir("div",{class:s.value,style:i.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":!0===e.indeterminate?void 0:e.value},pu(t.default,n))}}});let tb=0;const nb={fullscreen:Boolean,noRouteFullscreenExit:Boolean};const ob={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>"ad"===e||"da"===e,default:"ad"}};function lb(e,t,n,o){return{columnToSort:ar((()=>{const{sortBy:e}=t.value;return e&&n.value.find((t=>t.name===e))||null})),computedSortMethod:ar((()=>void 0!==e.sortMethod?e.sortMethod:(e,t,o)=>{const l=n.value.find((e=>e.name===t));if(void 0===l||void 0===l.field)return e;const r=!0===o?-1:1,a="function"==typeof l.field?e=>l.field(e):e=>e[l.field];return e.sort(((e,t)=>{let n=a(e),o=a(t);return void 0!==l.rawSort?l.rawSort(n,o,e,t)*r:null==n?-1*r:null==o?1*r:void 0!==l.sort?l.sort(n,o,e,t)*r:!0===bi(n)&&!0===bi(o)?(n-o)*r:!0===gi(n)&&!0===gi(o)?function(e,t){return new Date(e)-new Date(t)}(n,o)*r:"boolean"==typeof n&&"boolean"==typeof o?(n-o)*r:([n,o]=[n,o].map((e=>(e+"").toLocaleString().toLowerCase())),n<o?-1*r:n===o?0:r)}))})),sort:function(l){let r=e.columnSortOrder;if(!0===mi(l))l.sortOrder&&(r=l.sortOrder),l=l.name;else{const e=n.value.find((e=>e.name===l));(null==e?void 0:e.sortOrder)&&(r=e.sortOrder)}let{sortBy:a,descending:i}=t.value;a!==l?(a=l,i="da"===r):!0===e.binaryStateSort?i=!i:!0===i?"ad"===r?a=null:i=!1:"ad"===r?i=!0:a=null,o({sortBy:a,descending:i,page:1})}}}const rb={filter:[String,Object],filterMethod:Function};function ab(e){return e.page<1&&(e.page=1),void 0!==e.rowsPerPage&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}const ib={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};const sb={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}};function ub(e){return Array.isArray(e)?e.slice():[]}const cb={expanded:Array};const db={visibleColumns:Array};const fb="q-table__bottom row items-center",pb={};gf.forEach((e=>{pb[e]={}}));var vb=Va({name:"QTable",props:{rows:{type:Array,required:!0},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{},...pb,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],tableRowStyleFn:Function,tableRowClassFn:Function,cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],cardStyleFn:Function,cardClassFn:Function,hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...Yu,...nb,...db,...rb,...ib,...cb,...sb,...ob},emits:["request","virtualScroll","update:fullscreen","fullscreen","update:expanded","update:selected","selection"],setup(e,{slots:t,emit:n}){const o=Ql(),{proxy:{$q:l}}=o,r=Ju(e,l),{inFullscreen:a,toggleFullscreen:i}=function(){const e=Ql(),{props:t,emit:n,proxy:o}=e;let l,r,a;const i=Ct(!1);function s(){!0===i.value?c():u()}function u(){!0!==i.value&&(i.value=!0,a=o.$el.parentNode,a.replaceChild(r,o.$el),document.body.appendChild(o.$el),tb++,1===tb&&document.body.classList.add("q-body--fullscreen-mixin"),l={handler:c},ai.add(l))}function c(){!0===i.value&&(void 0!==l&&(ai.remove(l),l=void 0),a.replaceChild(o.$el,r),i.value=!1,tb=Math.max(0,tb-1),0===tb&&(document.body.classList.remove("q-body--fullscreen-mixin"),void 0!==o.$el.scrollIntoView&&setTimeout((()=>{o.$el.scrollIntoView()}))))}return!0===mc(e)&&Xo((()=>o.$route.fullPath),(()=>{!0!==t.noRouteFullscreenExit&&c()})),Xo((()=>t.fullscreen),(e=>{i.value!==e&&s()})),Xo(i,(e=>{n("update:fullscreen",e),n("fullscreen",e)})),Hn((()=>{r=document.createElement("span")})),Wn((()=>{!0===t.fullscreen&&u()})),Gn(c),Object.assign(o,{toggleFullscreen:s,setFullscreen:u,exitFullscreen:c}),{inFullscreen:i,toggleFullscreen:s}}(),s=ar((()=>"function"==typeof e.rowKey?e.rowKey:t=>t[e.rowKey])),u=Ct(null),c=Ct(null),d=ar((()=>!0!==e.grid&&!0===e.virtualScroll)),f=ar((()=>" q-table__card"+(!0===r.value?" q-table__card--dark q-dark":"")+(!0===e.square?" q-table--square":"")+(!0===e.flat?" q-table--flat":"")+(!0===e.bordered?" q-table--bordered":""))),p=ar((()=>`q-table__container q-table--${e.separator}-separator column no-wrap`+(!0===e.grid?" q-table--grid":f.value)+(!0===r.value?" q-table--dark":"")+(!0===e.dense?" q-table--dense":"")+(!1===e.wrapCells?" q-table--no-wrap":"")+(!0===a.value?" fullscreen scroll":""))),v=ar((()=>p.value+(!0===e.loading?" q-table--loading":"")));Xo((()=>e.tableStyle+e.tableClass+e.tableHeaderStyle+e.tableHeaderClass+p.value),(()=>{var e;!0===d.value&&(null==(e=c.value)||e.reset())}));const{innerPagination:h,computedPagination:m,isServerSide:g,requestServerInteraction:b,setPagination:y}=function(e,t){const{props:n,emit:o}=e,l=Ct(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:0!==n.rowsPerPageOptions.length?n.rowsPerPageOptions[0]:5},n.pagination)),r=ar((()=>ab(void 0!==n["onUpdate:pagination"]?{...l.value,...n.pagination}:l.value))),a=ar((()=>void 0!==r.value.rowsNumber));function i(e){s({pagination:e,filter:n.filter})}function s(e={}){Qt((()=>{o("request",{pagination:e.pagination||r.value,filter:e.filter||n.filter,getCellValue:t})}))}return{innerPagination:l,computedPagination:r,isServerSide:a,requestServerInteraction:s,setPagination:function(e,t){const s=ab({...r.value,...e});!0!==function(e,t){for(const n in t)if(t[n]!==e[n])return!1;return!0}(r.value,s)?!0!==a.value?void 0!==n.pagination&&void 0!==n["onUpdate:pagination"]?o("update:pagination",s):l.value=s:i(s):!0===a.value&&!0===t&&i(s)}}}(o,ue),{computedFilterMethod:_}=function(e,t){const n=ar((()=>void 0!==e.filterMethod?e.filterMethod:(e,t,n,o)=>{const l=t?t.toLowerCase():"";return e.filter((e=>n.some((t=>{const n=o(t,e)+"";return-1!==("undefined"===n||"null"===n?"":n.toLowerCase()).indexOf(l)}))))}));return Xo((()=>e.filter),(()=>{Qt((()=>{t({page:1},!0)}))}),{deep:!0}),{computedFilterMethod:n}}(e,y),{isRowExpanded:w,setExpanded:k,updateExpanded:x}=function(e,t){const n=Ct(ub(e.expanded));function o(o){void 0!==e.expanded?t("update:expanded",o):n.value=o}return Xo((()=>e.expanded),(e=>{n.value=ub(e)})),{isRowExpanded:function(e){return n.value.includes(e)},setExpanded:o,updateExpanded:function(e,t){const l=n.value.slice(),r=l.indexOf(e);!0===t?-1===r&&(l.push(e),o(l)):-1!==r&&(l.splice(r,1),o(l))}}}(e,n),S=ar((()=>{let t=e.rows;if(!0===g.value||0===t.length)return t;const{sortBy:n,descending:o}=m.value;return e.filter&&(t=_.value(t,e.filter,I.value,ue)),null!==B.value&&(t=z.value(e.rows===t?t.slice():t,n,o)),t})),C=ar((()=>S.value.length)),E=ar((()=>{let t=S.value;if(!0===g.value)return t;const{rowsPerPage:n}=m.value;return 0!==n&&(0===j.value&&e.rows!==t?t.length>U.value&&(t=t.slice(0,U.value)):t=t.slice(j.value,U.value)),t})),{hasSelectionMode:T,singleSelection:L,multipleSelection:q,allRowsSelected:O,someRowsSelected:P,rowsSelectedNumber:F,isRowSelected:R,clearSelection:A,updateSelection:V}=function(e,t,n,o){const l=ar((()=>{const t={};return e.selected.map(o.value).forEach((e=>{t[e]=!0})),t})),r=ar((()=>"none"!==e.selection)),a=ar((()=>"single"===e.selection)),i=ar((()=>"multiple"===e.selection)),s=ar((()=>0!==n.value.length&&n.value.every((e=>!0===l.value[o.value(e)])))),u=ar((()=>!0!==s.value&&n.value.some((e=>!0===l.value[o.value(e)])))),c=ar((()=>e.selected.length));return{hasSelectionMode:r,singleSelection:a,multipleSelection:i,allRowsSelected:s,someRowsSelected:u,rowsSelectedNumber:c,isRowSelected:function(e){return!0===l.value[e]},clearSelection:function(){t("update:selected",[])},updateSelection:function(n,l,r,i){t("selection",{rows:l,added:r,keys:n,evt:i});const s=!0===a.value?!0===r?l:[]:!0===r?e.selected.concat(l):e.selected.filter((e=>!1===n.includes(o.value(e))));t("update:selected",s)}}}(e,n,E,s),{colList:M,computedCols:I,computedColsMap:N,computedColspan:$}=function(e,t,n){const o=ar((()=>{if(void 0!==e.columns)return e.columns;const t=e.rows[0];return void 0!==t?Object.keys(t).map((e=>({name:e,label:e.toUpperCase(),field:e,align:bi(t[e])?"right":"left",sortable:!0}))):[]})),l=ar((()=>{const{sortBy:n,descending:l}=t.value;return(void 0!==e.visibleColumns?o.value.filter((t=>!0===t.required||!0===e.visibleColumns.includes(t.name))):o.value).map((e=>{const t=e.align||"right",o=`text-${t}`;return{...e,align:t,__iconClass:`q-table__sort-icon q-table__sort-icon--${t}`,__thClass:o+(void 0!==e.headerClasses?" "+e.headerClasses:"")+(!0===e.sortable?" sortable":"")+(e.name===n?" sorted "+(!0===l?"sort-desc":""):""),__tdStyle:void 0!==e.style?"function"!=typeof e.style?()=>e.style:e.style:()=>null,__tdClass:void 0!==e.classes?"function"!=typeof e.classes?()=>o+" "+e.classes:t=>o+" "+e.classes(t):()=>o}}))})),r=ar((()=>{const e={};return l.value.forEach((t=>{e[t.name]=t})),e})),a=ar((()=>void 0!==e.tableColspan?e.tableColspan:l.value.length+(!0===n.value?1:0)));return{colList:o,computedCols:l,computedColsMap:r,computedColspan:a}}(e,m,T),{columnToSort:B,computedSortMethod:z,sort:D}=lb(e,m,M,y),{firstRowIndex:j,lastRowIndex:U,isFirstPage:H,isLastPage:W,pagesNumber:K,computedRowsPerPageOptions:Q,computedRowsNumber:G,firstPage:Y,prevPage:J,nextPage:Z,lastPage:X}=function(e,t,n,o,l,r){const{props:a,emit:i,proxy:{$q:s}}=e,u=ar((()=>!0===o.value?n.value.rowsNumber||0:r.value)),c=ar((()=>{const{page:e,rowsPerPage:t}=n.value;return(e-1)*t})),d=ar((()=>{const{page:e,rowsPerPage:t}=n.value;return e*t})),f=ar((()=>1===n.value.page)),p=ar((()=>0===n.value.rowsPerPage?1:Math.max(1,Math.ceil(u.value/n.value.rowsPerPage)))),v=ar((()=>0===d.value||n.value.page>=p.value)),h=ar((()=>(a.rowsPerPageOptions.includes(t.value.rowsPerPage)?a.rowsPerPageOptions:[t.value.rowsPerPage].concat(a.rowsPerPageOptions)).map((e=>({label:0===e?s.lang.table.allRows:""+e,value:e})))));return Xo(p,((e,t)=>{if(e===t)return;const o=n.value.page;e&&!o?l({page:1}):e<o&&l({page:e})})),void 0!==a["onUpdate:pagination"]&&i("update:pagination",{...n.value}),{firstRowIndex:c,lastRowIndex:d,isFirstPage:f,isLastPage:v,pagesNumber:p,computedRowsPerPageOptions:h,computedRowsNumber:u,firstPage:function(){l({page:1})},prevPage:function(){const{page:e}=n.value;e>1&&l({page:e-1})},nextPage:function(){const{page:e,rowsPerPage:t}=n.value;d.value>0&&e*t<u.value&&l({page:e+1})},lastPage:function(){l({page:p.value})}}}(o,h,m,g,y,C),ee=ar((()=>0===E.value.length)),te=ar((()=>{const t={};return gf.forEach((n=>{t[n]=e[n]})),void 0===t.virtualScrollItemSize&&(t.virtualScrollItemSize=!0===e.dense?28:48),t}));function ne(){if(!0===e.grid)return function(){const o=void 0!==t.item?t.item:o=>{const l=o.cols.map((e=>ir("div",{class:"q-table__grid-item-row"},[ir("div",{class:"q-table__grid-item-title"},[e.label]),ir("div",{class:"q-table__grid-item-value"},[e.value])])));if(!0===T.value){const n=t["body-selection"],a=void 0!==n?n(o):[ir(Km,{modelValue:o.selected,color:e.color,dark:r.value,dense:e.dense,"onUpdate:modelValue":(e,t)=>{V([o.key],[o.row],e,t)}})];l.unshift(ir("div",{class:"q-table__grid-item-row"},a),ir(Hg,{dark:r.value}))}const a={class:["q-table__grid-item-card"+f.value,e.cardClass],style:e.cardStyle};if(void 0!==e.cardStyleFn&&(a.style=[a.style,e.cardStyleFn(o.row)]),void 0!==e.cardClassFn){const t=e.cardClassFn(o.row);t&&(a.class[0]+=` ${t}`)}return void 0===e.onRowClick&&void 0===e.onRowDblclick&&void 0===e.onRowContextmenu||(a.class[0]+=" cursor-pointer",void 0!==e.onRowClick&&(a.onClick=e=>{n("RowClick",e,o.row,o.pageIndex)}),void 0!==e.onRowDblclick&&(a.onDblclick=e=>{n("RowDblclick",e,o.row,o.pageIndex)}),void 0!==e.onRowContextmenu&&(a.onContextmenu=e=>{n("rowContextmenu",e,o.row,o.pageIndex)})),ir("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(!0===o.selected?" q-table__grid-item--selected":"")},[ir("div",a,l)])};return ir("div",{class:["q-table__grid-content row",e.cardContainerClass],style:e.cardContainerStyle},E.value.map(((e,t)=>o(ie({key:s.value(e),row:e,pageIndex:t})))))}();const o=!0!==e.hideHeader?pe:null;if(!0===d.value){const n=t["top-row"],l=t["bottom-row"],r={default:e=>re(e.item,t.body,e.index)};if(void 0!==n){const e=ir("tbody",n({cols:I.value}));r.before=null===o?()=>e:()=>[o()].concat(e)}else null!==o&&(r.before=o);return void 0!==l&&(r.after=()=>ir("tbody",l({cols:I.value}))),ir(Jg,{ref:c,class:e.tableClass,style:e.tableStyle,...te.value,scrollTarget:e.virtualScrollTarget,items:E.value,type:"__qtable",tableColspan:$.value,onVirtualScroll:oe},r)}const l=[ae()];return null!==o&&l.unshift(o()),Qg({class:["q-table__middle scroll",e.tableClass],style:e.tableStyle},l)}function oe(e){n("virtualScroll",e)}function le(){return[ir(eb,{class:"q-table__linear-progress",color:e.color,dark:r.value,indeterminate:!0,trackColor:"transparent"})]}function re(o,l,a){const i=s.value(o),u=R(i);if(void 0!==l){const t={key:i,row:o,pageIndex:a,__trClass:u?"selected":""};if(void 0!==e.tableRowStyleFn&&(t.__trStyle=e.tableRowStyleFn(o)),void 0!==e.tableRowClassFn){const n=e.tableRowClassFn(o);n&&(t.__trClass=`${n} ${t.__trClass}`)}return l(ie(t))}const c=t["body-cell"],d=I.value.map((e=>{const n=t[`body-cell-${e.name}`],l=void 0!==n?n:c;return void 0!==l?l(function(e){return se(e),Ta(e,"value",(()=>ue(e.col,e.row))),e}({key:i,row:o,pageIndex:a,col:e})):ir("td",{class:e.__tdClass(o),style:e.__tdStyle(o)},ue(e,o))}));if(!0===T.value){const n=t["body-selection"],l=void 0!==n?n(function(e){return se(e),e}({key:i,row:o,pageIndex:a})):[ir(Km,{modelValue:u,color:e.color,dark:r.value,dense:e.dense,"onUpdate:modelValue":(e,t)=>{V([i],[o],e,t)}})];d.unshift(ir("td",{class:"q-table--col-auto-width"},l))}const f={key:i,class:{selected:u}};if(void 0!==e.onRowClick&&(f.class["cursor-pointer"]=!0,f.onClick=e=>{n("rowClick",e,o,a)}),void 0!==e.onRowDblclick&&(f.class["cursor-pointer"]=!0,f.onDblclick=e=>{n("rowDblclick",e,o,a)}),void 0!==e.onRowContextmenu&&(f.class["cursor-pointer"]=!0,f.onContextmenu=e=>{n("rowContextmenu",e,o,a)}),void 0!==e.tableRowStyleFn&&(f.style=e.tableRowStyleFn(o)),void 0!==e.tableRowClassFn){const t=e.tableRowClassFn(o);t&&(f.class[t]=!0)}return ir("tr",f,d)}function ae(){const e=t.body,n=t["top-row"],o=t["bottom-row"];let l=E.value.map(((t,n)=>re(t,e,n)));return void 0!==n&&(l=n({cols:I.value}).concat(l)),void 0!==o&&(l=l.concat(o({cols:I.value}))),ir("tbody",l)}function ie(e){return se(e),e.cols=e.cols.map((t=>Ta({...t},"value",(()=>ue(t,e.row))))),e}function se(t){Object.assign(t,{cols:I.value,colsMap:N.value,sort:D,rowIndex:j.value+t.pageIndex,color:e.color,dark:r.value,dense:e.dense}),!0===T.value&&Ta(t,"selected",(()=>R(t.key)),((e,n)=>{V([t.key],[t.row],e,n)})),Ta(t,"expand",(()=>w(t.key)),(e=>{x(t.key,e)}))}function ue(e,t){const n="function"==typeof e.field?e.field(t):t[e.field];return void 0!==e.format?e.format(n,t):n}const ce=ar((()=>({pagination:m.value,pagesNumber:K.value,isFirstPage:H.value,isLastPage:W.value,firstPage:Y,prevPage:J,nextPage:Z,lastPage:X,inFullscreen:a.value,toggleFullscreen:i})));function de(){const n=t.top,o=t["top-left"],l=t["top-right"],r=t["top-selection"],a=!0===T.value&&void 0!==r&&F.value>0,i="q-table__top relative-position row items-center";if(void 0!==n)return ir("div",{class:i},[n(ce.value)]);let s;return!0===a?s=r(ce.value).slice():(s=[],void 0!==o?s.push(ir("div",{class:"q-table__control"},[o(ce.value)])):e.title&&s.push(ir("div",{class:"q-table__control"},[ir("div",{class:["q-table__title",e.titleClass]},e.title)]))),void 0!==l&&(s.push(ir("div",{class:"q-table__separator col"})),s.push(ir("div",{class:"q-table__control"},[l(ce.value)]))),0!==s.length?ir("div",{class:i},s):void 0}const fe=ar((()=>!0===P.value?null:O.value));function pe(){const n=function(){const n=t.header,o=t["header-cell"];if(void 0!==n)return n(ve({header:!0})).slice();const l=I.value.map((e=>{const n=t[`header-cell-${e.name}`],l=void 0!==n?n:o,r=ve({col:e});return void 0!==l?l(r):ir(Dg,{key:e.name,props:r},(()=>e.label))}));if(!0===L.value&&!0!==e.grid)l.unshift(ir("th",{class:"q-table--col-auto-width"}," "));else if(!0===q.value){const n=t["header-selection"],o=void 0!==n?n(ve({})):[ir(Km,{color:e.color,modelValue:fe.value,dark:r.value,dense:e.dense,"onUpdate:modelValue":he})];l.unshift(ir("th",{class:"q-table--col-auto-width"},o))}return[ir("tr",{class:e.tableHeaderClass,style:e.tableHeaderStyle},l)]}();return!0===e.loading&&void 0===t.loading&&n.push(ir("tr",{class:"q-table__progress"},[ir("th",{class:"relative-position",colspan:$.value},le())])),ir("thead",n)}function ve(t){return Object.assign(t,{cols:I.value,sort:D,colsMap:N.value,color:e.color,dark:r.value,dense:e.dense}),!0===q.value&&Ta(t,"selected",(()=>fe.value),he),t}function he(e){!0===P.value&&(e=!1),V(E.value.map(s.value),E.value,e)}const me=ar((()=>{const t=[e.iconFirstPage||l.iconSet.table.firstPage,e.iconPrevPage||l.iconSet.table.prevPage,e.iconNextPage||l.iconSet.table.nextPage,e.iconLastPage||l.iconSet.table.lastPage];return!0===l.lang.rtl?t.reverse():t}));function ge(){if(!0===e.hideBottom)return;if(!0===ee.value){if(!0===e.hideNoData)return;const n=!0===e.loading?e.loadingLabel||l.lang.table.loading:e.filter?e.noResultsLabel||l.lang.table.noResults:e.noDataLabel||l.lang.table.noData,o=t["no-data"],r=void 0!==o?[o({message:n,icon:l.iconSet.table.warning,filter:e.filter})]:[ir(Gu,{class:"q-table__bottom-nodata-icon",name:l.iconSet.table.warning}),n];return ir("div",{class:fb+" q-table__bottom--nodata"},r)}const n=t.bottom;if(void 0!==n)return ir("div",{class:fb},[n(ce.value)]);const o=!0!==e.hideSelectedBanner&&!0===T.value&&F.value>0?[ir("div",{class:"q-table__control"},[ir("div",[(e.selectedRowsLabel||l.lang.table.selectedRecords)(F.value)])])]:[];return!0!==e.hidePagination?ir("div",{class:fb+" justify-end"},function(n){let o;const{rowsPerPage:a}=m.value,i=e.paginationLabel||l.lang.table.pagination,s=t.pagination,u=e.rowsPerPageOptions.length>1;if(n.push(ir("div",{class:"q-table__separator col"})),!0===u&&n.push(ir("div",{class:"q-table__control"},[ir("span",{class:"q-table__bottom-item"},[e.rowsPerPageLabel||l.lang.table.recordsPerPage]),ir(qf,{class:"q-table__select inline q-table__bottom-item",color:e.color,modelValue:a,options:Q.value,displayValue:0===a?l.lang.table.allRows:a,dark:r.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":be})])),void 0!==s)o=s(ce.value);else if(o=[ir("span",0!==a?{class:"q-table__bottom-item"}:{},[a?i(j.value+1,Math.min(U.value,G.value),G.value):i(1,C.value,G.value)])],0!==a&&K.value>1){const t={color:e.color,round:!0,dense:!0,flat:!0};!0===e.dense&&(t.size="sm"),K.value>2&&o.push(ir(qm,{key:"pgFirst",...t,icon:me.value[0],disable:H.value,ariaLabel:l.lang.pagination.first,onClick:Y})),o.push(ir(qm,{key:"pgPrev",...t,icon:me.value[1],disable:H.value,ariaLabel:l.lang.pagination.prev,onClick:J}),ir(qm,{key:"pgNext",...t,icon:me.value[2],disable:W.value,ariaLabel:l.lang.pagination.next,onClick:Z})),K.value>2&&o.push(ir(qm,{key:"pgLast",...t,icon:me.value[3],disable:W.value,ariaLabel:l.lang.pagination.last,onClick:X}))}return n.push(ir("div",{class:"q-table__control"},o)),n}(o)):0!==o.length?ir("div",{class:fb},o):void 0}function be(e){y({page:1,rowsPerPage:e.value})}return Object.assign(o.proxy,{requestServerInteraction:b,setPagination:y,firstPage:Y,prevPage:J,nextPage:Z,lastPage:X,isRowSelected:R,clearSelection:A,isRowExpanded:w,setExpanded:k,sort:D,resetVirtualScroll:function(){!0===d.value&&c.value.reset()},scrollTo:function(t,o){if(null!==c.value)return void c.value.scrollTo(t,o);t=parseInt(t,10);const l=u.value.querySelector(`tbody tr:nth-of-type(${t+1})`);if(null!==l){const o=u.value.querySelector(".q-table__middle.scroll"),r=l.offsetTop-e.virtualScrollStickySizeStart,a=r<o.scrollTop?"decrease":"increase";o.scrollTop=r,n("virtualScroll",{index:t,from:0,to:h.value.rowsPerPage-1,direction:a})}},getCellValue:ue}),function(e,t){for(const n in t)Ta(e,n,t[n])}(o.proxy,{filteredSortedRows:()=>S.value,computedRows:()=>E.value,computedRowsNumber:()=>G.value}),()=>{const n=[de()],o={ref:u,class:v.value};return!0===e.grid?n.push(function(){const n=!0===e.gridHeader?[ir("table",{class:"q-table"},[pe()])]:!0===e.loading&&void 0===t.loading?le():void 0;return ir("div",{class:"q-table__middle"},n)}()):Object.assign(o,{class:[o.class,e.cardClass],style:e.cardStyle}),n.push(ne(),ge()),!0===e.loading&&void 0!==t.loading&&n.push(t.loading()),ir("div",o,n)}}});function hb(e){return e?"✅":"❌"}const mb=[{name:"website",label:"website url",align:"left",field:"website"},{name:"sitekey",label:"sitekey",align:"left",field:"sitekey"},{name:"isEnterprise",label:"Enterprise",align:"left",field:e=>hb(e.isEnterprise)},{name:"isRqDataRequired",label:"rqdata required",align:"left",field:e=>hb(e.isRqDataRequired)},{name:"jsonValue",label:"Capsolver json",align:"left",field:"jsonValue"}],gb=Ct([]),bb={websiteURL:"",websiteKey:"",version:"",isEnterprise:!1,getCaptcha:""};function yb(){return chrome.devtools.network.onRequestFinished.addListener((e=>{const t=e.request.url;/https:\/\/newassets\.([a-zA-Z0-9\-]+\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}\/captcha\/v1\/[a-z0-9]+\/static\/hcaptcha\.html/gm.test(t)?(e=>{bb.isEnterprise=!1,e.getContent((e=>{bb.getCaptcha=btoa(unescape(encodeURIComponent(e)))}))})(e):/https:\/\/js\.([a-zA-Z0-9\-]+\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}\/1\/api\.js/gm.test(t)?(e=>{const t=["sentry","custom","apiEndpoint","endpoint","reportapi","assethost","imghost"];for(let n of t)if(e.request.url.includes(n)){bb.isEnterprise=!0;break}})(e):/https:\/\/([a-zA-Z0-9\-]+\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}\/checksiteconfig\?.+/gm.test(t)?(e=>{const t=new URL(e.request.url);bb.version=t.searchParams.get("v"),bb.websiteURL=t.searchParams.get("host"),bb.websiteKey=t.searchParams.get("sitekey"),e.getContent((e=>{e.includes('"features":{}')||(bb.isEnterprise=!0)}))})(e):/https:\/\/([a-zA-Z0-9\-]+\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}\/getcaptcha\/[a-z0-9\-]+/gm.test(t)&&(e=>{let t=null;e.request.postData.params.forEach((e=>{"sitekey"===e.name&&(bb.websiteKey=e.value),"host"===e.name&&(bb.websiteURL=e.value),"v"===e.name&&(bb.version=e.value)})),e.getContent((e=>{e.includes("request_config")||(bb.isEnterprise=!0)})),e.request.postData&&e.request.postData.text.includes("rqdata")&&(bb.isEnterprise=!0,t=e.request.postData.text.match(/rqdata=([^&]*)/)[1]);const n={site_url:bb.websiteURL,is_enterprise:bb.isEnterprise,is_rqdata_required:!!t,key:bb.websiteKey,anchor:bb.getCaptcha},{site_url:o,is_enterprise:l,is_rqdata_required:r,key:a}=n,i=JSON.stringify(function(e,t,n,o=null){const l={clientKey:"YOUR_API_KEY",task:{type:"HCaptchaTask",websiteURL:e,websiteKey:t,getCaptcha:n,proxy:"Your proxy here"}};return o&&(l.task.enterprisePayload={rqdata:"Obtain this value, as it's required and changes dynamically"}),l}(bb.websiteURL,bb.websiteKey,bb.getCaptcha,t),null,4);gb.value.push({website:o,sitekey:a,isEnterprise:l,isRqDataRequired:r,jsonValue:i})})(e)})),{hCaptchaInfo:gb,columns:mb}}const _b=[{name:"website",label:"website url",align:"left",field:"website"},{name:"publicKey",label:"site key",align:"left",field:"publicKey"},{name:"isFunction",label:"funcaptcha",align:"left",field:e=>xb(e.isFunction)},{name:"funcaptchaApiJsSubdomain",label:"funcaptcha api js subdomain",align:"left",field:e=>e.funcaptchaApiJsSubdomain},{name:"dataBlob",label:"data blob",align:"left",field:e=>xb(e.dataBlob)},{name:"bda",label:"bda",align:"left",field:"bda"},{name:"userAgent",label:"user agent",align:"left",field:"userAgent"},{name:"jsonValue",label:"Capsolver json",align:"left",field:"jsonValue"}],wb=Ct([]),kb={websiteURL:"",websitePublicKey:"",data:null,bda:"",userAgent:"",isFunCaptcha:!1,funcaptchaApiJSSubdomain:""};function xb(e){return e?"✅":"❌"}const Sb=e=>{const t=new URL(e.request.url),n=e.request.postData.text,o=new URLSearchParams(n);kb.websiteURL=o.get("site"),kb.websitePublicKey=o.get("public_key"),kb.data=o.get("data[blob]"),kb.bda=o.get("bda"),kb.userAgent=o.get("userbrowser"),kb.funcaptchaApiJSSubdomain="https://"+function(e){return new URL(e).hostname}(t);const l={site_url:kb.websiteURL,is_funCaptcha:kb.isFunCaptcha,key:kb.websitePublicKey,data:kb.data,userAgent:kb.userAgent,bda:kb.bda,funcaptchaApiJSSubdomain:kb.funcaptchaApiJSSubdomain},{site_url:r,is_funCaptcha:a,key:i,data:s,funcaptchaApiJSSubdomain:u,bda:c,userAgent:d}=l,f=JSON.stringify(function(e,t,n,o=null){if(!n)throw new Error("Failed to extract subdomain from website URL");const l={clientKey:"YOUR_API_KEY_HERE",task:{type:"FunCaptchaTaskProxyLess",websiteURL:e,websitePublicKey:t,funcaptchaApiJSSubdomain:n}};return o&&(l.task.data=JSON.stringify({blob:"Obtain this value, it's required and it's different each time."})),l}(kb.websiteURL,kb.websitePublicKey,kb.funcaptchaApiJSSubdomain,kb.data),null,4);wb.value.push({website:r,publicKey:i,isFunction:a,funcaptchaApiJsSubdomain:u,dataBlob:!!s,jsonValue:f,bda:c,userAgent:d})};var Cb={
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
read:function(e,t,n,o,l){var r,a,i=8*l-o-1,s=(1<<i)-1,u=s>>1,c=-7,d=n?l-1:0,f=n?-1:1,p=e[t+d];for(d+=f,r=p&(1<<-c)-1,p>>=-c,c+=i;c>0;r=256*r+e[t+d],d+=f,c-=8);for(a=r&(1<<-c)-1,r>>=-c,c+=o;c>0;a=256*a+e[t+d],d+=f,c-=8);if(0===r)r=1-u;else{if(r===s)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,o),r-=u}return(p?-1:1)*a*Math.pow(2,r-o)},write:function(e,t,n,o,l,r){var a,i,s,u=8*r-l-1,c=(1<<u)-1,d=c>>1,f=23===l?Math.pow(2,-24)-Math.pow(2,-77):0,p=o?0:r-1,v=o?1:-1,h=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(i=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-a))<1&&(a--,s*=2),(t+=a+d>=1?f/s:f*Math.pow(2,1-d))*s>=2&&(a++,s/=2),a+d>=c?(i=0,a=c):a+d>=1?(i=(t*s-1)*Math.pow(2,l),a+=d):(i=t*Math.pow(2,d-1)*Math.pow(2,l),a=0));l>=8;e[n+p]=255&i,p+=v,i/=256,l-=8);for(a=a<<l|i,u+=l;u>0;e[n+p]=255&a,p+=v,a/=256,u-=8);e[n+p-v]|=128*h}},Eb=Lb,Tb=Cb;function Lb(e){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(e)?e:new Uint8Array(e||0),this.pos=0,this.type=0,this.length=this.buf.length}Lb.Varint=0,Lb.Fixed64=1,Lb.Bytes=2,Lb.Fixed32=5;var qb="undefined"==typeof TextDecoder?null:new TextDecoder("utf-8");function Ob(e){return e.type===Lb.Bytes?e.readVarint()+e.pos:e.pos+1}function Pb(e,t,n){return n?4294967296*t+(e>>>0):4294967296*(t>>>0)+(e>>>0)}function Fb(e,t,n){var o=t<=16383?1:t<=2097151?2:t<=268435455?3:Math.floor(Math.log(t)/(7*Math.LN2));n.realloc(o);for(var l=n.pos-1;l>=e;l--)n.buf[l+o]=n.buf[l]}function Rb(e,t){for(var n=0;n<e.length;n++)t.writeVarint(e[n])}function Ab(e,t){for(var n=0;n<e.length;n++)t.writeSVarint(e[n])}function Vb(e,t){for(var n=0;n<e.length;n++)t.writeFloat(e[n])}function Mb(e,t){for(var n=0;n<e.length;n++)t.writeDouble(e[n])}function Ib(e,t){for(var n=0;n<e.length;n++)t.writeBoolean(e[n])}function Nb(e,t){for(var n=0;n<e.length;n++)t.writeFixed32(e[n])}function $b(e,t){for(var n=0;n<e.length;n++)t.writeSFixed32(e[n])}function Bb(e,t){for(var n=0;n<e.length;n++)t.writeFixed64(e[n])}function zb(e,t){for(var n=0;n<e.length;n++)t.writeSFixed64(e[n])}function Db(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16)+16777216*e[t+3]}function jb(e,t,n){e[n]=t,e[n+1]=t>>>8,e[n+2]=t>>>16,e[n+3]=t>>>24}function Ub(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16)+(e[t+3]<<24)}Lb.prototype={destroy:function(){this.buf=null},readFields:function(e,t,n){for(n=n||this.length;this.pos<n;){var o=this.readVarint(),l=o>>3,r=this.pos;this.type=7&o,e(l,t,this),this.pos===r&&this.skip(o)}return t},readMessage:function(e,t){return this.readFields(e,t,this.readVarint()+this.pos)},readFixed32:function(){var e=Db(this.buf,this.pos);return this.pos+=4,e},readSFixed32:function(){var e=Ub(this.buf,this.pos);return this.pos+=4,e},readFixed64:function(){var e=Db(this.buf,this.pos)+4294967296*Db(this.buf,this.pos+4);return this.pos+=8,e},readSFixed64:function(){var e=Db(this.buf,this.pos)+4294967296*Ub(this.buf,this.pos+4);return this.pos+=8,e},readFloat:function(){var e=Tb.read(this.buf,this.pos,!0,23,4);return this.pos+=4,e},readDouble:function(){var e=Tb.read(this.buf,this.pos,!0,52,8);return this.pos+=8,e},readVarint:function(e){var t,n,o=this.buf;return t=127&(n=o[this.pos++]),n<128?t:(t|=(127&(n=o[this.pos++]))<<7,n<128?t:(t|=(127&(n=o[this.pos++]))<<14,n<128?t:(t|=(127&(n=o[this.pos++]))<<21,n<128?t:function(e,t,n){var o,l,r=n.buf;if(l=r[n.pos++],o=(112&l)>>4,l<128)return Pb(e,o,t);if(l=r[n.pos++],o|=(127&l)<<3,l<128)return Pb(e,o,t);if(l=r[n.pos++],o|=(127&l)<<10,l<128)return Pb(e,o,t);if(l=r[n.pos++],o|=(127&l)<<17,l<128)return Pb(e,o,t);if(l=r[n.pos++],o|=(127&l)<<24,l<128)return Pb(e,o,t);if(l=r[n.pos++],o|=(1&l)<<31,l<128)return Pb(e,o,t);throw new Error("Expected varint not more than 10 bytes")}(t|=(15&(n=o[this.pos]))<<28,e,this))))},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var e=this.readVarint();return e%2==1?(e+1)/-2:e/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var e=this.readVarint()+this.pos,t=this.pos;return this.pos=e,e-t>=12&&qb?function(e,t,n){return qb.decode(e.subarray(t,n))}(this.buf,t,e):function(e,t,n){var o="",l=t;for(;l<n;){var r,a,i,s=e[l],u=null,c=s>239?4:s>223?3:s>191?2:1;if(l+c>n)break;1===c?s<128&&(u=s):2===c?128==(192&(r=e[l+1]))&&(u=(31&s)<<6|63&r)<=127&&(u=null):3===c?(r=e[l+1],a=e[l+2],128==(192&r)&&128==(192&a)&&((u=(15&s)<<12|(63&r)<<6|63&a)<=2047||u>=55296&&u<=57343)&&(u=null)):4===c&&(r=e[l+1],a=e[l+2],i=e[l+3],128==(192&r)&&128==(192&a)&&128==(192&i)&&((u=(15&s)<<18|(63&r)<<12|(63&a)<<6|63&i)<=65535||u>=1114112)&&(u=null)),null===u?(u=65533,c=1):u>65535&&(u-=65536,o+=String.fromCharCode(u>>>10&1023|55296),u=56320|1023&u),o+=String.fromCharCode(u),l+=c}return o}(this.buf,t,e)},readBytes:function(){var e=this.readVarint()+this.pos,t=this.buf.subarray(this.pos,e);return this.pos=e,t},readPackedVarint:function(e,t){if(this.type!==Lb.Bytes)return e.push(this.readVarint(t));var n=Ob(this);for(e=e||[];this.pos<n;)e.push(this.readVarint(t));return e},readPackedSVarint:function(e){if(this.type!==Lb.Bytes)return e.push(this.readSVarint());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readSVarint());return e},readPackedBoolean:function(e){if(this.type!==Lb.Bytes)return e.push(this.readBoolean());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readBoolean());return e},readPackedFloat:function(e){if(this.type!==Lb.Bytes)return e.push(this.readFloat());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readFloat());return e},readPackedDouble:function(e){if(this.type!==Lb.Bytes)return e.push(this.readDouble());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readDouble());return e},readPackedFixed32:function(e){if(this.type!==Lb.Bytes)return e.push(this.readFixed32());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readFixed32());return e},readPackedSFixed32:function(e){if(this.type!==Lb.Bytes)return e.push(this.readSFixed32());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readSFixed32());return e},readPackedFixed64:function(e){if(this.type!==Lb.Bytes)return e.push(this.readFixed64());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readFixed64());return e},readPackedSFixed64:function(e){if(this.type!==Lb.Bytes)return e.push(this.readSFixed64());var t=Ob(this);for(e=e||[];this.pos<t;)e.push(this.readSFixed64());return e},skip:function(e){var t=7&e;if(t===Lb.Varint)for(;this.buf[this.pos++]>127;);else if(t===Lb.Bytes)this.pos=this.readVarint()+this.pos;else if(t===Lb.Fixed32)this.pos+=4;else{if(t!==Lb.Fixed64)throw new Error("Unimplemented type: "+t);this.pos+=8}},writeTag:function(e,t){this.writeVarint(e<<3|t)},realloc:function(e){for(var t=this.length||16;t<this.pos+e;)t*=2;if(t!==this.length){var n=new Uint8Array(t);n.set(this.buf),this.buf=n,this.length=t}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(e){this.realloc(4),jb(this.buf,e,this.pos),this.pos+=4},writeSFixed32:function(e){this.realloc(4),jb(this.buf,e,this.pos),this.pos+=4},writeFixed64:function(e){this.realloc(8),jb(this.buf,-1&e,this.pos),jb(this.buf,Math.floor(2.3283064365386963e-10*e),this.pos+4),this.pos+=8},writeSFixed64:function(e){this.realloc(8),jb(this.buf,-1&e,this.pos),jb(this.buf,Math.floor(2.3283064365386963e-10*e),this.pos+4),this.pos+=8},writeVarint:function(e){(e=+e||0)>268435455||e<0?function(e,t){var n,o;e>=0?(n=e%4294967296|0,o=e/4294967296|0):(o=~(-e/4294967296),4294967295^(n=~(-e%4294967296))?n=n+1|0:(n=0,o=o+1|0));if(e>=0x10000000000000000||e<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");t.realloc(10),function(e,t,n){n.buf[n.pos++]=127&e|128,e>>>=7,n.buf[n.pos++]=127&e|128,e>>>=7,n.buf[n.pos++]=127&e|128,e>>>=7,n.buf[n.pos++]=127&e|128,e>>>=7,n.buf[n.pos]=127&e}(n,0,t),function(e,t){var n=(7&e)<<4;if(t.buf[t.pos++]|=n|((e>>>=3)?128:0),!e)return;if(t.buf[t.pos++]=127&e|((e>>>=7)?128:0),!e)return;if(t.buf[t.pos++]=127&e|((e>>>=7)?128:0),!e)return;if(t.buf[t.pos++]=127&e|((e>>>=7)?128:0),!e)return;if(t.buf[t.pos++]=127&e|((e>>>=7)?128:0),!e)return;t.buf[t.pos++]=127&e}(o,t)}(e,this):(this.realloc(4),this.buf[this.pos++]=127&e|(e>127?128:0),e<=127||(this.buf[this.pos++]=127&(e>>>=7)|(e>127?128:0),e<=127||(this.buf[this.pos++]=127&(e>>>=7)|(e>127?128:0),e<=127||(this.buf[this.pos++]=e>>>7&127))))},writeSVarint:function(e){this.writeVarint(e<0?2*-e-1:2*e)},writeBoolean:function(e){this.writeVarint(Boolean(e))},writeString:function(e){e=String(e),this.realloc(4*e.length),this.pos++;var t=this.pos;this.pos=function(e,t,n){for(var o,l,r=0;r<t.length;r++){if((o=t.charCodeAt(r))>55295&&o<57344){if(!l){o>56319||r+1===t.length?(e[n++]=239,e[n++]=191,e[n++]=189):l=o;continue}if(o<56320){e[n++]=239,e[n++]=191,e[n++]=189,l=o;continue}o=l-55296<<10|o-56320|65536,l=null}else l&&(e[n++]=239,e[n++]=191,e[n++]=189,l=null);o<128?e[n++]=o:(o<2048?e[n++]=o>>6|192:(o<65536?e[n++]=o>>12|224:(e[n++]=o>>18|240,e[n++]=o>>12&63|128),e[n++]=o>>6&63|128),e[n++]=63&o|128)}return n}(this.buf,e,this.pos);var n=this.pos-t;n>=128&&Fb(t,n,this),this.pos=t-1,this.writeVarint(n),this.pos+=n},writeFloat:function(e){this.realloc(4),Tb.write(this.buf,e,this.pos,!0,23,4),this.pos+=4},writeDouble:function(e){this.realloc(8),Tb.write(this.buf,e,this.pos,!0,52,8),this.pos+=8},writeBytes:function(e){var t=e.length;this.writeVarint(t),this.realloc(t);for(var n=0;n<t;n++)this.buf[this.pos++]=e[n]},writeRawMessage:function(e,t){this.pos++;var n=this.pos;e(t,this);var o=this.pos-n;o>=128&&Fb(n,o,this),this.pos=n-1,this.writeVarint(o),this.pos+=o},writeMessage:function(e,t,n){this.writeTag(e,Lb.Bytes),this.writeRawMessage(t,n)},writePackedVarint:function(e,t){t.length&&this.writeMessage(e,Rb,t)},writePackedSVarint:function(e,t){t.length&&this.writeMessage(e,Ab,t)},writePackedBoolean:function(e,t){t.length&&this.writeMessage(e,Ib,t)},writePackedFloat:function(e,t){t.length&&this.writeMessage(e,Vb,t)},writePackedDouble:function(e,t){t.length&&this.writeMessage(e,Mb,t)},writePackedFixed32:function(e,t){t.length&&this.writeMessage(e,Nb,t)},writePackedSFixed32:function(e,t){t.length&&this.writeMessage(e,$b,t)},writePackedFixed64:function(e,t){t.length&&this.writeMessage(e,Bb,t)},writePackedSFixed64:function(e,t){t.length&&this.writeMessage(e,zb,t)},writeBytesField:function(e,t){this.writeTag(e,Lb.Bytes),this.writeBytes(t)},writeFixed32Field:function(e,t){this.writeTag(e,Lb.Fixed32),this.writeFixed32(t)},writeSFixed32Field:function(e,t){this.writeTag(e,Lb.Fixed32),this.writeSFixed32(t)},writeFixed64Field:function(e,t){this.writeTag(e,Lb.Fixed64),this.writeFixed64(t)},writeSFixed64Field:function(e,t){this.writeTag(e,Lb.Fixed64),this.writeSFixed64(t)},writeVarintField:function(e,t){this.writeTag(e,Lb.Varint),this.writeVarint(t)},writeSVarintField:function(e,t){this.writeTag(e,Lb.Varint),this.writeSVarint(t)},writeStringField:function(e,t){this.writeTag(e,Lb.Bytes),this.writeString(t)},writeFloatField:function(e,t){this.writeTag(e,Lb.Fixed32),this.writeFloat(t)},writeDoubleField:function(e,t){this.writeTag(e,Lb.Fixed64),this.writeDouble(t)},writeBooleanField:function(e,t){this.writeVarintField(e,Boolean(t))}};var Hb={read:null,_readField:null,write:null};Hb.read=function(e,t){return e.readFields(Hb._readField,{field_01:"",field_02:"",field_03:"",field_04:"",field_05:"",field_06:"",field_07:"",field_08:"",field_09:"",field_10:"",field_11:"",field_12:"",field_13:"",field_14:"",field_15:"",field_16:"",field_17:"",field_18:"",field_19:"",field_20:"",field_21:"",field_22:"",field_23:"",field_24:""},t)},Hb._readField=function(e,t,n){1===e?t.field_01=n.readString():2===e?t.field_02=n.readString():3===e?t.field_03=n.readString():4===e?t.field_04=n.readString():5===e?t.field_05=n.readString():6===e?t.field_06=n.readString():7===e?t.field_07=n.readString():8===e?t.field_08=n.readString():9===e?t.field_09=n.readString():10===e?t.field_10=n.readString():11===e?t.field_11=n.readString():12===e?t.field_12=n.readString():13===e?t.field_13=n.readString():14===e?t.field_14=n.readString():15===e?t.field_15=n.readString():16===e?t.field_16=n.readString():17===e?t.field_17=n.readString():18===e?t.field_18=n.readString():19===e?t.field_19=n.readString():20===e?t.field_20=n.readString():21===e?t.field_21=n.readString():22===e?t.field_22=n.readString():23===e?t.field_23=n.readString():24===e&&(t.field_24=n.readString())},Hb.write=function(e,t){e.field_01&&t.writeStringField(1,e.field_01),e.field_02&&t.writeStringField(2,e.field_02),e.field_03&&t.writeStringField(3,e.field_03),e.field_04&&t.writeStringField(4,e.field_04),e.field_05&&t.writeStringField(5,e.field_05),e.field_06&&t.writeStringField(6,e.field_06),e.field_07&&t.writeStringField(7,e.field_07),e.field_08&&t.writeStringField(8,e.field_08),e.field_09&&t.writeStringField(9,e.field_09),e.field_10&&t.writeStringField(10,e.field_10),e.field_11&&t.writeStringField(11,e.field_11),e.field_12&&t.writeStringField(12,e.field_12),e.field_13&&t.writeStringField(13,e.field_13),e.field_14&&t.writeStringField(14,e.field_14),e.field_15&&t.writeStringField(15,e.field_15),e.field_16&&t.writeStringField(16,e.field_16),e.field_17&&t.writeStringField(17,e.field_17),e.field_18&&t.writeStringField(18,e.field_18),e.field_19&&t.writeStringField(19,e.field_19),e.field_20&&t.writeStringField(20,e.field_20),e.field_21&&t.writeStringField(21,e.field_21),e.field_22&&t.writeStringField(22,e.field_22),e.field_23&&t.writeStringField(23,e.field_23),e.field_24&&t.writeStringField(24,e.field_24)};const Wb=[{name:"website",label:"website url",align:"left",field:"website"},{name:"sitekey",label:"sitekey",align:"left",field:e=>e.siteKey},{name:"action",label:"action",align:"left",field:"action"},{name:"isInvisible",label:"isInvisible",align:"left",field:e=>Gb(e.isInvisible)},{name:"recaptchaV2Normal",label:"recaptchaV2Normal",align:"left",field:e=>Gb(e.recaptchaV2Normal)},{name:"isReCaptchaV3",label:"isReCaptchaV3",align:"left",field:e=>Gb(e.isReCaptchaV3)},{name:"isEnterprise",label:"isEnterprise",align:"left",field:e=>Gb(e.isEnterprise)},{name:"isSRequired",label:"isSRequired",align:"left",field:e=>Gb(e.isSRequired)},{name:"apiDomain",label:"api domain",align:"left",field:"apiDomain"},{name:"jsonValue",label:"Capsolver json",align:"left",field:"jsonValue"}],Kb={},Qb=e=>{const t=new URL(e);return atob(t.searchParams.get("co").replaceAll(".","=")).replace(":443","")};function Gb(e){return e?"✅":"❌"}const Yb=Ct([]);const Jb=(e,t)=>{const n=Uint8Array.from(e,(e=>e.charCodeAt(0))),o=new Eb(n),l=Hb.read(o),{field_14:r,field_08:a=""}=l;if(Kb[r]){const{site_url:n,is_enterprise:o,is_invisible:l,is_s_required:i,apiDomain:s,anchor:u}=Kb[r],c=a.length>0,d=!l&&!c,f=!(!l||c),p=["accept","accept-language","content-type","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","sec-fetch-dest","sec-fetch-mode","sec-fetch-site","x-client-data"];let v="";const h={};t.request.headers.forEach((e=>{"referer"===e.name&&(v=e.value),p.includes(e.name)&&(h[e.name]=e.value)}));const m=`\n            fetch("${t.request.url}", {\n                "headers": ${JSON.stringify(h)},\n                "referrer": "${v}",\n                "referrerPolicy": "strict-origin-when-cross-origin",\n                "body": "${e}",\n                "method": "${t.request.method}",\n                "mode": "cors",\n                "credentials": "include" \n            });\n        `,g=btoa(unescape(encodeURIComponent(m))),b=JSON.stringify(JSON.parse(function(e,t,n,o,l,r,a,i,s,u){let c;c=o?r?"ReCaptchaV3EnterpriseTaskProxyless":"ReCaptchaV3TaskProxyLess":r?"ReCaptchaV2EnterpriseTaskProxyLess":"ReCaptchaV2TaskProxyLess";const d={clientKey:"YOUR_API_KEY",task:{type:c,websiteURL:e,websiteKey:t,anchor:s,reload:u}};return i.includes("recaptcha.net")&&(d.task.apiDomain=i),o&&(d.task.pageAction=n),l&&(d.task.isInvisible=!0),a&&(d.task.enterprisePayload={s:"SOME_ADDITIONAL_TOKEN"}),JSON.stringify(d,null,4)}(n,r,a,c,f,o,i,s,u,g)),null,4);Yb.value.push({website:n,siteKey:r,action:a,isInvisible:f,recaptchaV2Normal:d,isReCaptchaV3:c,isEnterprise:o,isSRequired:i,apiDomain:s,jsonValue:b})}};function Zb(){return chrome.devtools.network.onRequestFinished.addListener((e=>{/https:\/\/(www\.)*(google\.com|recaptcha\.net)\/recaptcha\/(api2|enterprise)\/anchor/gm.test(e.request.url)?(e=>{const t=new URL(e.request.url),n=t.searchParams.get("k"),o=t.searchParams.get("size"),l=t.searchParams.get("s"),r={site_url:Qb(e.request.url),is_enterprise:e.request.url.includes("enterprise"),is_invisible:o.includes("invisible"),is_s_required:null!=l,apiDomain:t.host.includes("recaptcha.net")?"www.recaptcha.net":""};e.getContent((e=>{r.anchor=btoa(unescape(encodeURIComponent(e))),Kb[n]=r}))})(e):/https:\/\/(www\.)*(google\.com|recaptcha\.net)\/recaptcha\/(api2|enterprise)\/reload/gm.test(e.request.url)&&Jb(e.request.postData.text,e)})),{reCaptchaInfo:Yb,recaptchaColumns:Wb}}const Xb={captcha_id:"",challenge:"",isV4:!1},ey=[{name:"isV4",label:"Is Geetest V4",align:"left",field:e=>e.isV4?"✅":"❌"},{name:"captcha_id",label:"captcha_id",align:"left",field:"captcha_id"}],ty=Ct([]);function ny(){return chrome.devtools.network.onRequestFinished.addListener((e=>{e.request.url.startsWith("https://gcaptcha4.geetest.com/load")&&function(e){e.request.queryString.forEach((e=>{"captcha_id"===e.name&&(Xb.captcha_id=e.value),"challenge"===e.name&&(Xb.challenge=e.value),Xb.isV4=!0})),ty.value.push(Xb)}(e)})),{columns:ey,geetestV4Info:ty}}const oy=Ct([]),ly=[{name:"website",label:"website url",align:"left",field:"website",classes:"turnstile-website"},{name:"sitekey",label:"sitekey",align:"left",field:"sitekey",style:"font-size: 16px"},{name:"action",label:"action",align:"left",field:"action",style:"font-size: 16px"},{name:"cData",label:"cData",align:"left",field:"cData",style:"font-size: 16px"}];async function ry(){(await chrome.tabs.query({active:!0})).forEach((e=>{chrome.tabs.sendMessage(e.id,{command:"get-cloudflare-info"},(e=>{!function(e){if(!e.sitekey)return;-1===oy.value.findIndex((t=>t.sitekey===e.sitekey))&&oy.value.push({sitekey:e.sitekey,action:null==e?void 0:e.action,cData:null==e?void 0:e.cData,website:e.website})}(e)}))}))}const ay={class:"detector"},iy={class:"logo"},sy=["src"],uy=["src"],cy={class:"content"},dy={class:"empty"};const fy=[{path:"/:catchAll(.*)*",component:()=>r((()=>import("./ErrorNotFound.d95144c0.js")),[])},{path:"/popup",component:rm,redirect:{name:"popup"},children:[{path:"",name:"popup",component:Bg}]},{path:"/devtools",component:ki(An({__name:"devtool",setup(e){const t=-1!==window.navigator.userAgent.indexOf("Chrome")?"www/index.html#/devtools":"index.html#/devtools";chrome.devtools.panels.create("Capsolver Captcha Detector","icons/icon-128x128.png",t);const{hCaptchaInfo:n,columns:o}=yb(),{funcaptchaInfo:l,funcaptchaColumns:r}=(chrome.devtools.network.onRequestFinished.addListener((e=>{const t=e.request.url;/^https:\/\/[^\/]+\/fc\/[a-zA-Z0-9-]+\/public_key\/[A-Fa-f0-9\-]+$/gm.test(t)&&(kb.isFunCaptcha=!0,Sb(e))})),{funcaptchaInfo:wb,funcaptchaColumns:_b}),{reCaptchaInfo:a,recaptchaColumns:i}=Zb(),{geetestV4Info:s,columns:u}=ny(),{turnstileInfo:c,columns:d}=(chrome.devtools.network.onRequestFinished.addListener((e=>{e.request.url.startsWith("https://challenges.cloudflare.com/turnstile/v0/")&&ry()})),{columns:ly,turnstileInfo:oy}),f=ar((()=>n.value.length>0)),p=ar((()=>l.value.length>0)),v=ar((()=>a.value.length>0)),h=ar((()=>s.value.length>0)),m=ar((()=>c.value.length>0)),g=ar((()=>!(f.value||p.value||v.value||h.value||m.value)));return(e,t)=>(Sl(),ql("div",ay,[Vl("div",iy,[Vl("img",{class:"logo-img",src:qt("assets/logo.eb4b912e.png"),alt:""},null,8,sy),Vl("img",{class:"logo-img--text",src:qt("assets/logo-text.10d5eeb5.png"),alt:""},null,8,uy)]),Vl("div",cy,[an(Ml(vb,{title:"hcaptcha",rows:qt(n),columns:qt(o),"row-key":"name"},{"body-cell-jsonValue":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"","model-value":e.row.jsonValue,"onUpdate:modelValue":t=>e.row.jsonValue=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),_:1},8,["rows","columns"]),[[Rr,f.value]]),an(Ml(vb,{title:"funcaptcha",rows:qt(l),columns:qt(r),"row-key":"name"},{"body-cell-jsonValue":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"","model-value":e.row.jsonValue,"onUpdate:modelValue":t=>e.row.jsonValue=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),"body-cell-bda":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"","model-value":e.row.bda,"onUpdate:modelValue":t=>e.row.bda=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),"body-cell-userAgent":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"","model-value":e.row.userAgent,"onUpdate:modelValue":t=>e.row.userAgent=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),_:1},8,["rows","columns"]),[[Rr,p.value]]),an(Ml(vb,{title:"recaptcha",rows:qt(a),columns:qt(i),"row-key":"name"},{"body-cell-jsonValue":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"","model-value":e.row.jsonValue,"onUpdate:modelValue":t=>e.row.jsonValue=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),_:1},8,["rows","columns"]),[[Rr,v.value]]),an(Ml(vb,{title:"geetestV4",rows:qt(s),columns:qt(u),"row-key":"name"},null,8,["rows","columns"]),[[Rr,h.value]]),an(Ml(vb,{title:"Cloudflare turnstile",rows:qt(c),columns:qt(d),"row-key":"name"},{"body-cell-website":rn((e=>[Ml(zg,{props:e},{default:rn((()=>[Ml(vm,{type:"textarea",outlined:"",readonly:"",autogrow:"","model-value":e.row.website,"onUpdate:modelValue":t=>e.row.website=t},null,8,["model-value","onUpdate:modelValue"])])),_:2},1032,["props"])])),_:1},8,["rows","columns"]),[[Rr,m.value]]),an(Vl("div",dy,t[0]||(t[0]=[Vl("span",null,"if you want to get captcha info, please reload page.",-1)]),512),[[Rr,g.value]])])]))}}),[["__scopeId","data-v-0f8e432f"]])}];var py=function(){return iu({scrollBehavior:()=>({left:0,top:0}),routes:fy,history:ws("")})};async function vy({app:e,router:t,store:n},o){let l=!1;const r=e=>{if(l=!0,"string"==typeof e&&/^https?:\/\//.test(e))return void(window.location.href=e);const n=(e=>{try{return t.resolve(e).href}catch(n){}return Object(e)===e?null:e})(e);null!==n&&(window.location.href=n,window.location.reload())},a=window.location.href.replace(window.location.origin,"");for(let s=0;!1===l&&s<o.length;s++)try{await o[s]({app:e,router:t,store:n,ssrContext:null,redirect:r,urlPath:a,publicPath:""})}catch(i){return i&&i.url?void r(i.url):void console.error("[Quasar] boot error:",i)}!0!==l&&(e.use(t),chrome.runtime.id&&setTimeout((()=>{!function(){const t=(e,t)=>{const n=chrome.runtime.connect({name:"app:"+e});let o=!1;n.onDisconnect.addListener((()=>{o=!0})),t(new Ea({listen(e){n.onMessage.addListener(e)},send(e){o||n.postMessage(e)}}))},n=e=>{const n=chrome.devtools?chrome.devtools.inspectedWindow.tabId:aa();t(n,e)};var o;o=t=>{window.QBexBridge=t,e.config.globalProperties.$q.bex=window.QBexBridge,e.mount("#q-app")},chrome.tabs&&!chrome.devtools?chrome.tabs.getCurrent((e=>{e&&e.id?t(e.id,o):n(o)})):n(o)}()}),300))}(async function(e,t){const n=e(xi);n.use(wi,t);const o="function"==typeof Ti?await Ti({}):Ti;n.use(o);const l=wt("function"==typeof py?await py({store:o}):py);return o.use((({store:e})=>{e.router=l})),{app:n,store:o,router:l}})(((...e)=>{const t=ta().createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(w(e)){return document.querySelector(e)}return e}(e);if(!o)return;const l=t._component;_(l)||l.render||l.template||(l.template=o.innerHTML),1===o.nodeType&&(o.textContent="");const r=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t}),{config:{dark:!1}}).then((e=>{const[t,n]=void 0!==Promise.allSettled?["allSettled",e=>e.map((e=>{if("rejected"!==e.status)return e.value.default;console.error("[Quasar] boot error:",e.reason)}))]:["all",e=>e.map((e=>e.default))];return Promise[t]([r((()=>Promise.resolve().then((function(){return Zh}))),void 0)]).then((t=>{const o=n(t).filter((e=>"function"==typeof e));vy(e,o)}))}));export{qm as Q,ki as _,Vl as a,Ml as b,ql as c,Sl as o};
