"use strict";(()=>{var He=Object.create;var Z=Object.defineProperty;var Ue=Object.getOwnPropertyDescriptor;var Pe=Object.getOwnPropertyNames;var je=Object.getPrototypeOf,We=Object.prototype.hasOwnProperty;var Ke=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ve=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Pe(t))!We.call(e,o)&&o!==n&&Z(e,o,{get:()=>t[o],enumerable:!(r=Ue(t,o))||r.enumerable});return e};var qe=(e,t,n)=>(n=e!=null?He(je(e)):{},Ve(t||!e||!e.__esModule?Z(n,"default",{value:e,enumerable:!0}):n,e));var ue=Ke((Et,j)=>{"use strict";var v=typeof Reflect=="object"?Reflect:null,ee=v&&typeof v.apply=="function"?v.apply:function(t,n,r){return Function.prototype.apply.call(t,n,r)},_;v&&typeof v.ownKeys=="function"?_=v.ownKeys:Object.getOwnPropertySymbols?_=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:_=function(t){return Object.getOwnPropertyNames(t)};function Ge(e){console&&console.warn&&console.warn(e)}var ne=Number.isNaN||function(t){return t!==t};function i(){i.init.call(this)}j.exports=i;j.exports.once=Je;i.EventEmitter=i;i.prototype._events=void 0;i.prototype._eventsCount=0;i.prototype._maxListeners=void 0;var te=10;function A(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return te},set:function(e){if(typeof e!="number"||e<0||ne(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");te=e}});i.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};i.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||ne(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function re(e){return e._maxListeners===void 0?i.defaultMaxListeners:e._maxListeners}i.prototype.getMaxListeners=function(){return re(this)};i.prototype.emit=function(t){for(var n=[],r=1;r<arguments.length;r++)n.push(arguments[r]);var o=t==="error",a=this._events;if(a!==void 0)o=o&&a.error===void 0;else if(!o)return!1;if(o){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var c=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw c.context=s,c}var f=a[t];if(f===void 0)return!1;if(typeof f=="function")ee(f,this,n);else for(var u=f.length,p=ce(f,u),r=0;r<u;++r)ee(p[r],this,n);return!0};function oe(e,t,n,r){var o,a,s;if(A(n),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),a=e._events),s=a[t]),s===void 0)s=a[t]=n,++e._eventsCount;else if(typeof s=="function"?s=a[t]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),o=re(e),o>0&&s.length>o&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=s.length,Ge(c)}return e}i.prototype.addListener=function(t,n){return oe(this,t,n,!1)};i.prototype.on=i.prototype.addListener;i.prototype.prependListener=function(t,n){return oe(this,t,n,!0)};function $e(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function ae(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},o=$e.bind(r);return o.listener=n,r.wrapFn=o,o}i.prototype.once=function(t,n){return A(n),this.on(t,ae(this,t,n)),this};i.prototype.prependOnceListener=function(t,n){return A(n),this.prependListener(t,ae(this,t,n)),this};i.prototype.removeListener=function(t,n){var r,o,a,s,c;if(A(n),o=this._events,o===void 0)return this;if(r=o[t],r===void 0)return this;if(r===n||r.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete o[t],o.removeListener&&this.emit("removeListener",t,r.listener||n));else if(typeof r!="function"){for(a=-1,s=r.length-1;s>=0;s--)if(r[s]===n||r[s].listener===n){c=r[s].listener,a=s;break}if(a<0)return this;a===0?r.shift():ze(r,a),r.length===1&&(o[t]=r[0]),o.removeListener!==void 0&&this.emit("removeListener",t,c||n)}return this};i.prototype.off=i.prototype.removeListener;i.prototype.removeAllListeners=function(t){var n,r,o;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var a=Object.keys(r),s;for(o=0;o<a.length;++o)s=a[o],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=r[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(o=n.length-1;o>=0;o--)this.removeListener(t,n[o]);return this};function se(e,t,n){var r=e._events;if(r===void 0)return[];var o=r[t];return o===void 0?[]:typeof o=="function"?n?[o.listener||o]:[o]:n?Qe(o):ce(o,o.length)}i.prototype.listeners=function(t){return se(this,t,!0)};i.prototype.rawListeners=function(t){return se(this,t,!1)};i.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):ie.call(e,t)};i.prototype.listenerCount=ie;function ie(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}i.prototype.eventNames=function(){return this._eventsCount>0?_(this._events):[]};function ce(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function ze(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function Qe(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function Je(e,t){return new Promise(function(n,r){function o(s){e.removeListener(t,a),r(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",o),n([].slice.call(arguments))}le(e,t,a,{once:!0}),t!=="error"&&Xe(e,o,{once:!0})})}function Xe(e,t,n){typeof e.on=="function"&&le(e,"error",t,n)}function le(e,t,n,r){if(typeof e.on=="function")r.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function o(a){r.once&&e.removeEventListener(t,o),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var de=qe(ue());var W,O=0,l=new Array(256);for(let e=0;e<256;e++)l[e]=(e+256).toString(16).substring(1);var Ye=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let r=t;r>0;r--)n.push(Math.floor(Math.random()*256));return n}})(),fe=4096;function pe(){(W===void 0||O+16>fe)&&(O=0,W=Ye(fe));let e=Array.prototype.slice.call(W,O,O+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,l[e[0]]+l[e[1]]+l[e[2]]+l[e[3]]+"-"+l[e[4]]+l[e[5]]+"-"+l[e[6]]+l[e[7]]+"-"+l[e[8]]+l[e[9]]+"-"+l[e[10]]+l[e[11]]+l[e[12]]+l[e[13]]+l[e[14]]+l[e[15]]}var Ze={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>K(n)+K(e[n])+t,0):0},K=e=>Ze[typeof e](e),T=class extends de.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(r=>this._emit(r)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,r=>{n({...r,respond:o=>this.send(r.eventResponseKey,o)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],r=`${n.event}.${pe()}`,o=r+".result";return new Promise((a,s)=>{let c=[],f=u=>{if(u!==void 0&&u._chunkSplit){let p=u._chunkSplit;c=[...c,...u.data],p.lastChunk&&(this.off(o,f),a(c))}else this.off(o,f),a(u)};this.on(o,f);try{let u=t.map(p=>({...p,payload:{data:p.payload,eventResponseKey:o}}));this.wall.send(u)}catch(u){let p="Message length exceeded maximum allowed length.";if(u.message===p&&Array.isArray(n.payload)){let y=K(n);if(y>this._maxMessageSize){let b=Math.ceil(y/this._maxMessageSize),d=Math.ceil(n.payload.length/b),U=n.payload;for(let I=0;I<b;I++){let P=Math.min(U.length,d);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:b,lastChunk:I===b-1},data:U.splice(0,P)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var me=(e,t)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===t){let r=n.data[0],o=e.getEvents();for(let a in o)a===r.event&&o[a](r.payload)}},!1)};var et=chrome.runtime.getURL("assets/config.js"),ge,M=(ge=globalThis.browser)!=null?ge:globalThis.chrome;async function tt(){var J,X;let e=await M.storage.local.get("defaultConfig");if((J=e.defaultConfig)!=null&&J.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],r=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],o=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,c=(await(await fetch(et)).text()).replace(o,""),f=c.slice(c.indexOf("{")+1,c.lastIndexOf("}")),u=JSON.stringify(f).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),p=u.indexOf("blackUrlList"),y=u.slice(p),b=y.indexOf("],"),d=y.slice(0,b+1);u.replace(d,"").split(",").forEach(Be=>{let[R,Y]=Be.split(":");if(R&&Y){let x=Y.replaceAll("'","").replaceAll('"',"");for(let h=0;h<n.length;h++)R.endsWith(n[h])&&(x=Number(x));for(let h=0;h<r.length;h++)R.startsWith(r[h])&&(x=x==="true");t[R]=x}}),d=d.replaceAll("'","").replaceAll('"',"");let P=d.indexOf(":["),Fe=d.slice(P+2,d.length-1);t.blackUrlList=Fe.split(",");let S=await M.storage.local.get("config");return(X=S==null?void 0:S.config)!=null&&X.apiKey&&(t.apiKey=S.config.apiKey),M.storage.local.set({defaultConfig:t}),t}var w={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},he={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function be(){let e=await tt(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!he[n].includes(e[n]))){{if(n.endsWith("Mode")&&!he.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;w.port=e.port}}Reflect.has(w,n)&&typeof w[n]==typeof e[n]&&(w[n]=e[n])}return w}var nt=be(),m={default:nt,async get(e){return(await this.getAll())[e]},async getAll(){let e=await be(),t=await M.storage.local.get("config");return m.joinConfig(e,t.config)},async set(e){let t=await m.getAll(),n=m.joinConfig(t,e);return M.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let r in e)n[r]=e[r];if(t)for(let r in t)n[r]=t[r];return n}};function ve(e){return new Promise((t,n)=>{let r=new Image;r.src=e,r.setAttribute("crossOrigin","anonymous"),r.onload=()=>{let o=document.createElement("canvas");o.width=r.width,o.height=r.height,o.getContext("2d").drawImage(r,0,0,r.width,r.height);let s=o.toDataURL();t(s)},r.onerror=o=>{n(o)}})}function Ce(e){return new Promise(t=>setTimeout(t,e))}function C(e,t){var n;return"KeyboardEvent"in window?n=new window.KeyboardEvent(t,{bubbles:!0,cancelable:!1}):(n=e.ownerDocument.createEvent("Events"),n.initEvent(t,!0,!1),n.charCode=0,n.keyCode=0,n.which=0,n.srcElement=e,n.target=e),n}function rt(e){return!e||e&&typeof e.click!="function"?!1:(e.click(),!0)}function ot(e,t){if(t){var n=e.value;e.focus(),e.value!==n&&(e.value=n)}else e.focus()}function at(e){var t=e.value;rt(e),ot(e,!1),e.dispatchEvent(C(e,"keydown")),e.dispatchEvent(C(e,"keypress")),e.dispatchEvent(C(e,"keyup")),e.value!==t&&(e.value=t)}function st(e){var t=e.value,n=e.ownerDocument.createEvent("HTMLEvents"),r=e.ownerDocument.createEvent("HTMLEvents");e.dispatchEvent(C(e,"keydown")),e.dispatchEvent(C(e,"keypress")),e.dispatchEvent(C(e,"keyup")),r.initEvent("input",!0,!0),e.dispatchEvent(r),n.initEvent("change",!0,!0),e.dispatchEvent(n),e.blur(),e.value!==t&&(e.value=t)}async function V(e,t){e.value=t,at(e),st(e)}var N="capsolver-image-to-text-source",L="capsolver-image-to-text-result",D=[],ye=0;function F(e){let t="",n="",r=[];return e.style.backgroundImage?n=e.style.backgroundImage:e.style.background&&(n=e.style.background),r=n.split(","),r.find(a=>a.startsWith("url("))?(t=n.slice(5,n.length-2),t.startsWith("blob:")?t.slice(5):t):""}function ct(){let e="["+N+"]",t=document.querySelectorAll(e),n=[];return Array.from(t).forEach(r=>{let o=r.tagName,a="";o==="IMG"?a=r.getAttribute("src"):a=F(r),a&&n.push(r)}),n}function lt(){let e="input["+L+"]";return Array.from(document.querySelectorAll(e))}function ut(e){let t=e.naturalWidth,n=e.naturalHeight,r=document.createElement("canvas");return Object.assign(r,{width:t,height:n}),r.getContext("2d").drawImage(e,0,0,t,n,0,0,t,n),r.toDataURL("image/jpeg")}async function ft(e){if(e.tagName==="IMG")return ut(e);{let n=F(e);return await ve(n)}}function pt(e,t){let n=[];return t.forEach(r=>{let o=r.getAttribute(L),a=e.find(s=>s.getAttribute(N)===o);a&&n.push({image:a,result:r,id:o})}),n}async function dt(e,t){let n=await ft(e.image),r={body:n.slice(n.indexOf(";base64,")+8),id:e.id},o={action:"solver",captchaType:"textCaptcha",params:r};chrome.runtime.sendMessage(o).then(a=>{var s;if(!(a!=null&&a.response)||((s=a==null?void 0:a.response)==null?void 0:s.error)){ye++,ye<=t&&D.splice(D.indexOf(e.id),1);return}vt(a.response)})}var mt=[{value:"mul",label:"\xD7"},{value:"add",label:"+"},{value:"subtract",label:"-"}],ht=new Map([["add","+"],["subtract","-"],["mul","\xD7"]]);function gt(e,t){let r=e.slice(0,e.length-1).split(ht.get(t));if(isNaN(Number(r[0]))||isNaN(Number(r[1])))return NaN;let o;switch(t){case"add":{o=Number(r[0])+Number(r[1]);break}case"subtract":{o=Number(r[0])-Number(r[1]);break}case"mul":{o=Number(r[0])*Number(r[1]);break}}return o}function bt(e){return e[e.length-1]!=="="?!1:mt.find(n=>e.indexOf(n.label)!==-1).value}function vt(e){var a;let t=(a=e.response)==null?void 0:a.solution,n=e.id,r=document.querySelector(`input[${L}="${n}"]`);if(!r)return;let o=bt(t.text);if(!o)V(r,t.text);else{let s=gt(t.text,o);V(r,isNaN(s)?t.text:s)}chrome.runtime.sendMessage({action:"solved"})}function xe(e){N=e.textCaptchaSourceAttribute||N,L=e.textCaptchaResultAttribute||L;let t=ct();if(t.length<=0)return!1;let n=lt();if(n.length<=0)return!1;let r=pt(t,n);return r.length<=0?!1:r}function Te(e,t){let n=e.length;for(let r=0;r<n;r++)D.includes(e[r].id)||(dt(e[r],t),D.push(e[r].id))}var $,g,k,q=0,G=0,we,E=(we=globalThis.browser)!=null?we:globalThis.chrome;function Me(){document.addEventListener("contextmenu",e=>{g=null,k=null;let t=e.target,n=t.tagName,r="";n==="IMG"?r=t.getAttribute("src"):r=F(t),r?g=t:k=t})}function z(){let e=window.location;return e.protocol+"//"+e.hostname+e.pathname}function Le(e){let t="",n=e.tagName.toLowerCase(),r=e.id;if(r)t=`${n}#${r}`;else{let o=Array.from(e.attributes),a="";for(let s=0;s<o.length;s++){let c=o[s];["id","class","onclick"].includes(c.name)||(a+="["+c.name+'="'+c.value+'"]')}t=`${n}${a}`}return t}async function B(e){let t=await m.getAll();if(e==="source")return t.textCaptchaSourceAttribute||"capsolver-image-to-text-source";if(e==="result")return t.textCaptchaResultAttribute||"capsolver-image-to-text-result"}async function Ct(e){var c;let t=Le(e),n=z(),r=await E.storage.local.get("imageUrls"),o=(c=r==null?void 0:r.imageUrls)!=null?c:{},a=o.hasOwnProperty(n)?o[n]:{},s=Object.assign({},a,{image:t});o.hasOwnProperty(n)?Object.assign(o[n],s):o[n]=s,E.storage.local.set({imageUrls:o})}async function yt(e){let t=Le(e),n=z(),o=(await E.storage.local.get("imageUrls")).imageUrls,a=o[n],s=Object.assign({},a,{input:t});o[n]=s,E.storage.local.set({imageUrls:o})}function ke(e){let t=e.indexOf("[");return t===-1?"id":e.indexOf("#")>t?"attr":"id"}async function xt(e){if(!e)return;let t,n=await B("source");ke(e)==="attr"?t=document.querySelector(e):t=document.getElementById(e.slice(e.indexOf("#")+1)),t==null||t.setAttribute(n,String(q))}async function Tt(e){if(!e)return;let t,n=await B("result");ke(e)==="attr"?t=document.querySelector(e):t=document.getElementById(e.slice(e.indexOf("#")+1)),t==null||t.setAttribute(n,String(G))}function wt(e){let t=e.image,n=e.input;xt(t),Tt(n)}function Ee(){return!g}function Ie(){return!!$}async function Se(){let e=await B("source");!g||(g.setAttribute(e,String(q)),$=g,q++,chrome.runtime.sendMessage({action:"updateMenu"}),Ct(g))}async function Re(){let e=await B("result");!k||(k.setAttribute(e,String(G)),$=null,G++,chrome.runtime.sendMessage({action:"updateMenu"}),yt(k))}async function _e(){let e=z(),t=await E.storage.local.get("imageUrls");if(!t)return;let n=t==null?void 0:t.imageUrls;if(!n)return;let r=n[e];!r||wt(r)}async function Ae(e){!e.useCapsolver||!e.enabledForImageToText||!e.apiKey||e.enabledForBlacklistControl&&e.isInBlackList||(await Ce(e.textCaptchaDelayTime),setInterval(async()=>{let t=xe(e);!t||Te(t,e.textCaptchaRepeatTimes)},1e3))}var H=null;H&&window.clearInterval(H);H=window.setInterval(async()=>{let e=await m.getAll();!e.isInit||e.manualSolving||(Ae(e),window.clearInterval(H))},100);window.addEventListener("message",async function(e){if(e.data.type!=="capsolverSolve")return;let t=await m.getAll();!t.manualSolving||Ae(t)});Me();chrome.runtime.onMessage.addListener((e,t,n)=>{let{command:r}=e;switch(r){case"image2Text:canMarkImage":let o=Ee();n(o);break;case"image2Text:canMarkInput":let a=Ie();n(a);break;case"image2Text:markedImage":Se(),n(null);break;case"image2Text:markedResult":Re(),n(null);break}return!1});var Mt=setInterval(function(){document.readyState==="complete"&&(_e(),clearInterval(Mt))},1e3),Oe=e=>{};var Q=chrome.runtime.connect({name:"contentScript"}),Ne=!1;Q.onDisconnect.addListener(()=>{Ne=!0});var De=new T({listen(e){Q.onMessage.addListener(e)},send(e){Ne||(Q.postMessage(e),window.postMessage({...e,from:"bex-content-script"},"*"))}});function Lt(e){let t=document.createElement("script");t.src=e,t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)}document instanceof HTMLDocument&&Lt(chrome.runtime.getURL("dom.js"));me(De,"bex-dom");Oe(De);})();
