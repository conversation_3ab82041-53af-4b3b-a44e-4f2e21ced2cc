"use strict";(()=>{var Le=Object.create;var N=Object.defineProperty;var Ie=Object.getOwnPropertyDescriptor;var Me=Object.getOwnPropertyNames;var Se=Object.getPrototypeOf,Pe=Object.prototype.hasOwnProperty;var _e=(e,t,n)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ae=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ee=(e,t,n,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Me(t))!Pe.call(e,o)&&o!==n&&N(e,o,{get:()=>t[o],enumerable:!(a=Ie(t,o))||a.enumerable});return e};var Ue=(e,t,n)=>(n=e!=null?Le(Se(e)):{},Ee(t||!e||!e.__esModule?N(n,"default",{value:e,enumerable:!0}):n,e));var C=(e,t,n)=>(_e(e,typeof t!="symbol"?t+"":t,n),n);var se=Ae((Tt,B)=>{"use strict";var T=typeof Reflect=="object"?Reflect:null,Q=T&&typeof T.apply=="function"?T.apply:function(t,n,a){return Function.prototype.apply.call(t,n,a)},_;T&&typeof T.ownKeys=="function"?_=T.ownKeys:Object.getOwnPropertySymbols?_=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:_=function(t){return Object.getOwnPropertyNames(t)};function Oe(e){console&&console.warn&&console.warn(e)}var X=Number.isNaN||function(t){return t!==t};function c(){c.init.call(this)}B.exports=c;B.exports.once=Be;c.EventEmitter=c;c.prototype._events=void 0;c.prototype._eventsCount=0;c.prototype._maxListeners=void 0;var J=10;function A(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return J},set:function(e){if(typeof e!="number"||e<0||X(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");J=e}});c.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};c.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||X(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function Z(e){return e._maxListeners===void 0?c.defaultMaxListeners:e._maxListeners}c.prototype.getMaxListeners=function(){return Z(this)};c.prototype.emit=function(t){for(var n=[],a=1;a<arguments.length;a++)n.push(arguments[a]);var o=t==="error",s=this._events;if(s!==void 0)o=o&&s.error===void 0;else if(!o)return!1;if(o){var r;if(n.length>0&&(r=n[0]),r instanceof Error)throw r;var i=new Error("Unhandled error."+(r?" ("+r.message+")":""));throw i.context=r,i}var u=s[t];if(u===void 0)return!1;if(typeof u=="function")Q(u,this,n);else for(var l=u.length,f=ae(u,l),a=0;a<l;++a)Q(f[a],this,n);return!0};function Y(e,t,n,a){var o,s,r;if(A(n),s=e._events,s===void 0?(s=e._events=Object.create(null),e._eventsCount=0):(s.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),r=s[t]),r===void 0)r=s[t]=n,++e._eventsCount;else if(typeof r=="function"?r=s[t]=a?[n,r]:[r,n]:a?r.unshift(n):r.push(n),o=Z(e),o>0&&r.length>o&&!r.warned){r.warned=!0;var i=new Error("Possible EventEmitter memory leak detected. "+r.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");i.name="MaxListenersExceededWarning",i.emitter=e,i.type=t,i.count=r.length,Oe(i)}return e}c.prototype.addListener=function(t,n){return Y(this,t,n,!1)};c.prototype.on=c.prototype.addListener;c.prototype.prependListener=function(t,n){return Y(this,t,n,!0)};function De(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function ee(e,t,n){var a={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},o=De.bind(a);return o.listener=n,a.wrapFn=o,o}c.prototype.once=function(t,n){return A(n),this.on(t,ee(this,t,n)),this};c.prototype.prependOnceListener=function(t,n){return A(n),this.prependListener(t,ee(this,t,n)),this};c.prototype.removeListener=function(t,n){var a,o,s,r,i;if(A(n),o=this._events,o===void 0)return this;if(a=o[t],a===void 0)return this;if(a===n||a.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete o[t],o.removeListener&&this.emit("removeListener",t,a.listener||n));else if(typeof a!="function"){for(s=-1,r=a.length-1;r>=0;r--)if(a[r]===n||a[r].listener===n){i=a[r].listener,s=r;break}if(s<0)return this;s===0?a.shift():Fe(a,s),a.length===1&&(o[t]=a[0]),o.removeListener!==void 0&&this.emit("removeListener",t,i||n)}return this};c.prototype.off=c.prototype.removeListener;c.prototype.removeAllListeners=function(t){var n,a,o;if(a=this._events,a===void 0)return this;if(a.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):a[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete a[t]),this;if(arguments.length===0){var s=Object.keys(a),r;for(o=0;o<s.length;++o)r=s[o],r!=="removeListener"&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=a[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(o=n.length-1;o>=0;o--)this.removeListener(t,n[o]);return this};function te(e,t,n){var a=e._events;if(a===void 0)return[];var o=a[t];return o===void 0?[]:typeof o=="function"?n?[o.listener||o]:[o]:n?Ne(o):ae(o,o.length)}c.prototype.listeners=function(t){return te(this,t,!0)};c.prototype.rawListeners=function(t){return te(this,t,!1)};c.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):ne.call(e,t)};c.prototype.listenerCount=ne;function ne(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}c.prototype.eventNames=function(){return this._eventsCount>0?_(this._events):[]};function ae(e,t){for(var n=new Array(t),a=0;a<t;++a)n[a]=e[a];return n}function Fe(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function Ne(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function Be(e,t){return new Promise(function(n,a){function o(r){e.removeListener(t,s),a(r)}function s(){typeof e.removeListener=="function"&&e.removeListener("error",o),n([].slice.call(arguments))}oe(e,t,s,{once:!0}),t!=="error"&&Ke(e,o,{once:!0})})}function Ke(e,t,n){typeof e.on=="function"&&oe(e,"error",t,n)}function oe(e,t,n,a){if(typeof e.on=="function")a.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function o(s){a.once&&e.removeEventListener(t,o),n(s)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var ce=Ue(se());var K,E=0,p=new Array(256);for(let e=0;e<256;e++)p[e]=(e+256).toString(16).substring(1);var je=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let a=t;a>0;a--)n.push(Math.floor(Math.random()*256));return n}})(),re=4096;function ie(){(K===void 0||E+16>re)&&(E=0,K=je(re));let e=Array.prototype.slice.call(K,E,E+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,p[e[0]]+p[e[1]]+p[e[2]]+p[e[3]]+"-"+p[e[4]]+p[e[5]]+"-"+p[e[6]]+p[e[7]]+"-"+p[e[8]]+p[e[9]]+"-"+p[e[10]]+p[e[11]]+p[e[12]]+p[e[13]]+p[e[14]]+p[e[15]]}var qe={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>j(n)+j(e[n])+t,0):0},j=e=>qe[typeof e](e),x=class extends ce.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(a=>this._emit(a)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,a=>{n({...a,respond:o=>this.send(a.eventResponseKey,o)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],a=`${n.event}.${ie()}`,o=a+".result";return new Promise((s,r)=>{let i=[],u=l=>{if(l!==void 0&&l._chunkSplit){let f=l._chunkSplit;i=[...i,...l.data],f.lastChunk&&(this.off(o,u),s(i))}else this.off(o,u),s(l)};this.on(o,u);try{let l=t.map(f=>({...f,payload:{data:f.payload,eventResponseKey:o}}));this.wall.send(l)}catch(l){let f="Message length exceeded maximum allowed length.";if(l.message===f&&Array.isArray(n.payload)){let k=j(n);if(k>this._maxMessageSize){let w=Math.ceil(k/this._maxMessageSize),m=Math.ceil(n.payload.length/w),D=n.payload;for(let M=0;M<w;M++){let F=Math.min(D.length,m);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:w,lastChunk:M===w-1},data:D.splice(0,F)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var He=chrome.runtime.getURL("assets/config.js"),ue,L=(ue=globalThis.browser)!=null?ue:globalThis.chrome;async function We(){var V,$;let e=await L.storage.local.get("defaultConfig");if((V=e.defaultConfig)!=null&&V.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],a=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],o=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,i=(await(await fetch(He)).text()).replace(o,""),u=i.slice(i.indexOf("{")+1,i.lastIndexOf("}")),l=JSON.stringify(u).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),f=l.indexOf("blackUrlList"),k=l.slice(f),w=k.indexOf("],"),m=k.slice(0,w+1);l.replace(m,"").split(",").forEach(Re=>{let[P,z]=Re.split(":");if(P&&z){let v=z.replaceAll("'","").replaceAll('"',"");for(let y=0;y<n.length;y++)P.endsWith(n[y])&&(v=Number(v));for(let y=0;y<a.length;y++)P.startsWith(a[y])&&(v=v==="true");t[P]=v}}),m=m.replaceAll("'","").replaceAll('"',"");let F=m.indexOf(":["),xe=m.slice(F+2,m.length-1);t.blackUrlList=xe.split(",");let S=await L.storage.local.get("config");return($=S==null?void 0:S.config)!=null&&$.apiKey&&(t.apiKey=S.config.apiKey),L.storage.local.set({defaultConfig:t}),t}var R={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},le={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function pe(){let e=await We(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!le[n].includes(e[n]))){{if(n.endsWith("Mode")&&!le.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;R.port=e.port}}Reflect.has(R,n)&&typeof R[n]==typeof e[n]&&(R[n]=e[n])}return R}var Ge=pe(),h={default:Ge,async get(e){return(await this.getAll())[e]},async getAll(){let e=await pe(),t=await L.storage.local.get("config");return h.joinConfig(e,t.config)},async set(e){let t=await h.getAll(),n=h.joinConfig(t,e);return L.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let a in e)n[a]=e[a];if(t)for(let a in t)n[a]=t[a];return n}};function fe(e){e.on("config",async({respond:t})=>{let n=await h.getAll();t(n).then()})}function de(e){e.on("log",({data:t,respond:n})=>{n()})}var Ve="https://www.google-analytics.com/mp/collect",$e="https://www.google-analytics.com/debug/mp/collect",ze="G-4M0CNKY3DG",Qe="5f5uGZ8yS9er8l9xMXdDBA";var q=class{constructor(t=!1){C(this,"debug",!1);this.debug=t}async getOrCreateClientId(){let{clientId:t}=await chrome.storage.local.get("clientId");return t||(t=self.crypto.randomUUID(),await chrome.storage.local.set({clientId:t})),t}async getOrCreateSessionId(){let{sessionData:t}=await chrome.storage.session.get("sessionData"),n=Date.now();return t&&t.timestamp&&((n-t.timestamp)/6e4>30?t=null:(t.timestamp=n,await chrome.storage.session.set({sessionData:t}))),t||(t={session_id:n.toString(),timestamp:n.toString()},await chrome.storage.session.set({sessionData:t})),t.session_id}async fireEvent(t,n={}){if(!n.session_id){let a=await this.getOrCreateSessionId();n.session_id=a}n.engagement_time_msec||(n.engagement_time_msec=100);try{let a=await fetch(`${this.debug?$e:Ve}?measurement_id=${ze}&api_secret=${Qe}`,{method:"POST",body:JSON.stringify({client_id:await this.getOrCreateClientId(),events:[{name:t,params:n}]})});if(!this.debug)return}catch(a){console.error("Google Analytics request failed with an exception",a)}}},H=new q;var U=class{constructor(t){C(this,"baseURL");this.baseURL=t}async post(t,n,a){let o=await fetch(this.getURL(t),{method:"POST",body:JSON.stringify(n),headers:{"Content-Type":"application/json"},...a});return{status:o.status,statusText:o.statusText,data:await o.json(),headers:o.headers}}getURL(t){return this.baseURL+t}};var d=class{constructor(t){C(this,"options",{apiKey:"",service:"https://api.capsolver.com",defaultTimeout:120,pollingInterval:5,recaptchaTimeout:600});C(this,"http");for(let n in this.options)this.options[n]=t[n]===void 0?this.options[n]:t[n];this.http=new U(this.options.service)}static async API(t){let n=await h.getAll();if(!(t!=null&&t.apiKey)&&!(n!=null&&n.apiKey))throw new Error("Capsover: No API Kye set up yet!");return new d({apiKey:n.apiKey,...t})}async getProxyParams(t){let n=await h.getAll();return{proxyType:n.proxyType,proxyAddress:n.hostOrIp,proxyPort:n.port,proxyLogin:n.proxyLogin,proxyPassword:n.proxyPassword,type:t.type.replace("ProxyLess","")}}async getBalance(){var n,a,o;let t=await this.http.post("/getBalance",{clientKey:this.options.apiKey});if(t.status!==200||((n=t.data)==null?void 0:n.errorCode)||((a=t.data)==null?void 0:a.errorId))throw new Error(((o=t.data)==null?void 0:o.errorDescription)||"createTask fail\uFF01");return t.data}async createTaskResult(t,n){n||(n={timeout:this.options.defaultTimeout,pollingInterval:this.options.pollingInterval});let a=await h.getAll();if(a.appId&&(t.appId=a.appId),a.useProxy){let l=await this.getProxyParams(t.task);Object.assign(t.task,l)}let o=await this.createTask(t),{taskId:s}=o,r=this.getTime(),i=n.timeout===void 0?this.options.defaultTimeout:n.timeout,u=n.pollingInterval===void 0?this.options.pollingInterval:n.pollingInterval;for(;!(this.getTime()-r>i);){await new Promise(f=>setTimeout(f,u*1e3));let l=await this.getTaskSolution({taskId:s});if(l.status==="ready")return l}throw new Error("Timeout "+i+" seconds reached")}async createTask(t){var r,i,u,l;let n=(r=globalThis.browser)!=null?r:globalThis.chrome,a=await n.storage.local.get("platform"),o=await n.storage.local.get("version"),s=await this.http.post("/createTask",{clientKey:this.options.apiKey,source:a.platform,version:o.version,...t});if(s.status!==200||((i=s.data)==null?void 0:i.errorCode)||((u=s.data)==null?void 0:u.errorId))throw new Error(((l=s.data)==null?void 0:l.errorCode)||"createTask fail\uFF01");if(!s.data.taskId)throw new Error("taskIs is empty!");return s.data}async getTaskSolution({taskId:t}){var a,o,s;let n=await this.http.post("/getTaskResult",{clientKey:this.options.apiKey,taskId:t});if(n.status!==200||((a=n.data)==null?void 0:a.errorCode)||((o=n.data)==null?void 0:o.errorId))throw new Error(((s=n.data)==null?void 0:s.errorCode)||"getTaskResult fail\uFF01");return n.data}async createRecognitionTask(t){var i,u,l,f;let n=await h.getAll(),a=(i=globalThis.browser)!=null?i:globalThis.chrome,o=await a.storage.local.get("platform"),s=await a.storage.local.get("version");n.appId&&(t.appId=n.appId);let r=await this.http.post("/createTask",{clientKey:this.options.apiKey,source:o.platform,version:s.version,...t});if(r.status!==200||((u=r.data)==null?void 0:u.errorCode)||((l=r.data)==null?void 0:l.errorId)!==0)throw new Error(((f=r.data)==null?void 0:f.errorCode)||"createTask fail\uFF01");if(!r.data.taskId)throw new Error("taskIs is empty!");return r.data}getTime(){return parseInt(String(Date.now()/1e3))}};function Je(e){chrome.contextMenus.update("capsolver-mark-image",{enabled:e})}function Xe(e){chrome.contextMenus.update("capsolver-mark-result",{enabled:e})}function O(e,t){var a;let n=(a=globalThis.browser)!=null?a:globalThis.chrome;return new Promise(o=>{n.tabs.query({active:!0,currentWindow:!0}).then(s=>{if(globalThis!=null&&globalThis.browser)browser.tabs.sendMessage(e,{command:t}).then(r=>{o(r)});else{let r=s.find(u=>u.id===e);(r==null?void 0:r.url)||o(!1),chrome.tabs.sendMessage(e,{command:t},u=>{o(u)})}})})}async function Ze(e){return await O(e,"image2Text:canMarkImage")}async function Ye(e){return await O(e,"image2Text:canMarkInput")}async function he(e){O(e,"image2Text:markedImage")}async function ge(e){O(e,"image2Text:markedResult")}async function I(e){let t=await Ze(e),n=await Ye(e);Je(t),Xe(n)}var be="",b={};function we(e,t,n){let{action:a}=e;return h.getAll().then(o=>{switch(a){case"solver":o[`${e.captchaType}Mode`]==="click"?at(e).then(s=>{n({response:s})}):tt(e,o).then(s=>{n({response:s})});break;case"execute":ye({command:"execute"});break;case"solved":ye({response:{action:"solved",callback:o.solvedCallback}});break;case"updateMenu":I(t.tab.id);break;case"getWebsiteUrl":be=e.websiteUrl;break;case"setWebsiteMetadata":b=e.metadata;break;case"ga":et(e.key);break;case"solveTurnstile":nt(e,o).then(s=>{n({response:s})});break}}),a==="solver"||a==="ga"||a==="solveTurnstile"}async function et(e){let t=await H.getOrCreateSessionId();H.fireEvent(e,{session_id:t})}var Ce,me=(Ce=globalThis.browser)!=null?Ce:globalThis.chrome;async function ye(e){let t=await me.tabs.query({currentWindow:!0,active:!0});for(let n of t)me.tabs.sendMessage(n.id,e)}async function tt(e,t){let{captchaType:n,widgetId:a,params:o,action:s}=e,r={action:s,request:{captchaType:n,widgetId:a}};if(!o)return r.error="params is error!",r;try{r.response=await Te(n,o,t)}catch(i){r.error=String(i)}return r}async function nt(e,t){let n={action:"solveTurnstile",request:{captchaType:"cloudflare",widgetId:"0"}};try{n.response=await Te("cloudflare",{sitekey:e.sitekey,websiteURL:e.websiteURL},t)}catch(a){n.error=String(a)}return n}async function at(e){let{captchaType:t,params:n,action:a}=e,o={action:a,request:{captchaType:t}};if(!n)return o.error="params is error!",o;n.hasOwnProperty("index")&&(o.index=n.index),n.hasOwnProperty("id")&&(o.id=n.id);try{o.response=await ot(t,n)}catch(s){o.error=String(s)}return o}async function Te(e,t,n){let a={code:"",status:"processing"};switch(e){case"hCaptcha":{let o=await st(t);a.code=o.solution.gRecaptchaResponse,a.status=o.status;break}case"reCaptcha":{let o=await rt(t);a.code=o.solution.gRecaptchaResponse,a.status=o.status;break}case"funCaptcha":{let o=await ct(t);a.code=o.solution.token,a.status=o.status;break}case"reCaptcha3":{let o=await it(t);a.code=o.solution.gRecaptchaResponse,a.status=o.status;break}case"cloudflare":{let o=await lt(t);a.code=o.solution.token,a.status=o.status;break}default:throw new Error("do not support captchaType: "+e)}return a}async function ot(e,t){t.url=be;let n={status:"processing"};switch(e){case"funCaptcha":{let a=await ut(t);n.status=a.status,n.solution=a.solution;break}case"hCaptcha":{let a=await pt(t);n.status=a.status,n.solution=a.solution;break}case"reCaptcha":{let a=await ft(t);n.status=a.status,n.solution=a.solution;break}case"textCaptcha":{let a=await dt(t);n.status=a.status,n.solution=a.solution;break}case"awsCaptcha":{let a=await ht(t);n.status=a.status,n.solution=a.solution;break}default:throw new Error("do not support captchaType: "+e)}return n}async function st(e){return await(await d.API()).createTaskResult({task:{type:"HCaptchaTaskProxyLess",websiteURL:e.websiteURL,websiteKey:e.sitekey}})}async function rt(e){var a;let t=await d.API();((a=e.websiteURL)==null?void 0:a.indexOf("tbi.com.iq"))!==-1&&(e.websiteURL="https://apps.tbi.com.iq/dollar/register.aspx");let n={type:"ReCaptchaV2TaskProxyLess",websiteURL:e.websiteURL,websiteKey:e.sitekey,invisible:e.invisible,enterprisePayload:{s:e.s},metadata:{pageURL:b.pageURL,title:b.title}};return e.action&&(n.pageAction=e.action),await t.createTaskResult({task:n})}async function it(e){var a;let t=await d.API(),n=await h.getAll();return((a=e.websiteURL)==null?void 0:a.indexOf("tbi.com.iq"))!==-1&&(e.websiteURL="https://apps.tbi.com.iq/dollar/register.aspx"),await t.createTaskResult({task:{type:n.reCaptcha3TaskType,websiteURL:e.websiteURL,websiteKey:e.sitekey,pageAction:e.action,enterprisePayload:{s:e.s},metadata:{pageURL:b.pageURL,title:b.title}}})}async function ct(e){return await(await d.API()).createTaskResult({task:{type:"FunCaptchaTaskProxyLess",websiteURL:e.websiteURL,websitePublicKey:e.websitePublicKey}})}async function lt(e){return await(await d.API()).createTaskResult({task:{type:"AntiTurnstileTaskProxyLess",websiteURL:e.websiteURL,websiteKey:e.sitekey}})}async function ut(e){return await(await d.API()).createRecognitionTask({task:{type:"FunCaptchaClassification",images:[e.image],question:e.question,websiteURL:e.url}})}async function pt(e){return await(await d.API()).createRecognitionTask({task:{type:"HCaptchaClassification",queries:e.queries,question:e.question,websiteURL:e.url}})}async function ft(e){var a;let t=await d.API();((a=e.url)==null?void 0:a.indexOf("tbi.com.iq"))!==-1&&(e.url="https://apps.tbi.com.iq/dollar/register.aspx");let n={type:"ReCaptchaV2Classification",image:e.image,question:e.question,websiteURL:e.url,metadata:{pageURL:b.pageURL,title:b.title}};return await t.createRecognitionTask({task:n})}async function dt(e){let t=await d.API(),n=await h.getAll();return await t.createRecognitionTask({task:{type:"ImageToTextTask",body:e.body,websiteURL:e.url,module:n.textCaptchaModule}})}async function ht(e){return await(await d.API()).createRecognitionTask({task:{type:"AwsWafClassification",images:e.question==="aws:toycarcity:carcity"?[e.image]:e.image,question:e.question,websiteURL:e.url}})}var ke,G=(ke=globalThis.browser)!=null?ke:globalThis.chrome,W="";async function gt(){let e=chrome.runtime.getURL("manifest.json");return(await(await fetch(e)).json()).version}chrome.runtime.onConnect.addListener(async()=>{W||(W=await gt()),G.storage.local.set({version:W})});chrome.runtime.onMessage.addListener(we);function mt(){chrome.contextMenus.removeAll(()=>{chrome.contextMenus.create({title:"capsolver mark image as captcha",contexts:["all"],id:"capsolver-mark-image",enabled:!0}),chrome.contextMenus.create({title:"select an input for the captcha result",contexts:["editable"],id:"capsolver-mark-result",enabled:!1})})}G.tabs.onActivated.addListener(({tabId:e})=>{I(e)});G.tabs.onUpdated.addListener((e,t)=>{t.status==="complete"&&I(e)});chrome.contextMenus.onClicked.addListener((e,t)=>{switch(e.menuItemId){case"capsolver-mark-image":he(t.id);break;case"capsolver-mark-result":ge(t.id);break}});function yt(e){return new Promise(t=>setTimeout(t,e))}chrome.webRequest.onBeforeRequest.addListener(async e=>{e.url.includes("https://challenges.cloudflare.com/turnstile/")&&e.url.includes("/api.js")&&await yt(1e3)},{urls:["<all_urls>"]});mt();var ve=e=>{de(e),fe(e)};var g={},Ct=e=>{let t=e.sender.tab,n;if(e.name.indexOf(":")>-1){let o=e.name.split(":");n=o[1],e.name=o[0]}t!==void 0&&(n=t.id);let a=g[n];return a||(a=g[n]={}),a[e.name]={port:e,connected:!0,listening:!1},a[e.name]};chrome.runtime.onConnect.addListener(e=>{let t=Ct(e);t.port.onDisconnect.addListener(()=>{t.connected=!1});let n=new x({listen(a){for(let o in g){let s=g[o];s.app&&!s.app.listening&&(s.app.listening=!0,s.app.port.onMessage.addListener(a)),s.contentScript&&!s.contentScript.listening&&(s.contentScript.port.onMessage.addListener(a),s.contentScript.listening=!0)}},send(a){for(let o in g){let s=g[o];s.app&&s.app.connected&&s.app.port.postMessage(a),s.contentScript&&s.contentScript.connected&&s.contentScript.port.postMessage(a)}}});ve(n,g);for(let a in g){let o=g[a];o.app&&o.contentScript&&bt(o.app,o.contentScript)}});function bt(e,t){e.port.onMessage.addListener(n=>{t.connected&&t.port.postMessage(n)}),t.port.onMessage.addListener(n=>{e.connected&&e.port.postMessage(n)})}})();
