import{_ as e,o as t,c as s,a as l,b as a,Q as o}from"./index.360a766e.js";const n={class:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"};var r=e({},[["render",function(e,r){return t(),s("div",n,[l("div",null,[r[0]||(r[0]=l("div",{style:{"font-size":"30vh"}},"404",-1)),r[1]||(r[1]=l("div",{class:"text-h2",style:{opacity:"0.4"}},"Oops. Nothing here...",-1)),a(o,{class:"q-mt-xl",color:"white","text-color":"blue",unelevated:"",to:"/",label:"Go Home","no-caps":""})])])}]]);export{r as default};
