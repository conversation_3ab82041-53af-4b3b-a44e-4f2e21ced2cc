"use strict";(()=>{var Pe=Object.create;var q=Object.defineProperty;var _e=Object.getOwnPropertyDescriptor;var Ae=Object.getOwnPropertyNames;var Fe=Object.getPrototypeOf,Oe=Object.prototype.hasOwnProperty;var He=(t,e,n)=>e in t?q(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var qe=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var Ue=(t,e,n,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Ae(e))!Oe.call(t,r)&&r!==n&&q(t,r,{get:()=>e[r],enumerable:!(o=_e(e,r))||o.enumerable});return t};var je=(t,e,n)=>(n=t!=null?Pe(Fe(t)):{},Ue(e||!t||!t.__esModule?q(n,"default",{value:t,enumerable:!0}):n,t));var g=(t,e,n)=>(He(t,typeof e!="symbol"?e+"":e,n),n);var se=qe((wt,U)=>{"use strict";var b=typeof Reflect=="object"?Reflect:null,J=b&&typeof b.apply=="function"?b.apply:function(e,n,o){return Function.prototype.apply.call(e,n,o)},E;b&&typeof b.ownKeys=="function"?E=b.ownKeys:Object.getOwnPropertySymbols?E=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:E=function(e){return Object.getOwnPropertyNames(e)};function Ne(t){console&&console.warn&&console.warn(t)}var Y=Number.isNaN||function(e){return e!==e};function c(){c.init.call(this)}U.exports=c;U.exports.once=ze;c.EventEmitter=c;c.prototype._events=void 0;c.prototype._eventsCount=0;c.prototype._maxListeners=void 0;var X=10;function R(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return X},set:function(t){if(typeof t!="number"||t<0||Y(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");X=t}});c.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};c.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||Y(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function Z(t){return t._maxListeners===void 0?c.defaultMaxListeners:t._maxListeners}c.prototype.getMaxListeners=function(){return Z(this)};c.prototype.emit=function(e){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);var r=e==="error",a=this._events;if(a!==void 0)r=r&&a.error===void 0;else if(!r)return!1;if(r){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var i=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw i.context=s,i}var f=a[e];if(f===void 0)return!1;if(typeof f=="function")J(f,this,n);else for(var d=f.length,m=re(f,d),o=0;o<d;++o)J(m[o],this,n);return!0};function ee(t,e,n,o){var r,a,s;if(R(n),a=t._events,a===void 0?(a=t._events=Object.create(null),t._eventsCount=0):(a.newListener!==void 0&&(t.emit("newListener",e,n.listener?n.listener:n),a=t._events),s=a[e]),s===void 0)s=a[e]=n,++t._eventsCount;else if(typeof s=="function"?s=a[e]=o?[n,s]:[s,n]:o?s.unshift(n):s.push(n),r=Z(t),r>0&&s.length>r&&!s.warned){s.warned=!0;var i=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");i.name="MaxListenersExceededWarning",i.emitter=t,i.type=e,i.count=s.length,Ne(i)}return t}c.prototype.addListener=function(e,n){return ee(this,e,n,!1)};c.prototype.on=c.prototype.addListener;c.prototype.prependListener=function(e,n){return ee(this,e,n,!0)};function Ke(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function te(t,e,n){var o={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},r=Ke.bind(o);return r.listener=n,o.wrapFn=r,r}c.prototype.once=function(e,n){return R(n),this.on(e,te(this,e,n)),this};c.prototype.prependOnceListener=function(e,n){return R(n),this.prependListener(e,te(this,e,n)),this};c.prototype.removeListener=function(e,n){var o,r,a,s,i;if(R(n),r=this._events,r===void 0)return this;if(o=r[e],o===void 0)return this;if(o===n||o.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,o.listener||n));else if(typeof o!="function"){for(a=-1,s=o.length-1;s>=0;s--)if(o[s]===n||o[s].listener===n){i=o[s].listener,a=s;break}if(a<0)return this;a===0?o.shift():$e(o,a),o.length===1&&(r[e]=o[0]),r.removeListener!==void 0&&this.emit("removeListener",e,i||n)}return this};c.prototype.off=c.prototype.removeListener;c.prototype.removeAllListeners=function(e){var n,o,r;if(o=this._events,o===void 0)return this;if(o.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):o[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete o[e]),this;if(arguments.length===0){var a=Object.keys(o),s;for(r=0;r<a.length;++r)s=a[r],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=o[e],typeof n=="function")this.removeListener(e,n);else if(n!==void 0)for(r=n.length-1;r>=0;r--)this.removeListener(e,n[r]);return this};function ne(t,e,n){var o=t._events;if(o===void 0)return[];var r=o[e];return r===void 0?[]:typeof r=="function"?n?[r.listener||r]:[r]:n?Ve(r):re(r,r.length)}c.prototype.listeners=function(e){return ne(this,e,!0)};c.prototype.rawListeners=function(e){return ne(this,e,!1)};c.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):oe.call(t,e)};c.prototype.listenerCount=oe;function oe(t){var e=this._events;if(e!==void 0){var n=e[t];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}c.prototype.eventNames=function(){return this._eventsCount>0?E(this._events):[]};function re(t,e){for(var n=new Array(e),o=0;o<e;++o)n[o]=t[o];return n}function $e(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function Ve(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}function ze(t,e){return new Promise(function(n,o){function r(s){t.removeListener(e,a),o(s)}function a(){typeof t.removeListener=="function"&&t.removeListener("error",r),n([].slice.call(arguments))}ae(t,e,a,{once:!0}),e!=="error"&&Ge(t,r,{once:!0})})}function Ge(t,e,n){typeof t.on=="function"&&ae(t,"error",e,n)}function ae(t,e,n,o){if(typeof t.on=="function")o.once?t.once(e,n):t.on(e,n);else if(typeof t.addEventListener=="function")t.addEventListener(e,function r(a){o.once&&t.removeEventListener(e,r),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}});var le=je(se());var j,B=0,l=new Array(256);for(let t=0;t<256;t++)l[t]=(t+256).toString(16).substring(1);var Qe=(()=>{let t=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(t!==void 0){if(t.randomBytes!==void 0)return t.randomBytes;if(t.getRandomValues!==void 0)return e=>{let n=new Uint8Array(e);return t.getRandomValues(n),n}}return e=>{let n=[];for(let o=e;o>0;o--)n.push(Math.floor(Math.random()*256));return n}})(),ie=4096;function ce(){(j===void 0||B+16>ie)&&(B=0,j=Qe(ie));let t=Array.prototype.slice.call(j,B,B+=16);return t[6]=t[6]&15|64,t[8]=t[8]&63|128,l[t[0]]+l[t[1]]+l[t[2]]+l[t[3]]+"-"+l[t[4]]+l[t[5]]+"-"+l[t[6]]+l[t[7]]+"-"+l[t[8]]+l[t[9]]+"-"+l[t[10]]+l[t[11]]+l[t[12]]+l[t[13]]+l[t[14]]+l[t[15]]}var Je={undefined:()=>0,boolean:()=>4,number:()=>8,string:t=>2*t.length,object:t=>t?Object.keys(t).reduce((e,n)=>N(n)+N(t[n])+e,0):0},N=t=>Je[typeof t](t),S=class extends le.EventEmitter{constructor(e){super(),this.setMaxListeners(1/0),this.wall=e,e.listen(n=>{Array.isArray(n)?n.forEach(o=>this._emit(o)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(e,n){return this._send([{event:e,payload:n}])}getEvents(){return this._events}on(e,n){return super.on(e,o=>{n({...o,respond:r=>this.send(o.eventResponseKey,r)})})}_emit(e){typeof e=="string"?this.emit(e):this.emit(e.event,e.payload)}_send(e){return this._sendingQueue.push(e),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let e=this._sendingQueue.shift(),n=e[0],o=`${n.event}.${ce()}`,r=o+".result";return new Promise((a,s)=>{let i=[],f=d=>{if(d!==void 0&&d._chunkSplit){let m=d._chunkSplit;i=[...i,...d.data],m.lastChunk&&(this.off(r,f),a(i))}else this.off(r,f),a(d)};this.on(r,f);try{let d=e.map(m=>({...m,payload:{data:m.payload,eventResponseKey:r}}));this.wall.send(d)}catch(d){let m="Message length exceeded maximum allowed length.";if(d.message===m&&Array.isArray(n.payload)){let x=N(n);if(x>this._maxMessageSize){let y=Math.ceil(x/this._maxMessageSize),C=Math.ceil(n.payload.length/y),O=n.payload;for(let D=0;D<y;D++){let H=Math.min(O.length,C);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:y,lastChunk:D===y-1},data:O.splice(0,H)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var pe=(t,e)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===e){let o=n.data[0],r=t.getEvents();for(let a in r)a===o.event&&r[a](o.payload)}},!1)};var Xe=chrome.runtime.getURL("assets/config.js"),ue,L=(ue=globalThis.browser)!=null?ue:globalThis.chrome;async function Ye(){var z,G;let t=await L.storage.local.get("defaultConfig");if((z=t.defaultConfig)!=null&&z.apiKey)return t.defaultConfig;let e={},n=["DelayTime","RepeatTimes","port"],o=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],r=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,i=(await(await fetch(Xe)).text()).replace(r,""),f=i.slice(i.indexOf("{")+1,i.lastIndexOf("}")),d=JSON.stringify(f).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),m=d.indexOf("blackUrlList"),x=d.slice(m),y=x.indexOf("],"),C=x.slice(0,y+1);d.replace(C,"").split(",").forEach(Be=>{let[I,Q]=Be.split(":");if(I&&Q){let T=Q.replaceAll("'","").replaceAll('"',"");for(let v=0;v<n.length;v++)I.endsWith(n[v])&&(T=Number(T));for(let v=0;v<o.length;v++)I.startsWith(o[v])&&(T=T==="true");e[I]=T}}),C=C.replaceAll("'","").replaceAll('"',"");let H=C.indexOf(":["),Re=C.slice(H+2,C.length-1);e.blackUrlList=Re.split(",");let k=await L.storage.local.get("config");return(G=k==null?void 0:k.config)!=null&&G.apiKey&&(e.apiKey=k.config.apiKey),L.storage.local.set({defaultConfig:e}),e}var M={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},de={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function fe(){let t=await Ye(),e=Object.keys(t);for(let n of e)if(!(n==="proxyType"&&!de[n].includes(t[n]))){{if(n.endsWith("Mode")&&!de.mode.includes(t[n]))continue;if(n==="port"){if(typeof t.port!="number")continue;M.port=t.port}}Reflect.has(M,n)&&typeof M[n]==typeof t[n]&&(M[n]=t[n])}return M}var Ze=fe(),p={default:Ze,async get(t){return(await this.getAll())[t]},async getAll(){let t=await fe(),e=await L.storage.local.get("config");return p.joinConfig(t,e.config)},async set(t){let e=await p.getAll(),n=p.joinConfig(e,t);return L.storage.local.set({config:n})},joinConfig(t,e){let n={};if(t)for(let o in t)n[o]=t[o];if(e)for(let o in e)n[o]=e[o];return n}};function ge(t){t.on("config",async({respond:e})=>{let n=await p.getAll();e(n).then()}),t.on("registerCaptchaWidget",({data:e,respond:n})=>{window.registerCaptchaWidget(e)})}function he(){let t=document.querySelector("head > capsolver-widgets");t||(t=document.createElement("capsolver-widgets"),document.head.appendChild(t)),window.registerCaptchaWidget=e=>{let n=et(e);if(window.isCaptchaWidgetRegistered(n))return;let o=document.createElement("capsolver-widget");for(let r in n)o.dataset[r]=n[r];t.appendChild(o)},window.isCaptchaWidgetRegistered=e=>{let{captchaType:n,widgetId:o}=e,r=t.children;for(let a=0;a<r.length;a++){let s=r[a],i=s.dataset.captchaType;if(s.dataset.widgetId===o)return!0}return!1},window.resetCaptchaWidget=e=>{let{captchaType:n,widgetId:o}=e,r=t.children;for(let a=0;a<r.length;a++){let i=r[a].dataset;if(i.captchaType===n&&i.widgetId===o){i.reset=String(!0);break}}},window.getCaptchaWidget=e=>{let{captchaType:n}=e,o=`capsolver-widget[data-captcha-type="${n}"]`;return document.querySelector(o)},window.getCaptchaWidgetDataset=(e,n)=>{var o;return(o=window.getCaptchaWidget({captchaType:e,widgetId:n}))==null?void 0:o.dataset},window.setCaptchaWidgetDataset=(e,n,o)=>{let r;e?r=`capsolver-widget[data-widget-id="${e}"]`:r="capsolver-widget";let a=document.querySelector(r);a&&(a.dataset[n]=o)}}function et(t){let e={};for(let n in t)t[n]===null||t[n]===void 0||(e[n]=`${t[n]}`);return e}function K(){let t=document.createElement("div");t.id="capsolver-solver-tip-button",t.classList.add("capsolver-solver"),t.dataset.state="solving";let e=document.createElement("div");e.classList.add("capsolver-solver-image");let n=document.createElement("img");n.src=chrome.runtime.getURL("assets/images/logo_solved.png"),n.alt="",e.appendChild(n);let o=document.createElement("div");return o.classList.add("capsolver-solver-info"),o.innerText=chrome.i18n.getMessage("solving"),t.appendChild(e),t.appendChild(o),t}function u(t,e){let n=document.querySelector("#capsolver-solver-tip-button"),o=n==null?void 0:n.querySelector(".capsolver-solver-info");o&&(o.innerHTML=t),e&&n&&(n.dataset.state=e)}var tt="hCaptcha";var P=class{constructor(){g(this,"captchaType",tt)}setSolveButton(e,n){let o=document.querySelector(`#${e.containerId}`),r=n;r.style.width=o.querySelector("iframe").offsetWidth+"px",o.append(r)}getParams(e,n){return{url:location.href,sitekey:e.sitekey}}onSolved(e,n){u(chrome.i18n.getMessage("solved"),"solved");let o=document.getElementById(e.containerId);if(!o)return;o.querySelectorAll("iframe[data-hcaptcha-widget-id]").forEach(a=>{let s=a.attributes["data-hcaptcha-widget-id"].value,i=JSON.stringify({source:"hcaptcha",label:"challenge-closed",id:s,contents:{event:"challenge-passed",response:n,expiration:120}});window.dispatchEvent(new MessageEvent("message",{data:i}))})}getForm(e){return document.querySelector(`#${e.containerId}`).querySelector("form")}getCallback(e){return e.callback}};var nt="reCaptcha";var W=class{constructor(e){g(this,"captchaType",nt);this.captchaType=e}setSolveButton(e,n){let o=n,r=this.getBindedElements(e);r.textarea&&r.textarea.parentElement?(r.textarea.parentElement.style.height="auto",r.textarea.parentElement.insertBefore(o,r.textarea.nextSibling)):r.button.parentElement.insertBefore(o,r.button.nextSibling)}getParams(e,n){return{sitekey:e.sitekey,url:location.href,version:(e==null?void 0:e.version)||"v2",score:(n==null?void 0:n.recaptchaV3MinScore)||.7,action:(e==null?void 0:e.action)||"",invisible:e==null?void 0:e.invisible,enterprise:!!e.enterprise,s:(e==null?void 0:e.s)||""}}onSolved(e,n){var r;u(chrome.i18n.getMessage("solved"),"solved");let o=this.getBindedElements(e).textarea;o||(o=(r=this.getForm(e))==null?void 0:r.querySelector("textarea[name=g-recaptcha-response]")),o.innerHTML=n,o.value=n}getForm(e){var o;let n=this.getBindedElements(e);return n.textarea?n.textarea.closest("form"):(o=n.button)==null?void 0:o.closest("form")}getCallback(e){return e.callback}getBindedElements(e){let n={button:null,textarea:null};if(e.bindedButtonId){let o=document.querySelector(`#${e.bindedButtonId}`);o&&(n.button=o)}else{let o=document.querySelector(`#${e.containerId} textarea[name=g-recaptcha-response]`);o&&(n.textarea=o)}return n}};var ot="funCaptcha";var me=304;function rt(t){return t-me>0?(t-me)/2:0}var _=class{constructor(){g(this,"captchaType",ot)}setSolveButton(e,n){let o=document.querySelector("#"+e.containerId),r=n;r.style.width=o.querySelector("iframe").offsetWidth+"px",r.style.position="absolute",r.style.left=rt(o.querySelector("iframe").offsetWidth)+"px",r.style.bottom="20px",o.append(r)}getParams(e){return{websiteURL:location.href,websitePublicKey:e.websitePublicKey}}onSolved(e,n){}getCallback(e){return"onSuccess"}};var at="cloudflare";function st(){let e=location.href.split("/");return e.slice(e.indexOf("turnstile")).sort((o,r)=>r.length-o.length)[0]}var A=class{constructor(){g(this,"captchaType",at)}getParams(e,n){return{websiteURL:location.href,websiteKey:st(),type:"turnstile"}}setSolveButton(e,n){let o=document.querySelector(`#${e.containerId}`),r=n;r.style.width=o.querySelector("iframe").offsetWidth+"px",o.append(r)}onSolved(e,n){u(chrome.i18n.getMessage("solved"),"solved");let o=this.getResponseInput(e);o.value=n}getResponseInput(e){let{containerId:n}=e,o=document.querySelector(`#${n}`);return o==null?void 0:o.querySelector('input[name="cf-turnstile-response"]')}getForm(e){return null}getCallback(e){return e==null?void 0:e.callback}};var it="awsCaptcha";var F=class{constructor(){g(this,"captchaType",it)}setSolveButton(e,n){let o=document.querySelector(`#${e.containerId}`),r=n;r.style.width=(o==null?void 0:o.offsetWidth)+"px",o==null||o.append(r)}};var $=class{constructor(){g(this,"list",[])}register(e){this.list[e.captchaType]=e}get(e){return this.list[e]}},h=new $;function Ce(t){h.register(new P),h.register(new W("reCaptcha")),h.register(new W("reCaptcha3")),h.register(new _),h.register(new A),h.register(new F)}var ve=0;function we(t){if(t!=null&&t.response){let{action:e}=t.response;switch(e){case"solver":ct(t.response);break;case"solved":u(chrome.i18n.getMessage("solved"),"solved"),xe();break}}}function ct(t){var e;try{if((e=t.request)!=null&&e.messageId)return pt(t);let n={captchaType:t.request.captchaType,widgetId:t.request.widgetId};t.error?(p.getAll().then(o=>{t.error==="Error: Capsover: No API Kye set up yet!"&&!(o!=null&&o.apiKey)&&u("Please input your API key!","error"),o[`${n.captchaType}RepeatTimes`]>=ve?window.setCaptchaWidgetDataset(n.widgetId,"status","ready"):(window.setCaptchaWidgetDataset(n.widgetId,"status","error"),u(t.error,"error"))}),ve++):(window.setCaptchaWidgetDataset(n.widgetId,"status","success"),lt(t),xe())}catch(n){console.error("handle error\uFF1A",n)}}function lt(t){let e=window.getCaptchaWidgetDataset(t.request.captchaType,t.request.widgetId),n=h.get(t.request.captchaType);n.onSolved(e,t.response.code);let o=n.getCallback(e);if(o){let r=document.createElement("textarea");r.id="c-b-t",r.setAttribute("data-function",o),r.value=t.response.code,document.body.appendChild(r)}}function xe(){p.getAll().then(t=>{window.postMessage({type:"capsolverCallback",callback:t.solvedCallback},"*")})}function pt(t){let e=document.querySelector("body > solver-ext-messages > solver-ext-message[data-message-id="+t.request.messageId+"]");!e||(t.error?ye(e[0],{error:t.error}):ye(e[0],{response:t.response.code}))}function ye(t,e){t.dataset.response=encodeURIComponent(JSON.stringify(e))}function Te(t){if(document.querySelector("#capsolver-solver-tip-button"))return;let e=h.get(t.captchaType),n=K();p.getAll().then(o=>{!o.showSolveButton||e.setSolveButton(t,n)})}function dt(t,e){if(document.querySelector("#capsolver-solver-tip-button"))return;let n=h.get(t.captchaType),o=K();o.onclick=()=>{e==="token"?window.onTaskByToken(t):chrome.runtime.sendMessage({action:"execute"}),u(chrome.i18n.getMessage("solving"),"solving")},p.getAll().then(r=>{!r.showSolveButton||(n.setSolveButton(t,o),u(chrome.i18n.getMessage("solveWithCapsolver"),"ready"))})}function Se(){window.getSolverButton=t=>{let{captchaType:e,widgetId:n}=t,o=`.capsolver-solver[data-captcha-type="${e}"][data-widget-id="${n}"]`;return document.querySelector(o)},window.setSolverButtonState=(t,e,n)=>{let o=window.getSolverButton(t);if(!o)return;o.setAttribute("data-state",e);let r=o.querySelector(".capsolver-solver-info");if(r&&(r.innerHTML=n),e==="error"){let a=parseInt(o.dataset.countErrors||"0")+1;o.dataset.countErrors=String(a)}},window.onTaskByToken=t=>{let{captchaType:e,widgetId:n}=t,o=window.getCaptchaWidgetDataset(e,n);p.getAll().then(r=>{let s=h.get(e).getParams(o,r);try{s.websiteURL=window.top.location.href}catch{}Te(t);let i={action:"solver",captchaType:e,widgetId:n,params:s};window.setCaptchaWidgetDataset(n,"status","processing"),chrome.runtime.sendMessage(i).then(we)})}}var ut=(t,e)=>{let n=new Map([["hCaptcha","enabledForHCaptcha"],["reCaptcha","enabledForRecaptcha"],["reCaptcha3","enabledForRecaptchaV3"],["cloudflare","enabledForCloudflare"],["awsCaptcha","enabledForAwsCaptcha"]]);return!!(t[n.get(e.captchaType)]&&e.containerId)};function Me(){return setInterval(async()=>{let t=await p.getAll(),e=document.querySelector("head").getElementsByTagName("capsolver-widget");for(let n=0;n<e.length;n++){let r=e.item(n).dataset;if(!(t!=null&&t.apiKey)){Te(r),u("Please input your API key!","error");return}!ut(t,r)||r.status!=="ready"||(t.manualSolving?dt(r,t[`${r.captchaType}Mode`]):t[`${r.captchaType}Mode`]==="token"&&window.onTaskByToken(r))}},2e3)}var We=document.createElement("script");We.src=chrome.runtime.getURL("assets/inject/solvedCallback.js");var ft=document.head||document.documentElement;ft.appendChild(We);var De=[],gt=["arkoselabs.com/fc","funcaptcha.com/fc","hcaptcha.com/captcha","google.com/recaptcha","recaptcha.net/recaptcha","recaptcha.net/recaptcha"],ht=chrome.runtime.connect({name:"content"});ht.onDisconnect.addListener(t=>{for(let e of De)e&&clearInterval(e)});var Le,mt=(Le=globalThis.browser)!=null?Le:globalThis.chrome;mt.storage.local.set({platform:window.navigator.userAgent.includes("Chrome")?"chrome":"firefox"});chrome.runtime.onMessage.addListener((t,e,n)=>{var a,s;let o=(a=t==null?void 0:t.response)==null?void 0:a.action,r=(s=t==null?void 0:t.response)==null?void 0:s.callback;switch(o){case"solved":u(chrome.i18n.getMessage("solved"),"solved"),window.postMessage({type:"capsolverCallback",callback:r},"*");break;default:break}return!1});function Ct(){try{let t=window.top.location.href;chrome.runtime.sendMessage({action:"getWebsiteUrl",websiteUrl:t})}catch{}}function vt(){try{let t=window.top.location.href,e=document.title;chrome.runtime.sendMessage({action:"setWebsiteMetadata",metadata:{pageURL:t,title:e}})}catch{}}p.getAll().then(function(t){he(),Ce(t),Se();let e=window.location.origin,n=window.location.pathname,o=e+n,r=gt.some(s=>o.indexOf(s)!==-1),a=t.blackUrlList.includes(o);p.set({isInBlackList:r?t.isInBlackList:a,isInit:!0}).then(()=>{(!t.enabledForBlacklistControl||!a)&&t.useCapsolver&&(De.push(Me()),Ct(),vt())})});window.addEventListener("message",async function(t){if(t.data.type!=="capsolverSolve")return;let e=await p.getAll(),n=document.querySelector("head").getElementsByTagName("capsolver-widget");for(let o=0;o<n.length;o++){let a=n.item(o).dataset;if(!(e!=null&&e.apiKey))return;e[`${a.captchaType}Mode`]==="token"?window.onTaskByToken(a):chrome.runtime.sendMessage({action:"execute"})}});var ke=t=>{ge(t)};var V=chrome.runtime.connect({name:"contentScript"}),Ie=!1;V.onDisconnect.addListener(()=>{Ie=!0});var Ee=new S({listen(t){V.onMessage.addListener(t)},send(t){Ie||(V.postMessage(t),window.postMessage({...t,from:"bex-content-script"},"*"))}});function yt(t){let e=document.createElement("script");e.src=t,e.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(e)}document instanceof HTMLDocument&&yt(chrome.runtime.getURL("dom.js"));pe(Ee,"bex-dom");ke(Ee);})();
