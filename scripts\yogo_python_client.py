#!/usr/bin/env python3
"""
Yopmail域名获取客户端
专门用于获取yopmail备用域名，不包含邮件获取功能
"""

import os
import requests
from typing import List, Dict, Any
import logging


class YogoPythonError(Exception):
    """自定义异常类"""
    pass


class CaptchaError(YogoPythonError):
    """验证码错误"""
    pass


class YogoPythonClient:
    """Yopmail域名获取客户端（简化版）"""

    def __init__(self, enable_debug: bool = False):
        """
        初始化域名获取客户端

        Args:
            enable_debug: 是否启用调试模式
        """
        self.enable_debug = enable_debug
        self.default_timeout = 15

        # 设置日志
        self.logger = logging.getLogger(__name__)
        if enable_debug:
            self.logger.setLevel(logging.DEBUG)
        else:
            self.logger.setLevel(logging.WARNING)

        # 初始化域名管理
        self.__init_domain_management()

    def _simple_get(self, url: str) -> requests.Response:
        """简单的HTTP GET请求"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        try:
            response = requests.get(url, headers=headers, timeout=self.default_timeout)
            response.raise_for_status()
            return response
        except Exception as e:
            raise YogoPythonError(f"HTTP请求失败: {e}")
    


    # ==================== 域名管理功能（兼容旧yop模块接口） ====================

    def __init_domain_management(self):
        """初始化域名管理相关属性"""
        self._alternative_domains = None
        self._available_domains = None
        self.blacklist_file = os.path.join('input', 'yopmail_domain_blacklist.txt')
        # 确保input目录存在
        os.makedirs('input', exist_ok=True)

    def get_yopmail_alternative_domains(self) -> List[str]:
        """
        获取yopmail的所有备用域名（缓存机制）

        Returns:
            备用域名列表
        """
        # 初始化域名管理属性（如果还没有）
        if not hasattr(self, '_alternative_domains'):
            self.__init_domain_management()

        # 如果已缓存，直接返回
        if self._alternative_domains is not None:
            return self._alternative_domains

        if self.enable_debug:
            self.logger.debug("开始获取yopmail备用域名")

        try:
            # 访问yopmail域名列表页面获取备用域名（不使用代理）
            url = "https://yopmail.com/zh/domain?d=list"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            if self.enable_debug:
                self.logger.debug("访问yopmail域名列表页面获取备用域名")
            response = requests.get(url, headers=headers, timeout=15)

            if response.status_code == 200:
                content = response.text

                # 从域名列表页面中提取备用域名
                domains = self._extract_domains_from_response(content)

                if domains:
                    self._alternative_domains = domains
                    if self.enable_debug:
                        self.logger.debug(f"从域名列表页面获取到 {len(domains)} 个备用域名")
                    return domains

        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"从页面获取域名失败: {e}")

        # 如果获取失败，返回空列表，不使用内置域名
        if self.enable_debug:
            self.logger.debug("无法获取yopmail备用域名")
        self._alternative_domains = []
        return []

    def _extract_domains_from_response(self, content: str) -> List[str]:
        """
        从yopmail域名列表页面响应中提取域名

        Args:
            content: 页面HTML内容

        Returns:
            提取到的域名列表
        """
        import re

        # 使用正则表达式提取所有@域名格式
        # 页面中的格式是：<option>@domain.com</option>
        pattern = r'@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'

        matches = re.findall(pattern, content, re.IGNORECASE)

        if matches:
            # 去重并排序
            unique_domains = list(set(match.lower() for match in matches))
            unique_domains.sort()

            # 验证域名格式
            valid_domains = []
            for domain in unique_domains:
                if self._is_valid_domain(domain):
                    valid_domains.append(domain)

            return valid_domains
        else:
            if self.enable_debug:
                self.logger.debug("未找到任何@域名格式")
            return []

    def _is_valid_domain(self, domain: str) -> bool:
        """
        验证域名格式是否有效

        Args:
            domain: 域名

        Returns:
            是否有效
        """
        # 基本格式检查
        if not domain or len(domain) < 4:
            return False

        # 必须包含点
        if '.' not in domain:
            return False

        # 不能以点开头或结尾
        if domain.startswith('.') or domain.endswith('.'):
            return False

        # 不能包含连续的点
        if '..' in domain:
            return False

        return True

    def _load_domain_blacklist(self) -> List[str]:
        """
        加载域名黑名单

        Returns:
            黑名单域名列表
        """
        if not hasattr(self, 'blacklist_file'):
            self.__init_domain_management()

        blacklist = []

        try:
            if os.path.exists(self.blacklist_file):
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        domain = line.strip()
                        if domain and not domain.startswith('#'):
                            blacklist.append(domain)
        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"加载黑名单文件失败: {e}")

        return blacklist

    def _save_domain_to_blacklist(self, domain: str):
        """
        将域名添加到黑名单

        Args:
            domain: 要添加的域名
        """
        if not hasattr(self, 'blacklist_file'):
            self.__init_domain_management()

        try:
            # 先检查是否已存在
            blacklist = self._load_domain_blacklist()
            if domain not in blacklist:
                # 简化的文件写入逻辑
                with open(self.blacklist_file, 'a', encoding='utf-8') as f:
                    f.write(f"{domain}\n")
                if self.enable_debug:
                    self.logger.debug(f"域名 {domain} 已添加到黑名单文件")
            else:
                if self.enable_debug:
                    self.logger.debug(f"域名 {domain} 已在黑名单中")
        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"添加域名到黑名单失败: {e}")

    def get_available_domains(self) -> List[str]:
        """
        获取可用的域名列表（排除黑名单）

        Returns:
            可用域名列表
        """
        # 初始化域名管理属性（如果还没有）
        if not hasattr(self, '_available_domains'):
            self.__init_domain_management()

        # 如果已缓存，直接返回
        if self._available_domains is not None:
            return self._available_domains

        # 获取所有备用域名
        all_domains = self.get_yopmail_alternative_domains()

        # 加载黑名单
        blacklist = self._load_domain_blacklist()

        # 过滤掉黑名单中的域名
        available_domains = [domain for domain in all_domains if domain not in blacklist]

        self._available_domains = available_domains
        if self.enable_debug:
            self.logger.debug(f"可用域名 {len(available_domains)} 个，黑名单 {len(blacklist)} 个")

        return available_domains

    def get_random_yopmail_domain(self) -> str:
        """
        随机获取一个可用的yopmail域名

        Returns:
            随机选择的域名
        """
        import random

        available_domains = self.get_available_domains()

        if not available_domains:
            if self.enable_debug:
                self.logger.debug("没有可用的yopmail域名，无法继续注册")
            raise Exception("没有可用的yopmail域名，请检查网络连接或yopmail网站状态")

        # 随机选择一个域名
        selected_domain = random.choice(available_domains)
        if self.enable_debug:
            self.logger.debug(f"随机选择域名: {selected_domain}")

        return selected_domain

    def mark_domain_as_blocked(self, domain: str, reason: str = "Resend blocked"):
        """
        标记域名为被阻止（添加到黑名单并从缓存中移除）

        Args:
            domain: 被阻止的域名
            reason: 阻止原因
        """
        if not hasattr(self, '_available_domains'):
            self.__init_domain_management()

        if self.enable_debug:
            self.logger.debug(f"域名 {domain} 被标记为阻止: {reason}")

        # 1. 添加到黑名单文件
        self._save_domain_to_blacklist(domain)

        # 2. 从可用域名缓存中移除
        if self._available_domains and domain in self._available_domains:
            self._available_domains.remove(domain)
            if self.enable_debug:
                self.logger.debug(f"已从可用域名缓存中移除: {domain}")

        # 3. 记录当前可用域名数量
        available_count = len(self._available_domains) if self._available_domains else 0
        if self.enable_debug:
            self.logger.debug(f"当前剩余可用域名: {available_count} 个")

    def get_domain_stats(self) -> Dict[str, Any]:
        """
        获取域名统计信息

        Returns:
            域名统计信息
        """
        all_domains = self.get_yopmail_alternative_domains()
        available_domains = self.get_available_domains()
        blacklist = self._load_domain_blacklist()

        return {
            "total_domains": len(all_domains),
            "available_domains": len(available_domains),
            "blacklisted_domains": len(blacklist),
            "all_domains_list": all_domains,
            "available_domains_list": available_domains,
            "blacklisted_domains_list": blacklist
        }


# 注意：邮件获取功能已迁移到 scripts/playwright_yop.py
# 此文件现在只保留域名获取相关功能






