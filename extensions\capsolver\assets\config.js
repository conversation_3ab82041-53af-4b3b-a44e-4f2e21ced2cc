export const defaultConfig = {
  // API key
  apiKey: "CAP-CE2A3DE7A3F83A04839B991AE859957C663379F212F991F527EBDD88C9BBE3C2",

  // Your Developer appId, Apply in dashboard's developer section
  appId: '',

  // Is the extension enabled by default or not
  useCapsolver: true,

  // Solve captcha manually
  manualSolving: true,

  // Captcha solved callback function name
  solvedCallback: "captchaSolvedCallback",

  // Use proxy or not
  // If useProxy is true, then proxyType, hostOrIp, port, proxyLogin, proxyPassword are required
  useProxy: false,
  proxyType: 'http',
  hostOrIp: '',
  port: '',
  proxyLogin: '',
  proxyPassword: '',

  enabledForBlacklistControl: false, // Use blacklist control
  blackUrlList: [], // Blacklist URL list

  // Is captcha enabled by default or not
  enabledForRecaptcha: true,
  enabledForRecaptchaV3: true,
  enabledForImageToText: true,
  enabledForAwsCaptcha: true,
  enabledForCloudflare: true,

  // Task type: click or token
  reCaptchaMode: "click",
  hCaptchaMode: 'click',

  // Delay before solving captcha (增加延迟减少误触发)
  reCaptchaDelayTime: 2000,
  hCaptchaDelayTime: 1000,
  textCaptchaDelayTime: 1000,
  awsDelayTime: 1000,

  // Number of repeated solutions after an error
  reCaptchaRepeatTimes: 10,
  reCaptcha3RepeatTimes: 10,
  hCaptchaRepeatTimes: 10,
  funCaptchaRepeatTimes: 10,
  textCaptchaRepeatTimes: 10,
  awsRepeatTimes: 10,

  // ReCaptcha V3 task type: ReCaptchaV3TaskProxyLess or ReCaptchaV3M1TaskProxyLess
  reCaptcha3TaskType: 'ReCaptchaV3TaskProxyLess',

  textCaptchaSourceAttribute: 'capsolver-image-to-text-source', // ImageToText source img's attribute name
  textCaptchaResultAttribute: 'capsolver-image-to-text-result', // ImageToText result element's attribute name

  textCaptchaModule: 'common', // ImageToText module

  showSolveButton: true, // Show solve button
};
