"use strict";(()=>{var Be=Object.create;var oe=Object.defineProperty;var Fe=Object.getOwnPropertyDescriptor;var je=Object.getOwnPropertyNames;var Ne=Object.getPrototypeOf,He=Object.prototype.hasOwnProperty;var Ue=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var qe=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of je(t))!He.call(e,r)&&r!==n&&oe(e,r,{get:()=>t[r],enumerable:!(o=Fe(t,r))||o.enumerable});return e};var We=(e,t,n)=>(n=e!=null?Be(Ne(e)):{},qe(t||!e||!e.__esModule?oe(n,"default",{value:e,enumerable:!0}):n,e));var me=Ue((wt,U)=>{"use strict";var C=typeof Reflect=="object"?Reflect:null,re=C&&typeof C.apply=="function"?C.apply:function(t,n,o){return Function.prototype.apply.call(t,n,o)},P;C&&typeof C.ownKeys=="function"?P=C.ownKeys:Object.getOwnPropertySymbols?P=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:P=function(t){return Object.getOwnPropertyNames(t)};function Ke(e){console&&console.warn&&console.warn(e)}var ae=Number.isNaN||function(t){return t!==t};function c(){c.init.call(this)}U.exports=c;U.exports.once=Ge;c.EventEmitter=c;c.prototype._events=void 0;c.prototype._eventsCount=0;c.prototype._maxListeners=void 0;var ie=10;function O(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return ie},set:function(e){if(typeof e!="number"||e<0||ae(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");ie=e}});c.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};c.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||ae(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function se(e){return e._maxListeners===void 0?c.defaultMaxListeners:e._maxListeners}c.prototype.getMaxListeners=function(){return se(this)};c.prototype.emit=function(t){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);var r=t==="error",i=this._events;if(i!==void 0)r=r&&i.error===void 0;else if(!r)return!1;if(r){var a;if(n.length>0&&(a=n[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var u=i[t];if(u===void 0)return!1;if(typeof u=="function")re(u,this,n);else for(var l=u.length,f=pe(u,l),o=0;o<l;++o)re(f[o],this,n);return!0};function ce(e,t,n,o){var r,i,a;if(O(n),i=e._events,i===void 0?(i=e._events=Object.create(null),e._eventsCount=0):(i.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),a=i[t]),a===void 0)a=i[t]=n,++e._eventsCount;else if(typeof a=="function"?a=i[t]=o?[n,a]:[a,n]:o?a.unshift(n):a.push(n),r=se(e),r>0&&a.length>r&&!a.warned){a.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=a.length,Ke(s)}return e}c.prototype.addListener=function(t,n){return ce(this,t,n,!1)};c.prototype.on=c.prototype.addListener;c.prototype.prependListener=function(t,n){return ce(this,t,n,!0)};function Qe(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function le(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=Qe.bind(o);return r.listener=n,o.wrapFn=r,r}c.prototype.once=function(t,n){return O(n),this.on(t,le(this,t,n)),this};c.prototype.prependOnceListener=function(t,n){return O(n),this.prependListener(t,le(this,t,n)),this};c.prototype.removeListener=function(t,n){var o,r,i,a,s;if(O(n),r=this._events,r===void 0)return this;if(o=r[t],o===void 0)return this;if(o===n||o.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,o.listener||n));else if(typeof o!="function"){for(i=-1,a=o.length-1;a>=0;a--)if(o[a]===n||o[a].listener===n){s=o[a].listener,i=a;break}if(i<0)return this;i===0?o.shift():Ve(o,i),o.length===1&&(r[t]=o[0]),r.removeListener!==void 0&&this.emit("removeListener",t,s||n)}return this};c.prototype.off=c.prototype.removeListener;c.prototype.removeAllListeners=function(t){var n,o,r;if(o=this._events,o===void 0)return this;if(o.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):o[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete o[t]),this;if(arguments.length===0){var i=Object.keys(o),a;for(r=0;r<i.length;++r)a=i[r],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=o[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(r=n.length-1;r>=0;r--)this.removeListener(t,n[r]);return this};function ue(e,t,n){var o=e._events;if(o===void 0)return[];var r=o[t];return r===void 0?[]:typeof r=="function"?n?[r.listener||r]:[r]:n?ze(r):pe(r,r.length)}c.prototype.listeners=function(t){return ue(this,t,!0)};c.prototype.rawListeners=function(t){return ue(this,t,!1)};c.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):fe.call(e,t)};c.prototype.listenerCount=fe;function fe(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}c.prototype.eventNames=function(){return this._eventsCount>0?P(this._events):[]};function pe(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}function Ve(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function ze(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function Ge(e,t){return new Promise(function(n,o){function r(a){e.removeListener(t,i),o(a)}function i(){typeof e.removeListener=="function"&&e.removeListener("error",r),n([].slice.call(arguments))}de(e,t,i,{once:!0}),t!=="error"&&Xe(e,r,{once:!0})})}function Xe(e,t,n){typeof e.on=="function"&&de(e,"error",t,n)}function de(e,t,n,o){if(typeof e.on=="function")o.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function r(i){o.once&&e.removeEventListener(t,r),n(i)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var ye=We(me());var q,I=0,d=new Array(256);for(let e=0;e<256;e++)d[e]=(e+256).toString(16).substring(1);var Ye=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let o=t;o>0;o--)n.push(Math.floor(Math.random()*256));return n}})(),he=4096;function ge(){(q===void 0||I+16>he)&&(I=0,q=Ye(he));let e=Array.prototype.slice.call(q,I,I+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,d[e[0]]+d[e[1]]+d[e[2]]+d[e[3]]+"-"+d[e[4]]+d[e[5]]+"-"+d[e[6]]+d[e[7]]+"-"+d[e[8]]+d[e[9]]+"-"+d[e[10]]+d[e[11]]+d[e[12]]+d[e[13]]+d[e[14]]+d[e[15]]}var Je={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>W(n)+W(e[n])+t,0):0},W=e=>Je[typeof e](e),M=class extends ye.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(o=>this._emit(o)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,o=>{n({...o,respond:r=>this.send(o.eventResponseKey,r)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],o=`${n.event}.${ge()}`,r=o+".result";return new Promise((i,a)=>{let s=[],u=l=>{if(l!==void 0&&l._chunkSplit){let f=l._chunkSplit;s=[...s,...l.data],f.lastChunk&&(this.off(r,u),i(s))}else this.off(r,u),i(l)};this.on(r,u);try{let l=t.map(f=>({...f,payload:{data:f.payload,eventResponseKey:r}}));this.wall.send(l)}catch(l){let f="Message length exceeded maximum allowed length.";if(l.message===f&&Array.isArray(n.payload)){let m=W(n);if(m>this._maxMessageSize){let g=Math.ceil(m/this._maxMessageSize),h=Math.ceil(n.payload.length/g),w=n.payload;for(let k=0;k<g;k++){let H=Math.min(w.length,h);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:g,lastChunk:k===g-1},data:w.splice(0,H)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var be=(e,t)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===t){let o=n.data[0],r=e.getEvents();for(let i in r)i===o.event&&r[i](o.payload)}},!1)};var $e=chrome.runtime.getURL("assets/config.js"),ve,E=(ve=globalThis.browser)!=null?ve:globalThis.chrome;async function Ze(){var ee,te;let e=await E.storage.local.get("defaultConfig");if((ee=e.defaultConfig)!=null&&ee.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],o=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],r=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,s=(await(await fetch($e)).text()).replace(r,""),u=s.slice(s.indexOf("{")+1,s.lastIndexOf("}")),l=JSON.stringify(u).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),f=l.indexOf("blackUrlList"),m=l.slice(f),g=m.indexOf("],"),h=m.slice(0,g+1);l.replace(h,"").split(",").forEach(De=>{let[_,ne]=De.split(":");if(_&&ne){let L=ne.replaceAll("'","").replaceAll('"',"");for(let b=0;b<n.length;b++)_.endsWith(n[b])&&(L=Number(L));for(let b=0;b<o.length;b++)_.startsWith(o[b])&&(L=L==="true");t[_]=L}}),h=h.replaceAll("'","").replaceAll('"',"");let H=h.indexOf(":["),Ie=h.slice(H+2,h.length-1);t.blackUrlList=Ie.split(",");let A=await E.storage.local.get("config");return(te=A==null?void 0:A.config)!=null&&te.apiKey&&(t.apiKey=A.config.apiKey),E.storage.local.set({defaultConfig:t}),t}var T={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},Ce={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function xe(){let e=await Ze(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!Ce[n].includes(e[n]))){{if(n.endsWith("Mode")&&!Ce.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;T.port=e.port}}Reflect.has(T,n)&&typeof T[n]==typeof e[n]&&(T[n]=e[n])}return T}var et=xe(),R={default:et,async get(e){return(await this.getAll())[e]},async getAll(){let e=await xe(),t=await E.storage.local.get("config");return R.joinConfig(e,t.config)},async set(e){let t=await R.getAll(),n=R.joinConfig(t,e);return E.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let o in e)n[o]=e[o];if(t)for(let o in t)n[o]=t[o];return n}};function we(e){return new Promise((t,n)=>{let o=new Image;o.src=e,o.setAttribute("crossOrigin","anonymous"),o.onload=()=>{let r=document.createElement("canvas");r.width=o.width,r.height=o.height,r.getContext("2d").drawImage(o,0,0,o.width,o.height);let a=r.toDataURL();t(a)},o.onerror=r=>{n(r)}})}function y(e){return new Promise(t=>setTimeout(t,e))}function p(e,t){let n=t-e+1;return Math.floor(Math.random()*n+e)}function K(e){let t=e==null?void 0:e.getBoundingClientRect();return t?{x:t.top+window.scrollY-document.documentElement.clientTop+p(-5,5),y:t.left+window.scrollX-document.documentElement.clientLeft+p(-5,5)}:{x:0,y:0}}function tt(e,t,n,o,r){let[i,a]=t,[s,u]=r,[l,f]=n,[m,g]=o,h=i*(1-e)*(1-e)*(1-e)+3*l*e*(1-e)*(1-e)+3*m*e*e*(1-e)+s*e*e*e,w=a*(1-e)*(1-e)*(1-e)+3*f*e*(1-e)*(1-e)+3*g*e*e*(1-e)+u*e*e*e;return[h,w]}function nt(e,t,n=30){let o=[],r=0,i=1;for(let m=0;m<n;++m)o.push(r),m<n*1/10?i+=p(60,100):m>=n*9/10&&(i-=p(60,100),i=Math.max(20,i)),r+=i;let a=[],s=[e.x,e.y],u=[(e.x+t.x)/2+p(30,100)*1,(e.y+t.y)/2+p(30,100)*1],l=[(e.x+t.x)/2+p(30,100)*1,(e.y+t.y)/2+p(30,100)*1],f=[t.x,t.y];for(let m of o){let[g,h]=tt(m/r,s,u,l,f);a.push({x:g,y:h})}return a}function ot(e,t){let n=nt(e,t,p(15,30));for(let o=0;o<n.length;o++)document.body.dispatchEvent(new MouseEvent("mousemove",{bubbles:!0,clientX:n[o].x,clientY:n[o].y}))}function rt({x:e,y:t}){document.body.dispatchEvent(new MouseEvent("mousedown",{bubbles:!0,clientX:e,clientY:t}))}function it({x:e,y:t}){document.body.dispatchEvent(new MouseEvent("mouseup",{bubbles:!0,clientX:e,clientY:t}))}async function at(e,t){ot(e,t),await y(p(30,80)),rt(t),await y(p(30,80)),it(t)}async function st(e){for(let t=0;t<e.length-1;t++)await at(e[t],e[t+1])}function ct(e,t,n){let r=[n?K(n):{x:t?p(420,530):p(10,100),y:t?p(200,300):p(5,200)}];for(let i=0;i<e.length;i++){let a=K(e[i]);r.push(a)}return r}async function D(e,t=null){let n=ct(e,!1,t);await st(n)}var Le=["Error: ERROR_UNSUPPORTED_QUESTION"];var Q="",B=[],V=-1,F=0,S=0,v=[],x=null,z=!1;var Ee,zt=(Ee=globalThis.browser)!=null?Ee:globalThis.chrome;function lt(){return document.querySelector(".rc-imageselect-error-select-more").style.display!=="none"}function ut(){return document.querySelector(".rc-imageselect-error-dynamic-more").style.display!=="none"}async function Me(){let e=Array.from(document.querySelectorAll(".rc-imageselect-tile")),t=p(0,e.length);e[t].click(),await D([e[t]],x),x=e[t],Re()}function ft(){var t;let e=((t=document.querySelector("#recaptcha-anchor"))==null?void 0:t.getAttribute("aria-checked"))==="true";return e&&(S=0,!z&&chrome.runtime.sendMessage({action:"solved"}),z=!0),e}function pt(){let e=document.querySelector(".rc-imageselect-incorrect-response");return(e==null?void 0:e.style.display)===""}function dt(){var e;(e=document.querySelector("#recaptcha-anchor"))==null||e.click()}function mt(){return v.length>0?v[0]:!1}function ht(){let e=document.querySelector("#recaptcha-reload-button");e==null||e.click()}async function Te(e){let t=await we(e.image),n={image:t.slice(t.indexOf(";base64,")+8),question:e.question};e.index&&(n.index=e.index),chrome.runtime.sendMessage({action:"solver",captchaType:"reCaptcha",params:n}).then(o=>{var r,i;if(!(o!=null&&o.response)||((r=o==null?void 0:o.response)==null?void 0:r.error)){Le.includes((i=o==null?void 0:o.response)==null?void 0:i.error)&&ht(),Q="",S++;return}gt(o.response)})}async function Re(){var e;(V===3&&F===0&&await j()||V===4)&&((e=document.querySelector("#recaptcha-verify-button"))==null||e.click(),x=null,B=[],v.shift(),await D([document.querySelector("#recaptcha-verify-button")],x))}function G(){return document.querySelector("#recaptcha-anchor")!==null}function X(){if(ft()){S=0;return}dt(),z=!1}function Y(){return document.querySelector("#rc-imageselect")!==null}function j(){return new Promise(e=>{let t=document.querySelectorAll(".rc-imageselect-tile"),n=document.querySelectorAll(".rc-imageselect-dynamic-selected");t.length>0&&n.length===0?e(!0):e(!1)})}function J(e){return new Promise(t=>{e<=S&&t(!1);let n=mt();n||t(!1),lt()&&(Me(),t(!1)),ut()&&(Me(),t(!1));let o=Array.from(document.querySelectorAll(".rc-imageselect-tile img")),r=o.length,i=Array(r).fill(null),a="",s=!1,u="";r!==9&&r!==16&&t(!1),V=r===9?3:4;for(let l=0;l<r;l++){let f=o[l];f.naturalWidth>=300?a=f.getAttribute("src"):f.naturalWidth===100&&(i[l]=f.getAttribute("src"),s=!0)}s&&(a=null),u=JSON.stringify([a,i]),Q===u&&t(!1),Q=u,F=0,t({question:n,url:a,urls:i})})}async function $(e){pt()&&S++;let{question:t,url:n,urls:o}=e,r="";if(n)r=n,await Te({question:t,image:r});else for(let i=0;i<o.length;i++)!o[i]||B.includes(o[i])||(r=o[i],B.push(r),await Te({question:t,image:r,index:i}))}async function gt(e){var r;let t=(r=e==null?void 0:e.response)==null?void 0:r.solution;if(!t)return;let n=Array.from(document.querySelectorAll(".rc-imageselect-tile")),o=[];if(t.type==="single")t.hasObject?(F++,n[e.index].click(),B.splice(e.index,1),o.push(n[e.index])):F=0;else{let i=t.objects,a=i.length;for(let s=0;s<a;s++)await y(100),n[i[s]].click(),o.push(n[i[s]])}await D(o,x),x=o[o.length-1],await y(500),Re()}function Se(e){let t=e.length,n=[];for(let o=0;o<t;o++)if(Array.isArray(e[o])&&e[o][0]!=="pmeta")n=Se(e[o]);else if(Array.isArray(e[o])&&e[o][0]==="pmeta"){n=e[o];break}return n}function ke(e){try{let t=JSON.parse(e.split(`
`)[1]),n=Se(t),o=n.length;if(o===0){v=[];return}let r=[];for(let i=0;i<o;i++)if(Array.isArray(n[i])){r=n[i];break}else continue;Array.isArray(r[0])?r[0].forEach(i=>{v.push(i[0])}):v.push(r[0])}catch{console.log("Get question failed")}}var Ae=document.createElement("script");Ae.src=chrome.runtime.getURL("assets/inject/inject-recaptcha.js");var yt=document.head||document.documentElement;yt.appendChild(Ae);window.addEventListener("message",function(e){var t,n;(((t=e==null?void 0:e.data)==null?void 0:t.type)==="xhr"||((n=e==null?void 0:e.data)==null?void 0:n.type)==="fetch")&&ke(e.data.data)});async function bt(e){!e.useCapsolver||!e.enabledForRecaptcha||!e.apiKey||e.enabledForBlacklistControl&&e.isInBlackList||e.reCaptchaMode!=="click"||(await y(e.reCaptchaDelayTime),setInterval(async()=>{if(G()&&X(),Y()){if(!await j())return;let n=await J(e.reCaptchaRepeatTimes);if(!n)return;await $(n)}},1e3))}async function Ct(e){setInterval(async()=>{if(G()&&X(),Y()){if(!await j())return;let n=await J(e.reCaptchaRepeatTimes);if(!n)return;await $(n)}},1e3)}var N=null;N&&window.clearInterval(N);N=window.setInterval(async()=>{let e=await R.getAll();!e.isInit||(e.manualSolving?chrome.runtime.onMessage.addListener(t=>{t.command==="execute"&&Ct(e)}):bt(e),window.clearInterval(N))},100);var _e=e=>{};var Z=chrome.runtime.connect({name:"contentScript"}),Pe=!1;Z.onDisconnect.addListener(()=>{Pe=!0});var Oe=new M({listen(e){Z.onMessage.addListener(e)},send(e){Pe||(Z.postMessage(e),window.postMessage({...e,from:"bex-content-script"},"*"))}});function vt(e){let t=document.createElement("script");t.src=e,t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)}document instanceof HTMLDocument&&vt(chrome.runtime.getURL("dom.js"));be(Oe,"bex-dom");_e(Oe);})();
