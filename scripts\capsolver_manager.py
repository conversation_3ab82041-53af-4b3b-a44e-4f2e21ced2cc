"""
CapSolver扩展管理器
负责检测、下载、安装、解压和配置CapSolver扩展
"""

import asyncio
import os
import json
import zipfile
import shutil
import requests
import logging
import yaml
from pathlib import Path
from typing import Optional, Tuple

class CapSolverManager:
    """CapSolver扩展管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 获取脚本根目录（向上一级到项目根目录）
        self.root_dir = Path(__file__).parent.parent

        # 使用绝对路径
        self.extension_dir = self.root_dir / "extensions"
        self.capsolver_dir = self.extension_dir / "capsolver"
        self.config_file = self.capsolver_dir / "assets" / "config.js"
        self.api_key = self._load_api_key()
        
        # GitHub发布页面信息
        self.github_repo = "capsolver/capsolver-browser-extension"
        # 使用具体版本号的下载链接，避免latest链接问题
        self.download_url = "https://github.com/capsolver/capsolver-browser-extension/releases/download/v1.16.0/capsolver-browser-extension.zip"

    def _load_api_key(self) -> str:
        """从配置文件加载API密钥"""
        try:
            config_path = self.root_dir / "input" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            api_key = config.get('capsolver', {}).get('api_key', '')
            if not api_key:
                self.logger.warning("⚠️ 配置文件中未找到CapSolver API密钥，请检查config.yaml")
                return ""

            self.logger.info("✅ 成功从配置文件加载CapSolver API密钥")
            return api_key

        except Exception as e:
            self.logger.error(f"❌ 加载API密钥失败: {e}")
            # 返回默认密钥作为备用
            return "CAP-CE2A3DE7A3F83A04839B991AE859957C663379F212F991F527EBDD88C9BBE3C2"
        
    def check_extension_installed(self) -> bool:
        """检查CapSolver扩展是否已安装并配置"""
        try:
            # 检查扩展目录是否存在
            if not self.capsolver_dir.exists():
                self.logger.info("❌ CapSolver扩展目录不存在")
                return False
            
            # 检查manifest.json是否存在
            manifest_file = self.capsolver_dir / "manifest.json"
            if not manifest_file.exists():
                self.logger.info("❌ CapSolver扩展manifest.json不存在")
                return False
            
            # 检查配置文件是否存在且配置正确
            if not self.config_file.exists():
                self.logger.info("❌ CapSolver配置文件不存在")
                return False
            
            # 验证配置文件内容
            if not self._verify_config():
                self.logger.info("❌ CapSolver配置文件配置不正确")
                return False
            
            self.logger.info("✅ CapSolver扩展已正确安装和配置")
            return True
            
        except Exception as e:
            self.logger.error(f"检查CapSolver扩展时出错: {e}")
            return False
    
    def _verify_config(self) -> bool:
        """验证配置文件是否正确配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含API key
            if self.api_key in content:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"验证配置文件时出错: {e}")
            return False
    
    def install_extension(self) -> bool:
        """安装CapSolver扩展（下载、解压、配置）"""
        try:
            self.logger.info("🚀 开始安装CapSolver扩展...")
            
            # 1. 创建扩展目录
            self.extension_dir.mkdir(exist_ok=True)
            
            # 2. 下载扩展
            zip_path = self.extension_dir / "capsolver_extension.zip"
            if not self._download_extension(zip_path):
                return False
            
            # 3. 解压扩展
            if not self._extract_extension(zip_path):
                return False
            
            # 4. 配置扩展
            if not self._configure_extension():
                return False
            
            # 5. 清理下载文件
            if zip_path.exists():
                zip_path.unlink()
            
            self.logger.info("✅ CapSolver扩展安装完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"安装CapSolver扩展时出错: {e}")
            return False
    
    def _download_extension(self, zip_path: Path) -> bool:
        """下载CapSolver扩展"""
        try:
            self.logger.info("📥 正在下载CapSolver扩展...")
            
            response = requests.get(self.download_url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"✅ 扩展下载完成: {zip_path}")
            return True
            
        except requests.RequestException as e:
            self.logger.error(f"下载CapSolver扩展失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"下载过程中出错: {e}")
            return False
    
    def _extract_extension(self, zip_path: Path) -> bool:
        """解压CapSolver扩展"""
        try:
            self.logger.info("📦 正在解压CapSolver扩展...")
            
            # 如果目标目录已存在，先删除
            if self.capsolver_dir.exists():
                shutil.rmtree(self.capsolver_dir)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.capsolver_dir)
            
            self.logger.info(f"✅ 扩展解压完成: {self.capsolver_dir}")
            return True
            
        except zipfile.BadZipFile as e:
            self.logger.error(f"ZIP文件损坏: {e}")
            return False
        except Exception as e:
            self.logger.error(f"解压过程中出错: {e}")
            return False
    
    def _configure_extension(self) -> bool:
        """配置CapSolver扩展"""
        try:
            self.logger.info("⚙️  正在配置CapSolver扩展...")
            
            # 确保assets目录存在
            assets_dir = self.capsolver_dir / "assets"
            assets_dir.mkdir(exist_ok=True)
            
            # 创建配置文件内容
            config_content = f"""export const defaultConfig = {{
    apiKey: '{self.api_key}',
    useCapsolver: true,
    enabledForRecaptcha: true,
    enabledForHcaptcha: true,
    enabledForGeetest: true,
    enabledForImageToText: true,
    reCaptchaMode: 'click',
    solvedCallback: 'captchaSolvedCallback',
    manualSolving: false,
    showSolveButton: true,
    useProxy: false,
    proxyType: 'http',
    hostOrIp: '',
    port: '',
    proxyLogin: '',
    proxyPassword: ''
}};"""
            
            # 写入配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            self.logger.info("✅ CapSolver扩展配置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"配置CapSolver扩展时出错: {e}")
            return False
    
    def get_extension_path(self) -> Optional[str]:
        """获取扩展路径"""
        if self.capsolver_dir.exists():
            return str(self.capsolver_dir.absolute())
        return None
    
    def ensure_extension_ready(self) -> Tuple[bool, Optional[str]]:
        """确保扩展准备就绪"""
        try:
            # 检查是否已安装
            if self.check_extension_installed():
                return True, self.get_extension_path()
            
            # 尝试安装
            self.logger.info("🔧 CapSolver扩展未安装，开始自动安装...")
            if self.install_extension():
                return True, self.get_extension_path()
            else:
                self.logger.error("❌ CapSolver扩展安装失败")
                return False, None
                
        except Exception as e:
            self.logger.error(f"确保扩展准备就绪时出错: {e}")
            return False, None
    
    def print_installation_guide(self):
        """打印安装指南"""
        print("\n" + "="*60)
        print("🎯 CapSolver扩展安装指南")
        print("="*60)
        print("如果自动安装失败，请手动安装：")
        print("1. 访问: https://github.com/capsolver/capsolver-browser-extension/releases")
        print("2. 下载最新版本的 capsolver-browser-extension.zip")
        print("3. 解压到 extensions/capsolver/ 目录")
        print("4. 配置 extensions/capsolver/assets/config.js 文件")
        print(f"5. 设置 apiKey: '{self.api_key}'")
        print("="*60)


def get_capsolver_manager() -> CapSolverManager:
    """获取CapSolver管理器实例"""
    return CapSolverManager()


# 测试函数
async def test_capsolver_manager():
    """测试CapSolver管理器"""
    manager = CapSolverManager()
    
    print("🧪 测试CapSolver管理器...")
    
    # 检查当前状态
    if manager.check_extension_installed():
        print("✅ 扩展已安装")
    else:
        print("❌ 扩展未安装，开始安装...")
        success = manager.install_extension()
        if success:
            print("✅ 安装成功")
        else:
            print("❌ 安装失败")
            manager.print_installation_guide()
    
    # 获取扩展路径
    path = manager.get_extension_path()
    print(f"📁 扩展路径: {path}")


if __name__ == "__main__":
    asyncio.run(test_capsolver_manager())
