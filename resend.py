#!/usr/bin/env python3
"""
Resend域名管理工具 - 批量操作版本 (HTTP POST请求)
支持从文件读取账户信息，提供完整的域名管理功能
使用纯HTTP POST请求替代SDK
"""

import sys
import time
import os
import re
import json
import requests
from typing import List, Dict, Optional, Tuple

class AccountInfo:
    """账户信息类"""
    def __init__(self, api_key: str, email: str, password: str):
        self.api_key = api_key
        self.email = email
        self.password = password
    
    def __str__(self):
        return f"{self.email} ({self.api_key[:10]}...)"


class ResendHTTPClient:
    """Resend HTTP客户端 - 使用纯POST请求"""
    
    def __init__(self):
        self.base_url = "https://api.resend.com"
        self.session = requests.Session()
        
    def _make_request(self, method: str, endpoint: str, api_key: str, data: Optional[Dict] = None) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """发送HTTP请求到Resend API"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "POST":
                response = self.session.post(url, headers=headers, json=data)
            elif method.upper() == "GET":
                response = self.session.get(url, headers=headers)
            elif method.upper() == "PATCH":
                response = self.session.patch(url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=headers)
            else:
                return False, None, f"不支持的HTTP方法: {method}", None
                
            # 检查响应状态
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    return True, result, None, response.status_code
                except json.JSONDecodeError:
                    return False, None, "响应不是有效的JSON格式", response.status_code
            else:
                # 尝试解析错误响应
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', f'HTTP {response.status_code} 错误')
                    return False, None, error_message, response.status_code
                except:
                    return False, None, f"HTTP {response.status_code}: {response.text}", response.status_code
                    
        except requests.exceptions.RequestException as e:
            return False, None, f"网络请求异常: {str(e)}", None
        except Exception as e:
            return False, None, f"未知错误: {str(e)}", None
    
    def create_domain(self, api_key: str, domain_name: str, region: str = "ap-northeast-1") -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """创建域名"""
        data = {
            "name": domain_name,
            "region": region
        }
        return self._make_request("POST", "/domains", api_key, data)
    
    def get_domain(self, api_key: str, domain_id: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """获取域名详情"""
        return self._make_request("GET", f"/domains/{domain_id}", api_key)
    
    def list_domains(self, api_key: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """列出所有域名"""
        return self._make_request("GET", "/domains", api_key)
    
    def verify_domain(self, api_key: str, domain_id: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """验证域名"""
        return self._make_request("POST", f"/domains/{domain_id}/verify", api_key)
    
    def update_domain(self, api_key: str, domain_id: str, update_data: Dict) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """更新域名"""
        return self._make_request("PATCH", f"/domains/{domain_id}", api_key, update_data)
    
    def delete_domain(self, api_key: str, domain_id: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """删除域名"""
        return self._make_request("DELETE", f"/domains/{domain_id}", api_key)


class ResendDomainManager:
    """Resend域名管理器 - 批量操作版本 (HTTP POST请求)"""

    def __init__(self):
        self.accounts: List[AccountInfo] = []
        self.http_client = ResendHTTPClient()
        self.request_delay = 0.5  # 每秒最多2次请求

    def load_accounts_from_file(self, file_path: str) -> bool:
        """从文件加载账户信息"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False

            self.accounts = []
            failed_lines = []  # 记录读取失败的行
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 解析格式: [API密钥]---[邮箱 密码]
                    match = re.match(r'\[([^\]]+)\]---\[([^\s]+)\s+([^\]]+)\]', line)
                    if match:
                        api_key, email, password = match.groups()
                        self.accounts.append(AccountInfo(
                            api_key=api_key.strip(),
                            email=email.strip(),
                            password=password.strip()
                        ))
                    else:
                        failed_lines.append((line_num, line))

            # 显示加载结果
            print(f"✅ 成功加载 {len(self.accounts)} 个账户")
            
            # 显示读取失败的行
            if failed_lines:
                print(f"❌ 读取失败的行 ({len(failed_lines)} 个):")
                for line_num, line in failed_lines:
                    print(f"   第{line_num}行: {line}")
                print()
            
            return len(self.accounts) > 0

        except Exception as e:
            print(f"❌ 加载账户文件失败: {e}")
            return False

    def select_input_file(self) -> bool:
        """选择输入文件"""
        print("\n📁 请选择账户文件:")
        print("1. output/registered_accounts.txt (默认)")
        print("2. 输入自定义文件路径")
        
        choice = input("请选择 (1-2, 默认1): ").strip() or "1"
        
        if choice == "1":
            file_path = "output/registered_accounts.txt"
        elif choice == "2":
            file_path = input("请输入文件路径: ").strip()
            if not file_path:
                print("❌ 文件路径不能为空")
                return False
        else:
            print("❌ 无效选择")
            return False
        
        return self.load_accounts_from_file(file_path)

    def display_accounts(self) -> None:
        """显示所有账户"""
        if not self.accounts:
            print("📭 没有加载的账户")
            return
        
        print(f"\n📋 已加载的账户 ({len(self.accounts)} 个):")
        for i, account in enumerate(self.accounts, 1):
            print(f"   {i:2d}. {account}")

    def select_accounts(self, prompt: str = "请选择要操作的账户") -> List[AccountInfo]:
        """选择要操作的账户"""
        if not self.accounts:
            print("❌ 没有可用的账户")
            return []
        
        self.display_accounts()
        
        print(f"\n{prompt}:")
        print("💡 输入格式: 1,3,5 或 1-5 或 all (全选)")
        
        selection = input("请输入选择: ").strip()
        
        if not selection:
            print("❌ 选择不能为空")
            return []
        
        if selection.lower() == "all":
            return self.accounts.copy()
        
        selected_accounts = []
        
        try:
            # 处理逗号分隔的选择
            parts = selection.split(',')
            for part in parts:
                part = part.strip()
                if '-' in part:
                    # 处理范围选择 (如 1-5)
                    start, end = map(int, part.split('-'))
                    for i in range(start, end + 1):
                        if 1 <= i <= len(self.accounts):
                            selected_accounts.append(self.accounts[i - 1])
                else:
                    # 处理单个选择
                    i = int(part)
                    if 1 <= i <= len(self.accounts):
                        selected_accounts.append(self.accounts[i - 1])
                    else:
                        print(f"⚠️ 忽略无效序号: {i}")
        
        except ValueError:
            print("❌ 输入格式错误")
            return []
        
        # 去重
        selected_accounts = list(dict.fromkeys(selected_accounts))
        
        if selected_accounts:
            print(f"✅ 已选择 {len(selected_accounts)} 个账户")
            for account in selected_accounts:
                print(f"   - {account}")
        
        return selected_accounts

    def wait_for_rate_limit(self):
        """等待以遵守API限制"""
        time.sleep(self.request_delay)

    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists("output"):
            os.makedirs("output")

    def generate_output_filename(self, operation_name: str) -> str:
        """生成输出文件名"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        return f"output/resend_{operation_name}_{timestamp}.txt"

    def save_accounts_to_file(self, accounts: List[AccountInfo], filename: str, title: str = "账户信息"):
        """将账户信息保存到文件"""
        try:
            self.ensure_output_dir()
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# {title}\n")
                f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总数量: {len(accounts)}\n\n")
                
                for account in accounts:
                    f.write(f"[{account.api_key}]---[{account.email} {account.password}]\n")
            
            print(f"✅ 结果已保存到文件: {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

    def load_domains_from_file(self, file_path: str) -> List[Tuple[str, str]]:
        """从文件加载域名信息
        
        Returns:
            List[Tuple[str, str]]: [(域名, CF账号), ...]
        """
        domains = []
        failed_lines = []
        
        try:
            if not os.path.exists(file_path):
                print(f"❌ 域名文件不存在: {file_path}")
                return domains

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 解析格式: 域名----CF账号
                    if '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            domain, cf_account = parts
                            domains.append((domain.strip(), cf_account.strip()))
                        else:
                            failed_lines.append((line_num, line))
                    else:
                        failed_lines.append((line_num, line))

            # 显示加载结果
            print(f"✅ 成功加载 {len(domains)} 个域名")
            
            # 显示读取失败的行
            if failed_lines:
                print(f"❌ 读取失败的行 ({len(failed_lines)} 个):")
                for line_num, line in failed_lines:
                    print(f"   第{line_num}行: {line}")
                print()
            
            return domains

        except Exception as e:
            print(f"❌ 加载域名文件失败: {e}")
            return domains

    def extract_main_domain(self, domain: str) -> str:
        """从子域名中提取主域名"""
        parts = domain.split('.')
        if len(parts) >= 2:
            return '.'.join(parts[-2:])  # 取最后两部分作为主域名
        return domain

    def classify_domain_error(self, error_message: str) -> str:
        """分类域名创建错误"""
        if not error_message:
            return "unknown"
        
        error_lower = error_message.lower()
        
        # 域名已被注册
        if ('domain has been' in error_lower or 
            'already exists' in error_lower or 
            'domain is already' in error_lower):
            return "domain_exists"
        
        # 账户被封锁
        if ('temporarily suspended' in error_lower or 
            'account suspended' in error_lower or 
            'check your email for more details' in error_lower):
            return "account_suspended"
        
        return "other"

    def save_error_report(self, failed_tasks: List[Dict], timestamp: str):
        """保存错误报告"""
        if not failed_tasks:
            return
        
        self.ensure_output_dir()
        error_filename = f"output/resend_create_domains_errors_{timestamp}.txt"
        
        try:
            with open(error_filename, 'w', encoding='utf-8') as f:
                f.write("# Resend域名创建错误报告\n")
                f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总失败任务: {len(failed_tasks)}\n\n")
                
                # 按错误类型分类
                domain_exists_tasks = [t for t in failed_tasks if t['error_type'] == 'domain_exists']
                account_suspended_tasks = [t for t in failed_tasks if t['error_type'] == 'account_suspended']
                other_tasks = [t for t in failed_tasks if t['error_type'] == 'other']
                
                if domain_exists_tasks:
                    f.write(f"## 域名已被注册 ({len(domain_exists_tasks)} 个):\n")
                    for task in domain_exists_tasks:
                        f.write(f"域名: {task['domain']} | 账户: {task['account_email']} | 错误: {task['error_message']}\n")
                    f.write("\n")
                
                if account_suspended_tasks:
                    f.write(f"## 账户被封锁 ({len(account_suspended_tasks)} 个):\n")
                    for task in account_suspended_tasks:
                        f.write(f"账户: {task['account_email']} | 域名: {task['domain']} | 错误: {task['error_message']}\n")
                    f.write("\n")
                
                if other_tasks:
                    f.write(f"## 其他错误 ({len(other_tasks)} 个):\n")
                    for task in other_tasks:
                        f.write(f"账户: {task['account_email']} | 域名: {task['domain']} | 错误: {task['error_message']}\n")
                    f.write("\n")
            
            print(f"📄 错误报告已保存: {error_filename}")
            
        except Exception as e:
            print(f"❌ 保存错误报告失败: {e}")

    def generate_smtp_files(self, successful_results: List[Dict], timestamp: str):
        """生成SMTP配置文件，按主域名分组"""
        if not successful_results:
            return
        
        # 按主域名分组
        domain_groups = {}
        for result in successful_results:
            domain = result['domain']
            main_domain = self.extract_main_domain(domain)
            
            if main_domain not in domain_groups:
                domain_groups[main_domain] = []
            domain_groups[main_domain].append(result)
        
        self.ensure_output_dir()
        date_str = time.strftime("%Y%m%d")
        
        # 为每个主域名生成文件
        for main_domain, results in domain_groups.items():
            count = len(results)
            smtp_filename = f"output/{main_domain}-{date_str}-{count}.txt"
            
            try:
                with open(smtp_filename, 'w', encoding='utf-8') as f:
                    f.write(f"# SMTP配置文件 - 主域名: {main_domain}\n")
                    f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 子域名数量: {count}\n\n")
                    
                    for result in results:
                        domain = result['domain']
                        account = result['account']
                        api_key = account.api_key
                        email = account.email
                        password = account.password
                        
                        # 格式: [子域名]---[smtp.resend.com]:[465]---[resend]---[api key]---[账号 密码]
                        line = f"[{domain}]---[smtp.resend.com]:[465]---[resend]---[{api_key}]---[{email} {password}]\n"
                        f.write(line)
                
                print(f"📧 SMTP文件已生成: {smtp_filename} ({count} 个子域名)")
                
            except Exception as e:
                print(f"❌ 生成SMTP文件失败 {smtp_filename}: {e}")

    def batch_create_domains(self):
        """批量创建域名 - 支持手动输入或文件输入"""
        selected_accounts = self.select_accounts("请选择要创建域名的账户")
        if not selected_accounts:
            return
        
        # 选择域名输入方式
        print("\n📝 请选择域名输入方式:")
        print("1. 手动输入单个域名")
        print("2. 从文件读取多个域名")
        
        input_choice = input("请选择 (1-2): ").strip()
        
        domains = []  # [(域名, CF账号), ...]
        
        if input_choice == "1":
            # 手动输入单个域名
            domain_name = input("\n请输入要创建的域名: ").strip()
            if not domain_name:
                print("❌ 域名不能为空")
                return
            
            cf_account = input("请输入对应的CF账号: ").strip()
            if not cf_account:
                print("❌ CF账号不能为空")
                return
            
            domains = [(domain_name, cf_account)]
            
        elif input_choice == "2":
            # 从文件读取域名
            print("\n📁 请选择域名文件:")
            print("1. domains.txt (默认)")
            print("2. 输入自定义文件路径")
            
            file_choice = input("请选择 (1-2, 默认1): ").strip() or "1"
            
            if file_choice == "1":
                file_path = "domains.txt"
            elif file_choice == "2":
                file_path = input("请输入域名文件路径: ").strip()
                if not file_path:
                    print("❌ 文件路径不能为空")
                    return
            else:
                print("❌ 无效选择")
                return
            
            domains = self.load_domains_from_file(file_path)
            if not domains:
                print("❌ 没有加载到有效的域名")
                return
        else:
            print("❌ 无效选择")
            return
        
        region = "ap-northeast-1"  # 固定区域
        print(f"🌏 使用区域: {region}")
        
        print(f"\n🚀 开始为 {len(selected_accounts)} 个账户创建 {len(domains)} 个域名")
        print("=" * 60)
        
        # 智能任务分配和错误处理
        current_account_index = 0
        current_domain_index = 0
        successful_results = []
        failed_tasks = []
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        while (current_account_index < len(selected_accounts) and 
               current_domain_index < len(domains)):
            
            account = selected_accounts[current_account_index]
            domain_name, cf_account = domains[current_domain_index]
            
            print(f"\n📋 处理: {account.email} -> {domain_name}")
            
            success, result, error_message, status_code = self.http_client.create_domain(
                api_key=account.api_key,
                domain_name=domain_name,
                region=region
            )
            
            if success and result:
                print("✅ 域名创建成功！")
                print(f"   域名ID: {result.get('id')}")
                print(f"   域名名称: {result.get('name')}")
                print(f"   状态: {result.get('status')}")
                print(f"   区域: {result.get('region')}")
                
                successful_results.append({
                    'account': account,
                    'domain': domain_name,
                    'cf_account': cf_account,
                    'domain_id': result.get('id')
                })
                
                # 成功后推进到下一个组合
                current_account_index += 1
                current_domain_index += 1
                
            else:
                error_type = self.classify_domain_error(error_message or "")
                
                print(f"❌ 创建失败:")
                print(f"   状态码: {status_code or 'Unknown'}")
                print(f"   错误消息: {error_message or 'Unknown'}")
                print(f"   错误类型: {error_type}")
                
                # 记录失败任务
                failed_tasks.append({
                    'account_email': account.email,
                    'domain': domain_name,
                    'cf_account': cf_account,
                    'error_message': error_message or 'Unknown',
                    'error_type': error_type,
                    'status_code': status_code
                })
                
                if error_type == "domain_exists":
                    # 域名已被注册 - 跳过域名，账户尝试下一个域名
                    print("   🔄 处理逻辑: 跳过域名，账户尝试下一个域名")
                    current_domain_index += 1
                    
                elif error_type == "account_suspended":
                    # 账户被封锁 - 跳过账户，域名给下一个账户
                    print("   🔄 处理逻辑: 跳过账户，域名给下一个账户")
                    current_account_index += 1
                    
                else:
                    # 其他错误 - 推进到下一个组合
                    print("   🔄 处理逻辑: 其他错误，推进到下一个组合")
                    current_account_index += 1
                    current_domain_index += 1
            
            # API限制
            self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 批量创建完成:")
        print(f"   成功: {len(successful_results)} 个")
        print(f"   失败: {len(failed_tasks)} 个")
        print(f"   剩余账户: {len(selected_accounts) - current_account_index}")
        print(f"   剩余域名: {len(domains) - current_domain_index}")
        
        # 生成输出文件
        if successful_results:
            # 1. 保存成功的账户信息
            successful_accounts = [r['account'] for r in successful_results]
            output_filename = self.generate_output_filename("create_domains")
            self.save_accounts_to_file(successful_accounts, output_filename, 
                                     f"域名创建成功的账户 - 总共 {len(successful_results)} 个域名")
            
            # 2. 生成SMTP配置文件
            self.generate_smtp_files(successful_results, timestamp)
        
        # 3. 生成错误报告
        if failed_tasks:
            self.save_error_report(failed_tasks, timestamp)

    def batch_list_domains(self):
        """批量列出域名"""
        selected_accounts = self.select_accounts("请选择要查询域名的账户")
        if not selected_accounts:
            return
        
        print(f"\n🔍 开始查询 {len(selected_accounts)} 个账户的域名")
        print("=" * 60)
        
        total_domains = 0
        for i, account in enumerate(selected_accounts, 1):
            print(f"\n📋 查询账户 {i}/{len(selected_accounts)}: {account.email}")
            print("-" * 40)
            
            success, result, error_message, status_code = self.http_client.list_domains(account.api_key)
            
            if success and result:
                domains = result.get('data', [])
                
                if not domains:
                    print("   📭 该账户没有域名")
                else:
                    print(f"   找到 {len(domains)} 个域名:")
                    for j, domain in enumerate(domains, 1):
                        print(f"     {j}. {domain.get('name')} (ID: {domain.get('id')})")
                        print(f"        状态: {domain.get('status')} | 区域: {domain.get('region')}")
                        print(f"        创建时间: {domain.get('created_at')}")
                
                total_domains += len(domains)
            else:
                print(f"❌ 查询失败:")
                print(f"   状态码: {status_code or 'Unknown'}")
                print(f"   错误消息: {error_message}")
            
            # API限制
            if i < len(selected_accounts):
                self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 查询完成: 总共找到 {total_domains} 个域名")
        
        # 生成输出文件
        if selected_accounts:
            output_filename = self.generate_output_filename("list_domains")
            self.save_accounts_to_file(selected_accounts, output_filename, 
                                     f"域名列表查询的账户 - 总共找到 {total_domains} 个域名")

    def batch_get_domains(self):
        """批量检索域名详情 - 自动查询域名列表供选择"""
        selected_accounts = self.select_accounts("请选择要检索域名的账户")
        if not selected_accounts:
            return
        
        # 先收集所有域名
        print(f"\n🔍 正在查询 {len(selected_accounts)} 个账户的域名列表...")
        all_domains = []  # [(account, domain_info)]
        
        for i, account in enumerate(selected_accounts, 1):
            print(f"📋 查询账户 {i}/{len(selected_accounts)}: {account.email}")
            
            success, result, error_message, status_code = self.http_client.list_domains(account.api_key)
            
            if success and result:
                domains = result.get('data', [])
                for domain in domains:
                    all_domains.append((account, domain))
            else:
                print(f"   ⚠️ 查询失败: {error_message}")
            
            # API限制
            if i < len(selected_accounts):
                self.wait_for_rate_limit()
        
        if not all_domains:
            print("📭 所有选中的账户都没有域名")
            return
        
        # 显示域名列表供选择
        print(f"\n📋 找到 {len(all_domains)} 个域名:")
        print("=" * 80)
        for i, (account, domain) in enumerate(all_domains, 1):
            print(f"{i:2d}. [{account.email}] {domain.get('name')} (ID: {domain.get('id')})")
            print(f"     状态: {domain.get('status')} | 区域: {domain.get('region')} | 创建: {domain.get('created_at')}")
            print()
        
        # 选择要检索详情的域名
        print("💡 输入格式: 1,3,5 或 1-5 或 all (全选)")
        selection = input("请选择要检索详情的域名: ").strip()
        
        if not selection:
            print("❌ 选择不能为空")
            return
        
        selected_domains = []
        
        if selection.lower() == "all":
            selected_domains = all_domains.copy()
        else:
            try:
                # 处理逗号分隔的选择
                parts = selection.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # 处理范围选择 (如 1-5)
                        start, end = map(int, part.split('-'))
                        for i in range(start, end + 1):
                            if 1 <= i <= len(all_domains):
                                selected_domains.append(all_domains[i - 1])
                    else:
                        # 处理单个选择
                        i = int(part)
                        if 1 <= i <= len(all_domains):
                            selected_domains.append(all_domains[i - 1])
                        else:
                            print(f"⚠️ 忽略无效序号: {i}")
            
            except ValueError:
                print("❌ 输入格式错误")
                return
        
        # 去重 - 使用域名ID作为唯一标识
        seen_domain_ids = set()
        unique_domains = []
        for account, domain in selected_domains:
            domain_id = domain.get('id')
            if domain_id not in seen_domain_ids:
                seen_domain_ids.add(domain_id)
                unique_domains.append((account, domain))
        
        selected_domains = unique_domains
        
        if not selected_domains:
            print("❌ 没有选择有效的域名")
            return
        
        print(f"\n🔍 开始检索 {len(selected_domains)} 个域名的详情")
        print("=" * 60)
        
        # 检索选中域名的详情
        non_verified_accounts = []  # 记录状态非verified的账户
        successful_retrievals = []  # 记录成功检索的账户
        
        for i, (account, domain) in enumerate(selected_domains, 1):
            domain_id = domain.get('id')
            domain_name = domain.get('name')
            
            print(f"\n📋 检索域名 {i}/{len(selected_domains)}: {domain_name}")
            print(f"   账户: {account.email}")
            
            success, result, error_message, status_code = self.http_client.get_domain(
                api_key=account.api_key,
                domain_id=domain_id
            )
            
            if success and result:
                print("✅ 检索成功！")
                domain_status = result.get('status')
                print(f"   域名ID: {result.get('id')}")
                print(f"   域名名称: {result.get('name')}")
                print(f"   状态: {domain_status}")
                print(f"   区域: {result.get('region')}")
                print(f"   创建时间: {result.get('created_at')}")
                
                # 检查域名状态，如果不是verified则记录账户
                if domain_status != 'verified':
                    non_verified_accounts.append(account)
                
                successful_retrievals.append(account)
                
                # 显示DNS记录
                records = result.get('records', [])
                if records:
                    print("   📋 DNS记录:")
                    for j, record in enumerate(records, 1):
                        print(f"     {j}. {record.get('record')} - {record.get('name')}")
                        print(f"        值: {record.get('value')}")
                        print(f"        状态: {record.get('status')}")
                else:
                    print("   📋 暂无DNS记录")
            else:
                print(f"❌ 检索失败:")
                print(f"   状态码: {status_code or 'Unknown'}")
                print(f"   错误消息: {error_message}")
            
            # API限制
            if i < len(selected_domains):
                self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 检索完成: 处理了 {len(selected_domains)} 个域名")
        
        # 生成输出文件
        if successful_retrievals:
            # 1. 保存所有成功检索的账户
            output_filename = self.generate_output_filename("get_domains_all")
            self.save_accounts_to_file(successful_retrievals, output_filename, 
                                     "域名检索成功的所有账户")
        
        # 2. 保存状态非verified的账户（重点功能）
        if non_verified_accounts:
            non_verified_filename = self.generate_output_filename("get_domains_non_verified")
            self.save_accounts_to_file(non_verified_accounts, non_verified_filename, 
                                     "域名状态非verified的账户")
            print(f"🔍 特别提醒: 找到 {len(non_verified_accounts)} 个状态非verified的账户")
        else:
            print("✅ 所有检索的域名状态都是verified")

    def batch_verify_domains(self):
        """批量验证域名 - 自动查询域名列表供选择"""
        selected_accounts = self.select_accounts("请选择要验证域名的账户")
        if not selected_accounts:
            return
        
        # 先收集所有域名
        print(f"\n🔍 正在查询 {len(selected_accounts)} 个账户的域名列表...")
        all_domains = []  # [(account, domain_info)]
        
        for i, account in enumerate(selected_accounts, 1):
            print(f"📋 查询账户 {i}/{len(selected_accounts)}: {account.email}")
            
            success, result, error_message, status_code = self.http_client.list_domains(account.api_key)
            
            if success and result:
                domains = result.get('data', [])
                for domain in domains:
                    all_domains.append((account, domain))
            else:
                print(f"   ⚠️ 查询失败: {error_message}")
            
            # API限制
            if i < len(selected_accounts):
                self.wait_for_rate_limit()
        
        if not all_domains:
            print("📭 所有选中的账户都没有域名")
            return
        
        # 显示域名列表供选择
        print(f"\n📋 找到 {len(all_domains)} 个域名:")
        print("=" * 80)
        for i, (account, domain) in enumerate(all_domains, 1):
            print(f"{i:2d}. [{account.email}] {domain.get('name')} (ID: {domain.get('id')})")
            print(f"     状态: {domain.get('status')} | 区域: {domain.get('region')} | 创建: {domain.get('created_at')}")
            print()
        
        # 选择要验证的域名
        print("💡 输入格式: 1,3,5 或 1-5 或 all (全选)")
        selection = input("请选择要验证的域名: ").strip()
        
        if not selection:
            print("❌ 选择不能为空")
            return
        
        selected_domains = []
        
        if selection.lower() == "all":
            selected_domains = all_domains.copy()
            print(f"✅ 已选择所有 {len(selected_domains)} 个域名进行验证")
        else:
            try:
                # 处理逗号分隔的选择
                parts = selection.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # 处理范围选择 (如 1-5)
                        start, end = map(int, part.split('-'))
                        for i in range(start, end + 1):
                            if 1 <= i <= len(all_domains):
                                selected_domains.append(all_domains[i - 1])
                    else:
                        # 处理单个选择
                        i = int(part)
                        if 1 <= i <= len(all_domains):
                            selected_domains.append(all_domains[i - 1])
                        else:
                            print(f"⚠️ 忽略无效序号: {i}")
            
            except ValueError:
                print("❌ 输入格式错误")
                return
        
        # 去重 - 使用域名ID作为唯一标识
        seen_domain_ids = set()
        unique_domains = []
        for account, domain in selected_domains:
            domain_id = domain.get('id')
            if domain_id not in seen_domain_ids:
                seen_domain_ids.add(domain_id)
                unique_domains.append((account, domain))
        
        selected_domains = unique_domains
        
        if not selected_domains:
            print("❌ 没有选择有效的域名")
            return
        
        print(f"✅ 已选择 {len(selected_domains)} 个域名进行验证")
        
        print(f"\n🔍 开始验证 {len(selected_domains)} 个域名")
        print("=" * 60)
        
        # 验证选中的域名
        success_count = 0
        successful_accounts = []
        
        for i, (account, domain) in enumerate(selected_domains, 1):
            domain_id = domain.get('id')
            domain_name = domain.get('name')
            
            print(f"\n📋 验证域名 {i}/{len(selected_domains)}: {domain_name}")
            print(f"   账户: {account.email}")
            print(f"   域名ID: {domain_id}")
            
            success, result, error_message, status_code = self.http_client.verify_domain(
                api_key=account.api_key,
                domain_id=domain_id
            )
            
            if success and result:
                print("✅ 验证请求已发送！")
                print(f"   返回域名ID: {result.get('id')}")
                print(f"   对象类型: {result.get('object')}")
                
                # 验证后再次查询域名状态
                print("   🔍 查询验证后的域名状态...")
                get_success, get_result, get_error, get_status = self.http_client.get_domain(
                    api_key=account.api_key,
                    domain_id=domain_id
                )
                
                if get_success and get_result:
                    new_status = get_result.get('status')
                    print(f"   📊 验证后状态: {new_status}")
                    
                    # 显示DNS记录状态
                    records = get_result.get('records', [])
                    if records:
                        print("   📋 DNS记录状态:")
                        for j, record in enumerate(records, 1):
                            record_status = record.get('status', 'unknown')
                            record_name = record.get('name', 'unknown')
                            record_type = record.get('type', 'unknown')
                            print(f"     {j}. {record_name} ({record_type}): {record_status}")
                else:
                    print(f"   ⚠️ 无法获取验证后状态: {get_error}")
                
                success_count += 1
                successful_accounts.append(account)
            else:
                print(f"❌ 验证失败:")
                print(f"   状态码: {status_code or 'Unknown'}")
                print(f"   错误消息: {error_message}")
            
            # API限制
            if i < len(selected_domains):
                self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 验证完成: 成功 {success_count}/{len(selected_domains)} 个")
        
        # 生成输出文件
        if successful_accounts:
            # 去重
            unique_accounts = list({account.api_key: account for account in successful_accounts}.values())
            output_filename = self.generate_output_filename("verify_domains")
            self.save_accounts_to_file(unique_accounts, output_filename, 
                                     "域名验证成功的账户")

    def batch_update_domains(self):
        """批量更新域名"""
        selected_accounts = self.select_accounts("请选择要更新域名的账户")
        if not selected_accounts:
            return
        
        domain_id = input("\n请输入要更新的域名ID: ").strip()
        if not domain_id:
            print("❌ 域名ID不能为空")
            return
        
        print("\n📝 请选择要更新的属性:")
        print("1. 点击跟踪 (click_tracking)")
        print("2. 打开跟踪 (open_tracking)")
        print("3. TLS (tls)")
        
        update_choice = input("请选择 (1-3): ").strip()
        
        if update_choice == "1":
            property_name = "click_tracking"
        elif update_choice == "2":
            property_name = "open_tracking"
        elif update_choice == "3":
            property_name = "tls"
        else:
            print("❌ 无效选择")
            return
        
        value = input(f"请输入 {property_name} 的值 (true/false): ").strip().lower()
        if value not in ["true", "false"]:
            print("❌ 值必须是 true 或 false")
            return
        
        value_bool = value == "true"
        update_data = {property_name: value_bool}
        
        print(f"\n🔄 开始在 {len(selected_accounts)} 个账户中更新域名: {domain_id}")
        print(f"   更新属性: {property_name} = {value_bool}")
        print("=" * 60)
        
        success_count = 0
        successful_accounts = []
        
        for i, account in enumerate(selected_accounts, 1):
            print(f"\n📋 更新账户 {i}/{len(selected_accounts)}: {account.email}")
            
            success, result, error_message, status_code = self.http_client.update_domain(
                api_key=account.api_key,
                domain_id=domain_id,
                update_data=update_data
            )
            
            if success and result:
                print("✅ 域名更新成功！")
                print(f"   域名ID: {result.get('id')}")
                print(f"   域名名称: {result.get('name')}")
                print(f"   {property_name}: {result.get(property_name)}")
                
                success_count += 1
                successful_accounts.append(account)
            else:
                if status_code == 404:
                    print("   ℹ️ 该账户中未找到此域名")
                else:
                    print(f"❌ 更新失败:")
                    print(f"   状态码: {status_code or 'Unknown'}")
                    print(f"   错误消息: {error_message}")
            
            # API限制
            if i < len(selected_accounts):
                self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 更新完成: 成功 {success_count}/{len(selected_accounts)} 个")
        
        # 生成输出文件
        if successful_accounts:
            output_filename = self.generate_output_filename("update_domains")
            self.save_accounts_to_file(successful_accounts, output_filename, 
                                     f"域名更新成功的账户 - 域名ID: {domain_id}, 属性: {property_name}={value_bool}")

    def batch_delete_domains(self):
        """批量删除域名 - 自动查询域名列表供选择"""
        selected_accounts = self.select_accounts("请选择要删除域名的账户")
        if not selected_accounts:
            return
        
        # 先收集所有域名
        print(f"\n🔍 正在查询 {len(selected_accounts)} 个账户的域名列表...")
        all_domains = []  # [(account, domain_info)]
        
        for i, account in enumerate(selected_accounts, 1):
            print(f"📋 查询账户 {i}/{len(selected_accounts)}: {account.email}")
            
            success, result, error_message, status_code = self.http_client.list_domains(account.api_key)
            
            if success and result:
                domains = result.get('data', [])
                for domain in domains:
                    all_domains.append((account, domain))
            else:
                print(f"   ⚠️ 查询失败: {error_message}")
            
            # API限制
            if i < len(selected_accounts):
                self.wait_for_rate_limit()
        
        if not all_domains:
            print("📭 所有选中的账户都没有域名")
            return
        
        # 显示域名列表供选择
        print(f"\n📋 找到 {len(all_domains)} 个域名:")
        print("=" * 80)
        for i, (account, domain) in enumerate(all_domains, 1):
            print(f"{i:2d}. [{account.email}] {domain.get('name')} (ID: {domain.get('id')})")
            print(f"     状态: {domain.get('status')} | 区域: {domain.get('region')} | 创建: {domain.get('created_at')}")
            print()
        
        # 选择要删除的域名
        print("💡 输入格式: 1,3,5 或 1-5 或 all (全选)")
        selection = input("请选择要删除的域名: ").strip()
        
        if not selection:
            print("❌ 选择不能为空")
            return
        
        selected_domains = []
        
        if selection.lower() == "all":
            selected_domains = all_domains.copy()
            print(f"✅ 已选择所有 {len(selected_domains)} 个域名进行删除")
        else:
            try:
                # 处理逗号分隔的选择
                parts = selection.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # 处理范围选择 (如 1-5)
                        start, end = map(int, part.split('-'))
                        for i in range(start, end + 1):
                            if 1 <= i <= len(all_domains):
                                selected_domains.append(all_domains[i - 1])
                    else:
                        # 处理单个选择
                        i = int(part)
                        if 1 <= i <= len(all_domains):
                            selected_domains.append(all_domains[i - 1])
                        else:
                            print(f"⚠️ 忽略无效序号: {i}")
            
            except ValueError:
                print("❌ 输入格式错误")
                return
        
        # 去重 - 使用域名ID作为唯一标识
        seen_domain_ids = set()
        unique_domains = []
        for account, domain in selected_domains:
            domain_id = domain.get('id')
            if domain_id not in seen_domain_ids:
                seen_domain_ids.add(domain_id)
                unique_domains.append((account, domain))
        
        selected_domains = unique_domains
        
        if not selected_domains:
            print("❌ 没有选择有效的域名")
            return
        
        # 确认删除
        print(f"\n⚠️ 即将删除 {len(selected_domains)} 个域名:")
        for account, domain in selected_domains:
            print(f"   - [{account.email}] {domain.get('name')} (ID: {domain.get('id')})")
        
        confirm = input("\n确认删除？(y/N): ").strip().lower()
        
        if confirm != 'y':
            print("❌ 删除已取消")
            return
        
        print(f"\n🗑️ 开始删除 {len(selected_domains)} 个域名")
        print("=" * 60)
        
        # 删除选中的域名
        success_count = 0
        successful_accounts = []
        
        for i, (account, domain) in enumerate(selected_domains, 1):
            domain_id = domain.get('id')
            domain_name = domain.get('name')
            
            print(f"\n📋 删除域名 {i}/{len(selected_domains)}: {domain_name}")
            print(f"   账户: {account.email}")
            print(f"   域名ID: {domain_id}")
            
            success, result, error_message, status_code = self.http_client.delete_domain(
                api_key=account.api_key,
                domain_id=domain_id
            )
            
            if success and result:
                print("✅ 域名删除成功！")
                print(f"   域名ID: {result.get('id')}")
                print(f"   删除状态: {result.get('deleted')}")
                
                success_count += 1
                successful_accounts.append(account)
            else:
                print(f"❌ 删除失败:")
                print(f"   状态码: {status_code or 'Unknown'}")
                print(f"   错误消息: {error_message}")
            
            # API限制
            if i < len(selected_domains):
                self.wait_for_rate_limit()
        
        print("\n" + "=" * 60)
        print(f"📊 删除完成: 成功 {success_count}/{len(selected_domains)} 个")
        
        # 生成输出文件
        if successful_accounts:
            # 去重
            unique_accounts = list({account.api_key: account for account in successful_accounts}.values())
            output_filename = self.generate_output_filename("delete_domains")
            self.save_accounts_to_file(unique_accounts, output_filename, 
                                     f"域名删除成功的账户 - 删除了 {success_count} 个域名")


def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("🌐 Resend域名管理工具 - 批量操作版本 (HTTP POST请求)")
    print("=" * 60)
    print("1. 批量创建域名")
    print("2. 批量检索域名详情")
    print("3. 批量验证域名")
    print("4. 批量更新域名")
    print("5. 批量列出域名")
    print("6. 批量删除域名")
    print("7. 显示已加载的账户")
    print("8. 重新选择输入文件")
    print("0. 退出")
    print("=" * 60)


def main():
    """主函数"""
    print("🚀 Resend域名管理工具 - 批量操作版本 (HTTP POST请求)")
    print("💡 支持从文件读取账户信息，进行批量域名操作")
    print("🔧 使用纯HTTP POST请求，无需SDK依赖")
    
    manager = ResendDomainManager()
    
    # 选择输入文件
    if not manager.select_input_file():
        print("❌ 无法加载账户文件，程序退出")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择功能 (0-8): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            
            elif choice == "1":
                manager.batch_create_domains()
            
            elif choice == "2":
                manager.batch_get_domains()
            
            elif choice == "3":
                manager.batch_verify_domains()
            
            elif choice == "4":
                manager.batch_update_domains()
            
            elif choice == "5":
                manager.batch_list_domains()
            
            elif choice == "6":
                manager.batch_delete_domains()
            
            elif choice == "7":
                manager.display_accounts()
            
            elif choice == "8":
                if not manager.select_input_file():
                    print("❌ 无法加载账户文件")
            
            else:
                print("❌ 无效选择，请输入0-8之间的数字")
        
        except KeyboardInterrupt:
            print("\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"💥 发生异常: {e}")


if __name__ == "__main__":
    main()
