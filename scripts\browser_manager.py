"""
IX浏览器管理模块
实现浏览器配置文件的创建、打开功能
"""

import yaml
import logging
import random
import string
from typing import Optional, Dict, Any
from ixbrowser_local_api import IXBrowserClient
from ixbrowser_local_api import Profile, Proxy, Consts


class BrowserManager:
    def __init__(self, config_path: str = "input/config.yaml"):
        """
        初始化浏览器管理器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.client = self._create_ix_client()
        self._setup_logging()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            raise
            
    def _create_ix_client(self) -> IXBrowserClient:
        """
        创建IX浏览器客户端
        
        Returns:
            配置好的IXBrowserClient实例
        """
        try:
            # 创建默认的IXBrowserClient实例（使用默认端口53200）
            client = IXBrowserClient()
            return client
            
        except Exception as e:
            print(f"创建IX客户端失败: {e}")
            raise
    
    def _setup_logging(self):
        """设置日志"""
        # 只创建logger实例，不配置文件输出
        self.logger = logging.getLogger(__name__)
        
    def _generate_session_id(self, length: int = 8) -> str:
        """
        生成随机session ID（确保包含大小写字母和数字）

        Args:
            length: session ID长度，默认8位

        Returns:
            随机生成的session ID
        """
        import string
        import random

        # 确保至少有1个数字和1个字母
        digits = string.digits
        letters = string.ascii_letters

        # 先生成至少1个数字和1个字母
        session_chars = [
            random.choice(digits),
            random.choice(letters)
        ]

        # 剩余位数从数字和字母中随机选择
        remaining_chars = digits + letters
        for _ in range(length - 2):
            session_chars.append(random.choice(remaining_chars))

        # 随机打乱顺序
        random.shuffle(session_chars)

        return ''.join(session_chars)
        
    def _build_proxy_username(self, base_username: str, session_time: int = 120) -> str:
        """
        构建代理用户名，添加sessid参数

        Args:
            base_username: 基础用户名
            session_time: session时间（分钟），默认120分钟

        Returns:
            带sessid参数的用户名
        """
        session_id = self._generate_session_id()
        proxy_username = f"{base_username}-sessid-{session_id}-sessTime-{session_time}"
        return proxy_username
        
    def create_profile(self) -> Optional[Dict[str, Any]]:
        """
        创建浏览器配置文件

        Returns:
            创建结果，包含profile_id等信息
        """
        try:
            # 获取配置
            create_config = self.config['ix_browser']['create_profile']
            basic_config = create_config['basic']
            proxy_config = create_config['proxy']
            
            # 创建Profile对象
            profile = Profile()
            
            # 设置基本信息
            profile.color = basic_config['color']
            profile.name = basic_config['name']
            profile.group_id = basic_config['group_id']
            
            # 处理标签 - 如果是列表则转换为字符串
            if isinstance(basic_config['tag'], list):
                profile.tag = ','.join(basic_config['tag'])  # type: ignore
            else:
                profile.tag = basic_config['tag']  # type: ignore
                
            profile.site_id = int(basic_config['site_id'])
            if basic_config['site_url']:
                profile.site_url = basic_config['site_url']
                
            # 设置代理
            proxy = Proxy()
            if proxy_config['proxy_type'] == 'direct':
                # 直连模式
                proxy.proxy_mode = 2
                proxy.proxy_type = 'direct'
            elif proxy_config['proxy_type'] == 'socks5':
                # SOCKS5代理模式
                proxy.change_to_custom_mode(
                    Consts.PROXY_TYPE_SOCKS5,
                    proxy_config['proxy_ip'],
                    proxy_config['proxy_port']
                )
                if proxy_config['proxy_user']:
                    # 构建带session参数的用户名
                    proxy_username = self._build_proxy_username(proxy_config['proxy_user'])
                    proxy.proxy_user = proxy_username
                if proxy_config['proxy_password']:
                    proxy.proxy_password = proxy_config['proxy_password']
            elif proxy_config['proxy_type'] == 'http':
                # HTTP代理模式
                proxy.change_to_custom_mode(
                    Consts.PROXY_TYPE_HTTP,
                    proxy_config['proxy_ip'],
                    proxy_config['proxy_port']
                )
                if proxy_config['proxy_user']:
                    # 构建带session参数的用户名
                    proxy_username = self._build_proxy_username(proxy_config['proxy_user'])
                    proxy.proxy_user = proxy_username
                if proxy_config['proxy_password']:
                    proxy.proxy_password = proxy_config['proxy_password']

                    
            # 设置代理绕过
            if proxy_config['enable_bypass'] == "1" and proxy_config['bypass_list']:
                bypass_list = proxy_config['bypass_list'].split('\n')
                proxy.set_bypass_list(bypass_list)
                
            profile.proxy_config = proxy
            
            # 创建配置文件
            result = self.client.create_profile(profile)
            
            if result is None:
                error_msg = f"创建配置文件失败 - 错误代码: {self.client.code}, 错误信息: {self.client.message}"
                self.logger.error(error_msg)
                return None
            else:
                # 检查返回结果类型
                if isinstance(result, int):
                    # 如果返回的是整数，说明是profile_id
                    return {"profile_id": result}
                elif isinstance(result, dict):
                    # 如果返回的是字典
                    return result
                else:
                    # 其他情况
                    return {"profile_id": result}
                
        except Exception as e:
            error_msg = f"创建配置文件异常: {str(e)}"
            self.logger.error(error_msg)
            return None
            
    def open_browser(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """
        打开浏览器
        
        Args:
            profile_id: 浏览器配置文件ID
            
        Returns:
            打开结果，包含webdriver路径和调试地址
        """
        try:
            # 获取打开配置，如果配置中没有相关参数则使用默认值
            open_config = self.config['ix_browser'].get('open_profile', {})
            cookies_backup = bool(open_config.get('cookies_backup', 1))
            load_profile_info_page = bool(open_config.get('load_profile_info_page', 1))
            
            # 打开浏览器
            result = self.client.open_profile(
                profile_id,
                cookies_backup=cookies_backup,
                load_profile_info_page=load_profile_info_page
            )
            
            if result is None:
                error_msg = f"打开浏览器失败 - Profile ID: {profile_id}, 错误代码: {self.client.code}, 错误信息: {self.client.message}"
                self.logger.error(error_msg)
                return None
            else:
                # 确保result是字典类型
                if isinstance(result, dict):
                    return result
                else:
                    # 如果不是字典，转换为字典格式
                    return {"result": result}
                
        except Exception as e:
            error_msg = f"打开浏览器异常 - Profile ID: {profile_id}, 异常: {str(e)}"
            self.logger.error(error_msg)
            return None

    def open_browser_with_retry(self, profile_id: int, max_retries: int = 3, retry_delay: int = 2) -> Optional[Dict[str, Any]]:
        """
        打开浏览器（带重试机制，支持代理问题自动修复）

        Args:
            profile_id: 浏览器配置文件ID
            max_retries: 最大重试次数，默认3次
            retry_delay: 重试间隔（秒），默认2秒

        Returns:
            打开结果，包含webdriver路径和调试地址
        """
        import time

        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试打开浏览器 Profile {profile_id} (尝试 {attempt + 1}/{max_retries})")

                result = self.open_browser(profile_id)

                if result:
                    self.logger.info(f"✅ 浏览器打开成功 (尝试 {attempt + 1}/{max_retries})")
                    return result
                else:
                    self.logger.warning(f"⚠️ 浏览器打开失败 (尝试 {attempt + 1}/{max_retries})")

                    # 如果不是最后一次尝试，检查是否为代理问题并尝试修复
                    if attempt < max_retries - 1:
                        # 检查IX客户端的错误信息，判断是否为代理问题
                        error_info = f"错误代码: {getattr(self.client, 'code', '')}, 错误信息: {getattr(self.client, 'message', '')}"
                        if self._is_proxy_error(error_info):
                            self.logger.info("🔧 检测到代理问题，执行完整代理切换流程...")
                            
                            # 步骤1: 关闭IX浏览器窗口（必须！）
                            self.logger.info("📋 步骤1: 关闭IX浏览器窗口")
                            try:
                                close_result = self.close_browser(profile_id)
                                if close_result:
                                    self.logger.info("✅ IX浏览器窗口已关闭")
                                else:
                                    self.logger.warning("⚠️ IX浏览器窗口关闭失败")
                            except Exception as close_e:
                                self.logger.warning(f"⚠️ 关闭窗口时出现异常: {close_e}")
                            
                            # 等待窗口完全关闭
                            time.sleep(2)
                            
                            # 步骤2: 更新代理配置
                            self.logger.info("📋 步骤2: 更新代理配置")
                            proxy_updated = self.update_proxy_for_profile(profile_id)
                            if proxy_updated:
                                self.logger.info("✅ 代理配置更新成功，继续重试")
                            else:
                                self.logger.warning("⚠️ 代理配置更新失败")

                        self.logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)

            except Exception as e:
                self.logger.error(f"❌ 打开浏览器异常 (尝试 {attempt + 1}/{max_retries}): {e}")

                # 如果不是最后一次尝试，检查是否为代理问题并尝试修复
                if attempt < max_retries - 1:
                    # 检查异常信息是否包含代理相关错误
                    if self._is_proxy_error(str(e)):
                        self.logger.info("🔧 检测到代理异常，先关闭当前窗口...")
                        
                        # 关键修复：先关闭可能已经打开但有问题的浏览器窗口
                        try:
                            close_result = self.close_browser(profile_id)
                            if close_result:
                                self.logger.info("✅ 已关闭问题窗口")
                            else:
                                self.logger.warning("⚠️ 关闭窗口失败或窗口未打开")
                        except Exception as close_e:
                            self.logger.warning(f"⚠️ 关闭窗口时出现异常: {close_e}")
                        
                        # 然后重新生成代理session参数
                        self.logger.info("🔄 重新生成代理session参数...")
                        proxy_updated = self.update_proxy_for_profile(profile_id)
                        if proxy_updated:
                            self.logger.info("✅ 代理session更新成功，继续重试")
                        else:
                            self.logger.warning("⚠️ 代理session更新失败")

                    self.logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)

        self.logger.error(f"❌ 浏览器打开最终失败，已尝试 {max_retries} 次")
        return None

    def close_browser(self, profile_id: int) -> bool:
        """
        关闭浏览器
        
        Args:
            profile_id: 浏览器配置文件ID
            
        Returns:
            是否成功关闭
        """
        try:
            result = self.client.close_profile(profile_id)
            
            if result is None:
                error_msg = f"关闭浏览器失败 - Profile ID: {profile_id}, 错误代码: {self.client.code}, 错误信息: {self.client.message}"
                self.logger.error(error_msg)
                return False
            else:
                return True
                
        except Exception as e:
            error_msg = f"关闭浏览器异常 - Profile ID: {profile_id}, 异常: {str(e)}"
            self.logger.error(error_msg)
            return False

    def get_proxy_dict_for_yopmail(self) -> Optional[Dict[str, str]]:
        """
        获取用于yopmail的代理配置字典
        注意：只返回HTTP代理，因为SOCKS5代理不支持HTTPS连接

        Returns:
            代理配置字典，格式为 {'http': 'proxy_url'}
        """
        try:
            proxy_config = self.config['ix_browser']['create_profile']['proxy']

            if proxy_config['proxy_type'] == 'direct':
                return None

            # 生成带session的代理用户名
            proxy_username = self._build_proxy_username(proxy_config['proxy_user'])

            # 构建代理URL
            proxy_url = f"{proxy_config['proxy_type']}://{proxy_username}:{proxy_config['proxy_password']}@{proxy_config['proxy_ip']}:{proxy_config['proxy_port']}"

            # 只返回HTTP代理，不设置HTTPS代理
            # 因为SOCKS5代理不支持HTTPS连接
            return {
                'http': proxy_url
                # 不设置 'https' 键，让HTTPS请求通过系统默认方式处理
            }
        except Exception as e:
            self.logger.error(f"获取yopmail代理配置失败: {e}")
            return None

    def update_profile(self, profile_id: int, name: Optional[str] = None, note: Optional[str] = None,
                      username: Optional[str] = None, password: Optional[str] = None, api_key: Optional[str] = None) -> bool:
        """
        更新浏览器配置文件的4个字段

        Args:
            profile_id: 浏览器配置文件ID
            name: 窗口名称（邮箱）
            note: 备注（如果提供了api_key，会自动格式化为：账户----密码----API密钥）
            username: 平台用户名
            password: 平台密码
            api_key: API密钥（可选，如果提供会自动加入备注）

        Returns:
            是否更新成功
        """
        try:
            # 创建Profile对象用于更新
            profile = Profile()
            profile.profile_id = profile_id  # type: ignore

            # 只设置需要更新的字段
            if name is not None:
                profile.name = name  # type: ignore

            # 智能处理备注格式
            if note is not None or api_key is not None:
                if api_key is not None and username is not None and password is not None:
                    # 如果提供了API密钥，自动格式化备注：邮箱----密码----API密钥
                    formatted_note = f"{username}----{password}----{api_key}"
                    profile.note = formatted_note  # type: ignore
                    self.logger.info(f"自动格式化备注: {username}----{password}----{api_key[:10]}...")
                elif note is not None:
                    # 使用提供的备注
                    profile.note = note  # type: ignore

            if username is not None:
                profile.username = username  # type: ignore

            if password is not None:
                profile.password = password  # type: ignore

            # 硬编码设置Resend平台配置
            profile.site_id = 22  # type: ignore  # Resend平台ID
            profile.site_url = "https://resend.com/domains"  # type: ignore  # Resend指定平台地址

            # 调用IX API更新配置文件
            result = self.client.update_profile(profile)

            if result is None:
                error_msg = f"更新配置文件失败 - Profile ID: {profile_id}, 错误代码: {self.client.code}, 错误信息: {self.client.message}"
                self.logger.error(error_msg)
                return False
            else:
                return True

        except Exception as e:
            error_msg = f"更新配置文件异常 - Profile ID: {profile_id}, 异常: {str(e)}"
            self.logger.error(error_msg)
            return False

    def get_profile_list(self) -> Optional[list]:
        """
        获取浏览器配置文件列表
        
        Returns:
            配置文件列表
        """
        try:
            result = self.client.get_profile_list()
            
            if result is None:
                error_msg = f"获取配置文件列表失败 - 错误代码: {self.client.code}, 错误信息: {self.client.message}"
                self.logger.error(error_msg)
                return None
            else:
                # 确保result是列表类型
                if isinstance(result, list):
                    return result
                else:
                    # 如果不是列表，转换为列表格式
                    return [result] if result is not None else []
                
        except Exception as e:
            error_msg = f"获取配置文件列表异常: {str(e)}"
            self.logger.error(error_msg)
            return None

    def get_browser_connection_info(self, browser_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        从IX浏览器返回信息中提取连接信息

        Args:
            browser_info: IX浏览器打开后返回的信息

        Returns:
            连接信息字典
        """
        connection_info = {
            'debug_port': None,
            'ws_endpoint': None,
            'cdp_endpoint': None,
            'raw_info': browser_info
        }

        if not isinstance(browser_info, dict):
            self.logger.warning(f"浏览器信息格式异常: {type(browser_info)}")
            return connection_info

        # 动态提取调试端口 - 遍历所有字段寻找端口信息
        for key, value in browser_info.items():
            # 查找包含port的字段
            if 'port' in key.lower() and isinstance(value, int) and value > 1000:
                connection_info['debug_port'] = value
                self.logger.info(f"找到调试端口字段: {key} = {value}")
                break

        # 动态提取WebSocket端点 - 遍历所有字段寻找ws://开头的URL
        for key, value in browser_info.items():
            if isinstance(value, str) and value.startswith('ws://'):
                connection_info['ws_endpoint'] = value
                self.logger.debug(f"找到WebSocket端点字段: {key} = {value}")
                break

        # 构建CDP端点
        if connection_info['ws_endpoint']:
            connection_info['cdp_endpoint'] = connection_info['ws_endpoint']
        elif connection_info['debug_port']:
            connection_info['cdp_endpoint'] = f"ws://127.0.0.1:{connection_info['debug_port']}"
        else:
            # 使用默认端口
            connection_info['cdp_endpoint'] = "ws://127.0.0.1:9222"
            self.logger.warning("未找到调试端口信息，使用默认端口9222")

        self.logger.debug(f"提取的连接信息: {connection_info}")
        return connection_info

    def update_proxy_for_profile(self, profile_id: int) -> bool:
        """
        为指定配置文件更新代理（生成新的session）

        Args:
            profile_id: 浏览器配置文件ID

        Returns:
            是否更新成功
        """
        try:
            self.logger.info(f"为Profile {profile_id} 更新代理session")

            # 获取当前代理配置
            proxy_config = self.config['ix_browser']['create_profile']['proxy']

            # 创建新的代理对象（会生成新的session）
            proxy = Proxy()

            if proxy_config['proxy_type'] == 'socks5':
                proxy.change_to_custom_mode(
                    Consts.PROXY_TYPE_SOCKS5,
                    proxy_config['proxy_ip'],
                    proxy_config['proxy_port']
                )
                if proxy_config['proxy_user']:
                    # 生成新的session用户名
                    proxy_username = self._build_proxy_username(proxy_config['proxy_user'])
                    proxy.proxy_user = proxy_username
                if proxy_config['proxy_password']:
                    proxy.proxy_password = proxy_config['proxy_password']
            elif proxy_config['proxy_type'] == 'http':
                proxy.change_to_custom_mode(
                    Consts.PROXY_TYPE_HTTP,
                    proxy_config['proxy_ip'],
                    proxy_config['proxy_port']
                )
                if proxy_config['proxy_user']:
                    # 生成新的session用户名
                    proxy_username = self._build_proxy_username(proxy_config['proxy_user'])
                    proxy.proxy_user = proxy_username
                if proxy_config['proxy_password']:
                    proxy.proxy_password = proxy_config['proxy_password']

            # 更新配置文件
            profile = Profile()
            profile.profile_id = profile_id  # type: ignore
            profile.proxy_config = proxy

            result = self.client.update_profile(profile)

            if result:
                self.logger.info(f"✅ Profile {profile_id} 代理更新成功")
                return True
            else:
                self.logger.error(f"❌ Profile {profile_id} 代理更新失败")
                return False

        except Exception as e:
            self.logger.error(f"更新代理时出现异常: {e}")
            return False

    def _is_proxy_error(self, error_message: str = "") -> bool:
        """
        检测是否为代理相关错误

        Args:
            error_message: 错误信息字符串，可选

        Returns:
            是否为代理错误
        """
        # 代理相关的错误关键词
        proxy_error_keywords = [
            "Proxy detection failed",
            "代理检测失败",
            "Socks5 proxy rejected connection",
            "HostUnreachable",
            "WRONG_VERSION_NUMBER",
            "SSL routines",
            "OPENSSL_internal",
            "Connection Error",
            "write EPROTO",
            "EPROTO",
            "boringssl",
            "tls_record.cc",
            "proxy",
            "1003"  # IX浏览器代理错误代码
        ]

        # 检查错误信息中是否包含代理相关关键词
        if error_message:
            error_lower = error_message.lower()
            for keyword in proxy_error_keywords:
                if keyword.lower() in error_lower:
                    self.logger.debug(f"检测到代理错误关键词: {keyword}")
                    return True

        return False


