"use strict";(()=>{var fe=Object.create;var K=Object.defineProperty;var de=Object.getOwnPropertyDescriptor;var pe=Object.getOwnPropertyNames;var he=Object.getPrototypeOf,me=Object.prototype.hasOwnProperty;var ge=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var ve=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of pe(t))!me.call(e,o)&&o!==n&&K(e,o,{get:()=>t[o],enumerable:!(r=de(t,o))||r.enumerable});return e};var ye=(e,t,n)=>(n=e!=null?fe(he(e)):{},ve(t||!e||!e.__esModule?K(n,"default",{value:e,enumerable:!0}):n,e));var X=ge((Ie,O)=>{"use strict";var g=typeof Reflect=="object"?Reflect:null,U=g&&typeof g.apply=="function"?g.apply:function(t,n,r){return Function.prototype.apply.call(t,n,r)},E;g&&typeof g.ownKeys=="function"?E=g.ownKeys:Object.getOwnPropertySymbols?E=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:E=function(t){return Object.getOwnPropertyNames(t)};function Ce(e){console&&console.warn&&console.warn(e)}var H=Number.isNaN||function(t){return t!==t};function a(){a.init.call(this)}O.exports=a;O.exports.once=Le;a.EventEmitter=a;a.prototype._events=void 0;a.prototype._eventsCount=0;a.prototype._maxListeners=void 0;var W=10;function S(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return W},set:function(e){if(typeof e!="number"||e<0||H(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");W=e}});a.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};a.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||H(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function V(e){return e._maxListeners===void 0?a.defaultMaxListeners:e._maxListeners}a.prototype.getMaxListeners=function(){return V(this)};a.prototype.emit=function(t){for(var n=[],r=1;r<arguments.length;r++)n.push(arguments[r]);var o=t==="error",s=this._events;if(s!==void 0)o=o&&s.error===void 0;else if(!o)return!1;if(o){var i;if(n.length>0&&(i=n[0]),i instanceof Error)throw i;var c=new Error("Unhandled error."+(i?" ("+i.message+")":""));throw c.context=i,c}var f=s[t];if(f===void 0)return!1;if(typeof f=="function")U(f,this,n);else for(var u=f.length,d=G(f,u),r=0;r<u;++r)U(d[r],this,n);return!0};function z(e,t,n,r){var o,s,i;if(S(n),s=e._events,s===void 0?(s=e._events=Object.create(null),e._eventsCount=0):(s.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),i=s[t]),i===void 0)i=s[t]=n,++e._eventsCount;else if(typeof i=="function"?i=s[t]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n),o=V(e),o>0&&i.length>o&&!i.warned){i.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=i.length,Ce(c)}return e}a.prototype.addListener=function(t,n){return z(this,t,n,!1)};a.prototype.on=a.prototype.addListener;a.prototype.prependListener=function(t,n){return z(this,t,n,!0)};function xe(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function q(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},o=xe.bind(r);return o.listener=n,r.wrapFn=o,o}a.prototype.once=function(t,n){return S(n),this.on(t,q(this,t,n)),this};a.prototype.prependOnceListener=function(t,n){return S(n),this.prependListener(t,q(this,t,n)),this};a.prototype.removeListener=function(t,n){var r,o,s,i,c;if(S(n),o=this._events,o===void 0)return this;if(r=o[t],r===void 0)return this;if(r===n||r.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete o[t],o.removeListener&&this.emit("removeListener",t,r.listener||n));else if(typeof r!="function"){for(s=-1,i=r.length-1;i>=0;i--)if(r[i]===n||r[i].listener===n){c=r[i].listener,s=i;break}if(s<0)return this;s===0?r.shift():be(r,s),r.length===1&&(o[t]=r[0]),o.removeListener!==void 0&&this.emit("removeListener",t,c||n)}return this};a.prototype.off=a.prototype.removeListener;a.prototype.removeAllListeners=function(t){var n,r,o;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var s=Object.keys(r),i;for(o=0;o<s.length;++o)i=s[o],i!=="removeListener"&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=r[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(o=n.length-1;o>=0;o--)this.removeListener(t,n[o]);return this};function Q(e,t,n){var r=e._events;if(r===void 0)return[];var o=r[t];return o===void 0?[]:typeof o=="function"?n?[o.listener||o]:[o]:n?we(o):G(o,o.length)}a.prototype.listeners=function(t){return Q(this,t,!0)};a.prototype.rawListeners=function(t){return Q(this,t,!1)};a.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):$.call(e,t)};a.prototype.listenerCount=$;function $(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}a.prototype.eventNames=function(){return this._eventsCount>0?E(this._events):[]};function G(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function be(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function we(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function Le(e,t){return new Promise(function(n,r){function o(i){e.removeListener(t,s),r(i)}function s(){typeof e.removeListener=="function"&&e.removeListener("error",o),n([].slice.call(arguments))}J(e,t,s,{once:!0}),t!=="error"&&ke(e,o,{once:!0})})}function ke(e,t,n){typeof e.on=="function"&&J(e,"error",t,n)}function J(e,t,n,r){if(typeof e.on=="function")r.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function o(s){r.once&&e.removeEventListener(t,o),n(s)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var ee=ye(X());var j,T=0,l=new Array(256);for(let e=0;e<256;e++)l[e]=(e+256).toString(16).substring(1);var _e=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let r=t;r>0;r--)n.push(Math.floor(Math.random()*256));return n}})(),Y=4096;function Z(){(j===void 0||T+16>Y)&&(T=0,j=_e(Y));let e=Array.prototype.slice.call(j,T,T+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,l[e[0]]+l[e[1]]+l[e[2]]+l[e[3]]+"-"+l[e[4]]+l[e[5]]+"-"+l[e[6]]+l[e[7]]+"-"+l[e[8]]+l[e[9]]+"-"+l[e[10]]+l[e[11]]+l[e[12]]+l[e[13]]+l[e[14]]+l[e[15]]}var Ee={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>B(n)+B(e[n])+t,0):0},B=e=>Ee[typeof e](e),x=class extends ee.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(r=>this._emit(r)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,r=>{n({...r,respond:o=>this.send(r.eventResponseKey,o)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],r=`${n.event}.${Z()}`,o=r+".result";return new Promise((s,i)=>{let c=[],f=u=>{if(u!==void 0&&u._chunkSplit){let d=u._chunkSplit;c=[...c,...u.data],d.lastChunk&&(this.off(o,f),s(c))}else this.off(o,f),s(u)};this.on(o,f);try{let u=t.map(d=>({...d,payload:{data:d.payload,eventResponseKey:o}}));this.wall.send(u)}catch(u){let d="Message length exceeded maximum allowed length.";if(u.message===d&&Array.isArray(n.payload)){let y=B(n);if(y>this._maxMessageSize){let m=Math.ceil(y/this._maxMessageSize),p=Math.ceil(n.payload.length/m),A=n.payload;for(let L=0;L<m;L++){let R=Math.min(A.length,p);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:m,lastChunk:L===m-1},data:A.splice(0,R)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var te=(e,t)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===t){let r=n.data[0],o=e.getEvents();for(let s in o)s===r.event&&o[s](r.payload)}},!1)};var Se=chrome.runtime.getURL("assets/config.js"),re,w=(re=globalThis.browser)!=null?re:globalThis.chrome;async function Te(){var I,F;let e=await w.storage.local.get("defaultConfig");if((I=e.defaultConfig)!=null&&I.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],r=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],o=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,c=(await(await fetch(Se)).text()).replace(o,""),f=c.slice(c.indexOf("{")+1,c.lastIndexOf("}")),u=JSON.stringify(f).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),d=u.indexOf("blackUrlList"),y=u.slice(d),m=y.indexOf("],"),p=y.slice(0,m+1);u.replace(p,"").split(",").forEach(ue=>{let[_,N]=ue.split(":");if(_&&N){let C=N.replaceAll("'","").replaceAll('"',"");for(let h=0;h<n.length;h++)_.endsWith(n[h])&&(C=Number(C));for(let h=0;h<r.length;h++)_.startsWith(r[h])&&(C=C==="true");t[_]=C}}),p=p.replaceAll("'","").replaceAll('"',"");let R=p.indexOf(":["),le=p.slice(R+2,p.length-1);t.blackUrlList=le.split(",");let k=await w.storage.local.get("config");return(F=k==null?void 0:k.config)!=null&&F.apiKey&&(t.apiKey=k.config.apiKey),w.storage.local.set({defaultConfig:t}),t}var b={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},ne={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function oe(){let e=await Te(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!ne[n].includes(e[n]))){{if(n.endsWith("Mode")&&!ne.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;b.port=e.port}}Reflect.has(b,n)&&typeof b[n]==typeof e[n]&&(b[n]=e[n])}return b}var Me=oe(),v={default:Me,async get(e){return(await this.getAll())[e]},async getAll(){let e=await oe(),t=await w.storage.local.get("config");return v.joinConfig(e,t.config)},async set(e){let t=await v.getAll(),n=v.joinConfig(t,e);return w.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let r in e)n[r]=e[r];if(t)for(let r in t)n[r]=t[r];return n}};function ie(){let e=document.createElement("div");e.id="capsolver-solver-tip-button",e.classList.add("capsolver-solver"),e.dataset.state="solving";let t=document.createElement("div");t.classList.add("capsolver-solver-image");let n=document.createElement("img");n.src=chrome.runtime.getURL("assets/images/logo_solved.png"),n.alt="",t.appendChild(n);let r=document.createElement("div");return r.classList.add("capsolver-solver-info"),r.innerText=chrome.i18n.getMessage("solving"),e.appendChild(t),e.appendChild(r),e}function M(e,t){let n=document.querySelector("#capsolver-solver-tip-button"),r=n==null?void 0:n.querySelector(".capsolver-solver-info");r&&(r.innerHTML=e),t&&n&&(n.dataset.state=t)}var P="";function Ae(){let e=document.createElement("script");e.src=chrome.runtime.getURL("assets/inject/inject-turnstile.js");let t=document.head||document.documentElement;t.insertBefore(e,t.firstChild)}async function Re(e){let t=await v.getAll();if(t.showSolveButton){let n=ie();if(n.setAttribute("style","position: fixed;right: 20px; bottom: 180px;min-width:220px;"),document.body.appendChild(n),!t.apiKey||t.enabledForBlacklistControl&&t.isInBlackList){M("Please input your API key!","error");return}}t.manualSolving?P=e.data.sitekey:chrome.runtime.sendMessage({action:"solveTurnstile",sitekey:e.data.sitekey,websiteURL:window.location.href}).then(n=>{var r,o,s,i;((o=(r=n==null?void 0:n.response)==null?void 0:r.response)==null?void 0:o.status)==="ready"&&(window==null||window.postMessage({type:"turnstileSolved",token:(i=(s=n==null?void 0:n.response)==null?void 0:s.response)==null?void 0:i.code}),M(chrome.i18n.getMessage("solved"),"solved"))})}window.addEventListener("message",async function(e){var t;if(!!["registerTurnstile","capsolverSolve"].includes(e.data.type)&&!(!((t=e.data)!=null&&t.sitekey)&&!P)){if(e.data.type==="registerTurnstile"){Re(e);return}chrome.runtime.sendMessage({action:"solveTurnstile",sitekey:P,websiteURL:window.location.href}).then(n=>{var r,o,s,i;((o=(r=n==null?void 0:n.response)==null?void 0:r.response)==null?void 0:o.status)==="ready"&&(window==null||window.postMessage({type:"turnstileSolved",token:(i=(s=n==null?void 0:n.response)==null?void 0:s.response)==null?void 0:i.code}),M(chrome.i18n.getMessage("solved"),"solved"))})}});async function Oe(){let e=await v.getAll();!e.useCapsolver||!e.enabledForCloudflare||Ae()}Oe();function je(){let e=document.querySelector(".cf-turnstile");if(!e)return null;let t=e.getAttribute("data-sitekey"),n=e.getAttribute("data-action"),r=e.getAttribute("data-cdata"),o=document.location.href;return{sitekey:t,action:n,cData:r,website:o}}function Be(){let e=Array.from(document.querySelectorAll("iframe[src]")),t="";e.forEach(o=>{o.src.startsWith("https://challenges.cloudflare.com/cdn-cgi/challenge-platform/h/b/turnstile")&&(t=o.src)});let n=t.split("/");return n[n.length-3]}chrome.runtime.onMessage.addListener((e,t,n)=>{if((e==null?void 0:e.command)==="get-cloudflare-info"){let r={sitekey:"",website:document.location.href},o=je();if(!o||!o.sitekey){r.sitekey=Be(),n(r);return}n(o);return}});var se=e=>{};var D=chrome.runtime.connect({name:"contentScript"}),ae=!1;D.onDisconnect.addListener(()=>{ae=!0});var ce=new x({listen(e){D.onMessage.addListener(e)},send(e){ae||(D.postMessage(e),window.postMessage({...e,from:"bex-content-script"},"*"))}});function Pe(e){let t=document.createElement("script");t.src=e,t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)}document instanceof HTMLDocument&&Pe(chrome.runtime.getURL("dom.js"));te(ce,"bex-dom");se(ce);})();
