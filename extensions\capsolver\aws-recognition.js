"use strict";(()=>{var Ee=Object.create;var Q=Object.defineProperty;var ke=Object.getOwnPropertyDescriptor;var Se=Object.getOwnPropertyNames;var Re=Object.getPrototypeOf,_e=Object.prototype.hasOwnProperty;var Pe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ie=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Se(t))!_e.call(e,r)&&r!==n&&Q(e,r,{get:()=>t[r],enumerable:!(o=ke(t,r))||o.enumerable});return e};var Oe=(e,t,n)=>(n=e!=null?Ee(Re(e)):{},Ie(t||!e||!e.__esModule?Q(n,"default",{value:e,enumerable:!0}):n,e));var ce=Pe((rt,F)=>{"use strict";var b=typeof Reflect=="object"?Reflect:null,$=b&&typeof b.apply=="function"?b.apply:function(t,n,o){return Function.prototype.apply.call(t,n,o)},R;b&&typeof b.ownKeys=="function"?R=b.ownKeys:Object.getOwnPropertySymbols?R=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:R=function(t){return Object.getOwnPropertyNames(t)};function Be(e){console&&console.warn&&console.warn(e)}var ee=Number.isNaN||function(t){return t!==t};function i(){i.init.call(this)}F.exports=i;F.exports.once=je;i.EventEmitter=i;i.prototype._events=void 0;i.prototype._eventsCount=0;i.prototype._maxListeners=void 0;var Z=10;function _(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return Z},set:function(e){if(typeof e!="number"||e<0||ee(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");Z=e}});i.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};i.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||ee(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function te(e){return e._maxListeners===void 0?i.defaultMaxListeners:e._maxListeners}i.prototype.getMaxListeners=function(){return te(this)};i.prototype.emit=function(t){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);var r=t==="error",s=this._events;if(s!==void 0)r=r&&s.error===void 0;else if(!r)return!1;if(r){var a;if(n.length>0&&(a=n[0]),a instanceof Error)throw a;var c=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw c.context=a,c}var l=s[t];if(l===void 0)return!1;if(typeof l=="function")$(l,this,n);else for(var u=l.length,p=se(l,u),o=0;o<u;++o)$(p[o],this,n);return!0};function ne(e,t,n,o){var r,s,a;if(_(n),s=e._events,s===void 0?(s=e._events=Object.create(null),e._eventsCount=0):(s.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),a=s[t]),a===void 0)a=s[t]=n,++e._eventsCount;else if(typeof a=="function"?a=s[t]=o?[n,a]:[a,n]:o?a.unshift(n):a.push(n),r=te(e),r>0&&a.length>r&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=a.length,Be(c)}return e}i.prototype.addListener=function(t,n){return ne(this,t,n,!1)};i.prototype.on=i.prototype.addListener;i.prototype.prependListener=function(t,n){return ne(this,t,n,!0)};function De(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function oe(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=De.bind(o);return r.listener=n,o.wrapFn=r,r}i.prototype.once=function(t,n){return _(n),this.on(t,oe(this,t,n)),this};i.prototype.prependOnceListener=function(t,n){return _(n),this.prependListener(t,oe(this,t,n)),this};i.prototype.removeListener=function(t,n){var o,r,s,a,c;if(_(n),r=this._events,r===void 0)return this;if(o=r[t],o===void 0)return this;if(o===n||o.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,o.listener||n));else if(typeof o!="function"){for(s=-1,a=o.length-1;a>=0;a--)if(o[a]===n||o[a].listener===n){c=o[a].listener,s=a;break}if(s<0)return this;s===0?o.shift():Ae(o,s),o.length===1&&(r[t]=o[0]),r.removeListener!==void 0&&this.emit("removeListener",t,c||n)}return this};i.prototype.off=i.prototype.removeListener;i.prototype.removeAllListeners=function(t){var n,o,r;if(o=this._events,o===void 0)return this;if(o.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):o[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete o[t]),this;if(arguments.length===0){var s=Object.keys(o),a;for(r=0;r<s.length;++r)a=s[r],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=o[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(r=n.length-1;r>=0;r--)this.removeListener(t,n[r]);return this};function re(e,t,n){var o=e._events;if(o===void 0)return[];var r=o[t];return r===void 0?[]:typeof r=="function"?n?[r.listener||r]:[r]:n?Fe(r):se(r,r.length)}i.prototype.listeners=function(t){return re(this,t,!0)};i.prototype.rawListeners=function(t){return re(this,t,!1)};i.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):ae.call(e,t)};i.prototype.listenerCount=ae;function ae(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}i.prototype.eventNames=function(){return this._eventsCount>0?R(this._events):[]};function se(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}function Ae(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function Fe(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function je(e,t){return new Promise(function(n,o){function r(a){e.removeListener(t,s),o(a)}function s(){typeof e.removeListener=="function"&&e.removeListener("error",r),n([].slice.call(arguments))}ie(e,t,s,{once:!0}),t!=="error"&&Ne(e,r,{once:!0})})}function Ne(e,t,n){typeof e.on=="function"&&ie(e,"error",t,n)}function ie(e,t,n,o){if(typeof e.on=="function")o.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function r(s){o.once&&e.removeEventListener(t,r),n(s)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var fe=Oe(ce());var j,P=0,f=new Array(256);for(let e=0;e<256;e++)f[e]=(e+256).toString(16).substring(1);var Ue=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let o=t;o>0;o--)n.push(Math.floor(Math.random()*256));return n}})(),le=4096;function ue(){(j===void 0||P+16>le)&&(P=0,j=Ue(le));let e=Array.prototype.slice.call(j,P,P+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,f[e[0]]+f[e[1]]+f[e[2]]+f[e[3]]+"-"+f[e[4]]+f[e[5]]+"-"+f[e[6]]+f[e[7]]+"-"+f[e[8]]+f[e[9]]+"-"+f[e[10]]+f[e[11]]+f[e[12]]+f[e[13]]+f[e[14]]+f[e[15]]}var qe={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>N(n)+N(e[n])+t,0):0},N=e=>qe[typeof e](e),w=class extends fe.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(o=>this._emit(o)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,o=>{n({...o,respond:r=>this.send(o.eventResponseKey,r)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],o=`${n.event}.${ue()}`,r=o+".result";return new Promise((s,a)=>{let c=[],l=u=>{if(u!==void 0&&u._chunkSplit){let p=u._chunkSplit;c=[...c,...u.data],p.lastChunk&&(this.off(r,l),s(c))}else this.off(r,l),s(u)};this.on(r,l);try{let u=t.map(p=>({...p,payload:{data:p.payload,eventResponseKey:r}}));this.wall.send(u)}catch(u){let p="Message length exceeded maximum allowed length.";if(u.message===p&&Array.isArray(n.payload)){let C=N(n);if(C>this._maxMessageSize){let v=Math.ceil(C/this._maxMessageSize),m=Math.ceil(n.payload.length/v),D=n.payload;for(let E=0;E<v;E++){let A=Math.min(D.length,m);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:v,lastChunk:E===v-1},data:D.splice(0,A)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var pe=(e,t)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===t){let o=n.data[0],r=e.getEvents();for(let s in r)s===o.event&&r[s](o.payload)}},!1)};var He=chrome.runtime.getURL("assets/config.js"),me,L=(me=globalThis.browser)!=null?me:globalThis.chrome;async function Ke(){var J,X;let e=await L.storage.local.get("defaultConfig");if((J=e.defaultConfig)!=null&&J.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],o=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],r=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,c=(await(await fetch(He)).text()).replace(r,""),l=c.slice(c.indexOf("{")+1,c.lastIndexOf("}")),u=JSON.stringify(l).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),p=u.indexOf("blackUrlList"),C=u.slice(p),v=C.indexOf("],"),m=C.slice(0,v+1);u.replace(m,"").split(",").forEach(Te=>{let[S,Y]=Te.split(":");if(S&&Y){let x=Y.replaceAll("'","").replaceAll('"',"");for(let g=0;g<n.length;g++)S.endsWith(n[g])&&(x=Number(x));for(let g=0;g<o.length;g++)S.startsWith(o[g])&&(x=x==="true");t[S]=x}}),m=m.replaceAll("'","").replaceAll('"',"");let A=m.indexOf(":["),Le=m.slice(A+2,m.length-1);t.blackUrlList=Le.split(",");let k=await L.storage.local.get("config");return(X=k==null?void 0:k.config)!=null&&X.apiKey&&(t.apiKey=k.config.apiKey),L.storage.local.set({defaultConfig:t}),t}var M={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},de={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function he(){let e=await Ke(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!de[n].includes(e[n]))){{if(n.endsWith("Mode")&&!de.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;M.port=e.port}}Reflect.has(M,n)&&typeof M[n]==typeof e[n]&&(M[n]=e[n])}return M}var We=he(),y={default:We,async get(e){return(await this.getAll())[e]},async getAll(){let e=await he(),t=await L.storage.local.get("config");return y.joinConfig(e,t.config)},async set(e){let t=await y.getAll(),n=y.joinConfig(t,e);return L.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let o in e)n[o]=e[o];if(t)for(let o in t)n[o]=t[o];return n}};function h(e){return new Promise(t=>setTimeout(t,e))}function U(e,t){function n(o,r,s){let a=["mouseover","mousedown","mouseup","click"],c={clientX:r,clientY:s,bubbles:!0};for(let l=0;l<a.length;l++){let u=new MouseEvent(a[l],c);o.dispatchEvent(u)}}e.forEach(o=>{n(t,o.x,o.y)})}var T="",d="",H="",I="",q=0,ge=0;function ye(){let e=document.querySelector("#amzn-btn-verify-internal");if(!e){let t=document.querySelector("awswaf-captcha");if(!t)return null;let n=t.shadowRoot;if(!n)return null;e=n.querySelector("#amzn-btn-verify-internal")}return e.style.display==="none"?null:e}function O(){let e=document.querySelector("canvas");if(!e){let t=document.querySelector("awswaf-captcha");if(!t)return null;let n=t.shadowRoot;if(!n)return null;e=n.querySelector("canvas")}return e}function K(){let e=O();return e?e==null?void 0:e.toDataURL():null}function Ve(){return{image:d==="toycarcity"?T:JSON.parse(T),question:d==="toycarcity"?"aws:toycarcity:carcity":`aws:grid:${H}`}}async function ze(e){var c;let t=(c=e==null?void 0:e.box)!=null?c:[],n=0,o=0,r=[],s=O(),a=s.getBoundingClientRect();for(let l=0;l<t.length;l++)l%2===0?n=t[l]+a.left:(o=t[l]+a.top,r.push({x:n,y:o}));U(r,s),I=K(),await h(500),ve()}function Ge(e){let n=O().getBoundingClientRect(),o=n.width,r=n.height,s=Math.floor(e%3*(o/3)+o/6)+n.left,a=Math.floor(Math.floor(e/3)*(r/3)+r/6)+n.top;return{x:s,y:a}}async function Je(e){var o;let t=(o=e==null?void 0:e.objects)!=null?o:[],n=O();for(let r=0;r<t.length;r++){let s=Ge(t[r]);U([s],n),await h(200)}I=K(),await h(500),ve()}function ve(){let e=ye();e==null||e.click()}function Xe(){return document.querySelector("#captcha-container")&&document.querySelector("#amzn-captcha-verify-button")}function Ye(){document.querySelector("#amzn-captcha-verify-button").click()}async function Qe(){let e=K();return e===I||!ye()?!1:(I=e,!0)}async function $e(){let e=await y.getAll();if(!e.useCapsolver||!e.enabledForAwsCaptcha||!e.apiKey||e.enabledForBlacklistControl&&e.isInBlackList||e.awsCaptchaMode!=="click")return!1;if(ge<q||!d)return;let t=Ve(),n={action:"solver",captchaType:"awsCaptcha",params:t};chrome.runtime.sendMessage(n).then(o=>{var r,s,a;if(!(o!=null&&o.response)||((r=o==null?void 0:o.response)==null?void 0:r.error)){T="",d="",q++,z();return}d==="toycarcity"?ze((s=o.response.response)==null?void 0:s.solution):Je((a=o.response.response)==null?void 0:a.solution)})}function be(){let e=setInterval(async()=>{await Qe()&&(clearInterval(e),await $e())},1e3)}async function W(e){try{let t=JSON.parse(e);if(!(t!=null&&t.problem_type))return;d=t.problem_type,T=t.assets.image||t.assets.images,d!=="toycarcity"&&(H=JSON.parse(t.assets.target)[0]),be()}catch(t){console.error(t)}}function Ze(){let e=document.querySelector("#captcha-container #root form > div:nth-of-type(3)");if(!e)return!1;let n=e.textContent.split(": "),o=n[n.length-1];return Number(o)>1}async function V(e){var t;try{let n=JSON.parse(e);if(n.token)return;if(n.success&&!Ze()){chrome.runtime.sendMessage({action:"solved"});return}if(!((t=n==null?void 0:n.problem)!=null&&t.problem_type))return;d=n.problem.problem_type,T=n.problem.assets.image||n.problem.assets.images,d!=="toycarcity"&&(H=JSON.parse(n.problem.assets.target)[0]),q++,be()}catch(n){console.error(n)}}function z(e){e&&(ge=e.awsRepeatTimes);let t=setInterval(()=>{Xe()&&(Ye(),clearInterval(t))},1e3)}function et(){let e=document.createElement("script");e.src=chrome.runtime.getURL("assets/inject/inject-aws.js");let t=document.head||document.documentElement;if(t.children.length!==0)t.appendChild(e);else{let n=setInterval(()=>{document.querySelector("#amzn-btn-verify-internal")&&(document.head.appendChild(e),clearInterval(n),window.addEventListener("message",function(r){var s,a;if(((s=r==null?void 0:r.data)==null?void 0:s.type)==="xhr"||((a=r==null?void 0:r.data)==null?void 0:a.type)==="fetch"){let c=r.data.url;c.includes("/problem")&&W(r.data.data),c.includes("/verify")&&V(r.data.data)}}),h(1e3).then(()=>{document.querySelector("#amzn-btn-refresh-internal").click()}))},300)}}et();window.addEventListener("message",function(e){var t,n;if(((t=e==null?void 0:e.data)==null?void 0:t.type)==="xhr"||((n=e==null?void 0:e.data)==null?void 0:n.type)==="fetch"){let o=e.data.url;o.includes("/problem")&&W(e.data.data),o.includes("/verify")&&V(e.data.data)}});function Ce(e){z(e)}async function tt(e){!e.useCapsolver||!e.enabledForAwsCaptcha||!e.apiKey||e.enabledForBlacklistControl&&e.isInBlackList||e.awsCaptchaMode!=="click"||(await h(e.awsDelayTime),Ce(e))}var B=null;B&&window.clearInterval(B);B=window.setInterval(async()=>{let e=await y.getAll();!e.isInit||(e.manualSolving?chrome.runtime.onMessage.addListener(t=>{t.command==="execute"&&Ce(e)}):tt(e),window.clearInterval(B))},100);var xe=e=>{};var G=chrome.runtime.connect({name:"contentScript"}),we=!1;G.onDisconnect.addListener(()=>{we=!0});var Me=new w({listen(e){G.onMessage.addListener(e)},send(e){we||(G.postMessage(e),window.postMessage({...e,from:"bex-content-script"},"*"))}});function nt(e){let t=document.createElement("script");t.src=e,t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)}document instanceof HTMLDocument&&nt(chrome.runtime.getURL("dom.js"));pe(Me,"bex-dom");xe(Me);})();
