"use strict";(()=>{var Fe=Object.create;var fe=Object.defineProperty;var je=Object.getOwnPropertyDescriptor;var Be=Object.getOwnPropertyNames;var Ne=Object.getPrototypeOf,Ue=Object.prototype.hasOwnProperty;var We=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ke=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Be(t))!Ue.call(e,r)&&r!==n&&fe(e,r,{get:()=>t[r],enumerable:!(o=je(t,r))||o.enumerable});return e};var Ve=(e,t,n)=>(n=e!=null?Fe(Ne(e)):{},Ke(t||!e||!e.__esModule?fe(n,"default",{value:e,enumerable:!0}):n,e));var we=We((St,G)=>{"use strict";var w=typeof Reflect=="object"?Reflect:null,pe=w&&typeof w.apply=="function"?w.apply:function(t,n,o){return Function.prototype.apply.call(t,n,o)},q;w&&typeof w.ownKeys=="function"?q=w.ownKeys:Object.getOwnPropertySymbols?q=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:q=function(t){return Object.getOwnPropertyNames(t)};function Ge(e){console&&console.warn&&console.warn(e)}var me=Number.isNaN||function(t){return t!==t};function f(){f.init.call(this)}G.exports=f;G.exports.once=Ye;f.EventEmitter=f;f.prototype._events=void 0;f.prototype._eventsCount=0;f.prototype._maxListeners=void 0;var de=10;function D(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(f,"defaultMaxListeners",{enumerable:!0,get:function(){return de},set:function(e){if(typeof e!="number"||e<0||me(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");de=e}});f.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};f.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||me(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function he(e){return e._maxListeners===void 0?f.defaultMaxListeners:e._maxListeners}f.prototype.getMaxListeners=function(){return he(this)};f.prototype.emit=function(t){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);var r=t==="error",a=this._events;if(a!==void 0)r=r&&a.error===void 0;else if(!r)return!1;if(r){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var i=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw i.context=s,i}var c=a[t];if(c===void 0)return!1;if(typeof c=="function")pe(c,this,n);else for(var l=c.length,p=Ce(c,l),o=0;o<l;++o)pe(p[o],this,n);return!0};function ge(e,t,n,o){var r,a,s;if(D(n),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),a=e._events),s=a[t]),s===void 0)s=a[t]=n,++e._eventsCount;else if(typeof s=="function"?s=a[t]=o?[n,s]:[s,n]:o?s.unshift(n):s.push(n),r=he(e),r>0&&s.length>r&&!s.warned){s.warned=!0;var i=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");i.name="MaxListenersExceededWarning",i.emitter=e,i.type=t,i.count=s.length,Ge(i)}return e}f.prototype.addListener=function(t,n){return ge(this,t,n,!1)};f.prototype.on=f.prototype.addListener;f.prototype.prependListener=function(t,n){return ge(this,t,n,!0)};function Qe(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function ye(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=Qe.bind(o);return r.listener=n,o.wrapFn=r,r}f.prototype.once=function(t,n){return D(n),this.on(t,ye(this,t,n)),this};f.prototype.prependOnceListener=function(t,n){return D(n),this.prependListener(t,ye(this,t,n)),this};f.prototype.removeListener=function(t,n){var o,r,a,s,i;if(D(n),r=this._events,r===void 0)return this;if(o=r[t],o===void 0)return this;if(o===n||o.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,o.listener||n));else if(typeof o!="function"){for(a=-1,s=o.length-1;s>=0;s--)if(o[s]===n||o[s].listener===n){i=o[s].listener,a=s;break}if(a<0)return this;a===0?o.shift():ze(o,a),o.length===1&&(r[t]=o[0]),r.removeListener!==void 0&&this.emit("removeListener",t,i||n)}return this};f.prototype.off=f.prototype.removeListener;f.prototype.removeAllListeners=function(t){var n,o,r;if(o=this._events,o===void 0)return this;if(o.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):o[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete o[t]),this;if(arguments.length===0){var a=Object.keys(o),s;for(r=0;r<a.length;++r)s=a[r],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=o[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(r=n.length-1;r>=0;r--)this.removeListener(t,n[r]);return this};function ve(e,t,n){var o=e._events;if(o===void 0)return[];var r=o[t];return r===void 0?[]:typeof r=="function"?n?[r.listener||r]:[r]:n?Xe(r):Ce(r,r.length)}f.prototype.listeners=function(t){return ve(this,t,!0)};f.prototype.rawListeners=function(t){return ve(this,t,!1)};f.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):be.call(e,t)};f.prototype.listenerCount=be;function be(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}f.prototype.eventNames=function(){return this._eventsCount>0?q(this._events):[]};function Ce(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}function ze(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function Xe(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function Ye(e,t){return new Promise(function(n,o){function r(s){e.removeListener(t,a),o(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",r),n([].slice.call(arguments))}xe(e,t,a,{once:!0}),t!=="error"&&$e(e,r,{once:!0})})}function $e(e,t,n){typeof e.on=="function"&&xe(e,"error",t,n)}function xe(e,t,n,o){if(typeof e.on=="function")o.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function r(a){o.once&&e.removeEventListener(t,r),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var Le=Ve(we());var Q,F=0,d=new Array(256);for(let e=0;e<256;e++)d[e]=(e+256).toString(16).substring(1);var Je=(()=>{let e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{let n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{let n=[];for(let o=t;o>0;o--)n.push(Math.floor(Math.random()*256));return n}})(),Te=4096;function Me(){(Q===void 0||F+16>Te)&&(F=0,Q=Je(Te));let e=Array.prototype.slice.call(Q,F,F+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,d[e[0]]+d[e[1]]+d[e[2]]+d[e[3]]+"-"+d[e[4]]+d[e[5]]+"-"+d[e[6]]+d[e[7]]+"-"+d[e[8]]+d[e[9]]+"-"+d[e[10]]+d[e[11]]+d[e[12]]+d[e[13]]+d[e[14]]+d[e[15]]}var Ze={undefined:()=>0,boolean:()=>4,number:()=>8,string:e=>2*e.length,object:e=>e?Object.keys(e).reduce((t,n)=>z(n)+z(e[n])+t,0):0},z=e=>Ze[typeof e](e),L=class extends Le.EventEmitter{constructor(t){super(),this.setMaxListeners(1/0),this.wall=t,t.listen(n=>{Array.isArray(n)?n.forEach(o=>this._emit(o)):this._emit(n)}),this._sendingQueue=[],this._sending=!1,this._maxMessageSize=32*1024*1024}send(t,n){return this._send([{event:t,payload:n}])}getEvents(){return this._events}on(t,n){return super.on(t,o=>{n({...o,respond:r=>this.send(o.eventResponseKey,r)})})}_emit(t){typeof t=="string"?this.emit(t):this.emit(t.event,t.payload)}_send(t){return this._sendingQueue.push(t),this._nextSend()}_nextSend(){if(!this._sendingQueue.length||this._sending)return Promise.resolve();this._sending=!0;let t=this._sendingQueue.shift(),n=t[0],o=`${n.event}.${Me()}`,r=o+".result";return new Promise((a,s)=>{let i=[],c=l=>{if(l!==void 0&&l._chunkSplit){let p=l._chunkSplit;i=[...i,...l.data],p.lastChunk&&(this.off(r,c),a(i))}else this.off(r,c),a(l)};this.on(r,c);try{let l=t.map(p=>({...p,payload:{data:p.payload,eventResponseKey:r}}));this.wall.send(l)}catch(l){let p="Message length exceeded maximum allowed length.";if(l.message===p&&Array.isArray(n.payload)){let u=z(n);if(u>this._maxMessageSize){let h=Math.ceil(u/this._maxMessageSize),g=Math.ceil(n.payload.length/h),v=n.payload;for(let b=0;b<h;b++){let O=Math.min(v.length,g);this.wall.send([{event:n.event,payload:{_chunkSplit:{count:h,lastChunk:b===h-1},data:v.splice(0,O)}}])}}}}this._sending=!1,setTimeout(()=>this._nextSend(),16)})}};var Ee=(e,t)=>{window.addEventListener("message",n=>{if(n.source===window&&n.data.from!==void 0&&n.data.from===t){let o=n.data[0],r=e.getEvents();for(let a in r)a===o.event&&r[a](o.payload)}},!1)};var et=chrome.runtime.getURL("assets/config.js"),Se,k=(Se=globalThis.browser)!=null?Se:globalThis.chrome;async function tt(){var A,le;let e=await k.storage.local.get("defaultConfig");if((A=e.defaultConfig)!=null&&A.apiKey)return e.defaultConfig;let t={},n=["DelayTime","RepeatTimes","port"],o=["enabledFor","useCapsolver","manualSolving","useProxy","showSolveButton"],r=/\/\*[\s\S]*?\*\/|([^:]|^)\/\/.*$/gm,i=(await(await fetch(et)).text()).replace(r,""),c=i.slice(i.indexOf("{")+1,i.lastIndexOf("}")),l=JSON.stringify(c).replaceAll('\\"',"'").replaceAll("\\n","").replaceAll('"',"").replaceAll(" ",""),p=l.indexOf("blackUrlList"),u=l.slice(p),h=u.indexOf("],"),g=u.slice(0,h+1);l.replace(g,"").split(",").forEach(De=>{let[H,ue]=De.split(":");if(H&&ue){let M=ue.replaceAll("'","").replaceAll('"',"");for(let C=0;C<n.length;C++)H.endsWith(n[C])&&(M=Number(M));for(let C=0;C<o.length;C++)H.startsWith(o[C])&&(M=M==="true");t[H]=M}}),g=g.replaceAll("'","").replaceAll('"',"");let O=g.indexOf(":["),P=g.slice(O+2,g.length-1);t.blackUrlList=P.split(",");let x=await k.storage.local.get("config");return(le=x==null?void 0:x.config)!=null&&le.apiKey&&(t.apiKey=x.config.apiKey),k.storage.local.set({defaultConfig:t}),t}var E={manualSolving:!1,apiKey:"",appId:"",enabledForImageToText:!0,enabledForRecaptchaV3:!0,enabledForHCaptcha:!1,enabledForGeetestV4:!1,recaptchaV3MinScore:.5,enabledForRecaptcha:!0,enabledForDataDome:!1,enabledForAwsCaptcha:!0,useProxy:!1,proxyType:"http",hostOrIp:"",port:"",proxyLogin:"",proxyPassword:"",enabledForBlacklistControl:!1,blackUrlList:[],isInBlackList:!1,reCaptchaMode:"click",reCaptchaDelayTime:0,reCaptchaCollapse:!1,reCaptchaRepeatTimes:10,reCaptcha3Mode:"token",reCaptcha3DelayTime:0,reCaptcha3Collapse:!1,reCaptcha3RepeatTimes:10,reCaptcha3TaskType:"ReCaptchaV3TaskProxyLess",hCaptchaMode:"click",hCaptchaDelayTime:0,hCaptchaCollapse:!1,hCaptchaRepeatTimes:10,funCaptchaMode:"click",funCaptchaDelayTime:0,funCaptchaCollapse:!1,funCaptchaRepeatTimes:10,geetestMode:"click",geetestCollapse:!1,geetestDelayTime:0,geetestRepeatTimes:10,textCaptchaMode:"click",textCaptchaCollapse:!1,textCaptchaDelayTime:0,textCaptchaRepeatTimes:10,enabledForCloudflare:!1,cloudflareMode:"click",cloudflareCollapse:!1,cloudflareDelayTime:0,cloudflareRepeatTimes:10,datadomeMode:"click",datadomeCollapse:!1,datadomeDelayTime:0,datadomeRepeatTimes:10,awsCaptchaMode:"click",awsCollapse:!1,awsDelayTime:0,awsRepeatTimes:10,useCapsolver:!0,isInit:!1,solvedCallback:"captchaSolvedCallback",textCaptchaSourceAttribute:"capsolver-image-to-text-source",textCaptchaResultAttribute:"capsolver-image-to-text-result",textCaptchaModule:"common",showSolveButton:!0},ke={proxyType:["socks5","http","https","socks4"],mode:["click","token"]};async function Re(){let e=await tt(),t=Object.keys(e);for(let n of t)if(!(n==="proxyType"&&!ke[n].includes(e[n]))){{if(n.endsWith("Mode")&&!ke.mode.includes(e[n]))continue;if(n==="port"){if(typeof e.port!="number")continue;E.port=e.port}}Reflect.has(E,n)&&typeof E[n]==typeof e[n]&&(E[n]=e[n])}return E}var nt=Re(),S={default:nt,async get(e){return(await this.getAll())[e]},async getAll(){let e=await Re(),t=await k.storage.local.get("config");return S.joinConfig(e,t.config)},async set(e){let t=await S.getAll(),n=S.joinConfig(t,e);return k.storage.local.set({config:n})},joinConfig(e,t){let n={};if(e)for(let o in e)n[o]=e[o];if(t)for(let o in t)n[o]=t[o];return n}};function X(e){return new Promise((t,n)=>{let o=new Image;o.src=e,o.setAttribute("crossOrigin","anonymous"),o.onload=()=>{let r=document.createElement("canvas");r.width=o.width,r.height=o.height,r.getContext("2d").drawImage(o,0,0,o.width,o.height);let s=r.toDataURL();t(s)},o.onerror=r=>{n(r)}})}function y(e){return new Promise(t=>setTimeout(t,e))}function m(e,t){let n=t-e+1;return Math.floor(Math.random()*n+e)}function Y(e){let t=e==null?void 0:e.getBoundingClientRect();return t?{x:t.top+window.scrollY-document.documentElement.clientTop+m(-5,5),y:t.left+window.scrollX-document.documentElement.clientLeft+m(-5,5)}:{x:0,y:0}}var _e=["Error: ERROR_UNSUPPORTED_QUESTION"];function ot(e,t,n,o,r){let[a,s]=t,[i,c]=r,[l,p]=n,[u,h]=o,g=a*(1-e)*(1-e)*(1-e)+3*l*e*(1-e)*(1-e)+3*u*e*e*(1-e)+i*e*e*e,v=s*(1-e)*(1-e)*(1-e)+3*p*e*(1-e)*(1-e)+3*h*e*e*(1-e)+c*e*e*e;return[g,v]}function rt(e,t,n=30){let o=[],r=0,a=1;for(let u=0;u<n;++u)o.push(r),u<n*1/10?a+=m(60,100):u>=n*9/10&&(a-=m(60,100),a=Math.max(20,a)),r+=a;let s=[],i=[e.x,e.y],c=[(e.x+t.x)/2+m(30,100)*1,(e.y+t.y)/2+m(30,100)*1],l=[(e.x+t.x)/2+m(30,100)*1,(e.y+t.y)/2+m(30,100)*1],p=[t.x,t.y];for(let u of o){let[h,g]=ot(u/r,i,c,l,p);s.push({x:h,y:g})}return s}function st(e,t){let n=rt(e,t,m(15,30));for(let o=0;o<n.length;o++)document.body.dispatchEvent(new MouseEvent("mousemove",{bubbles:!0,clientX:n[o].x,clientY:n[o].y}))}function at({x:e,y:t}){document.body.dispatchEvent(new MouseEvent("mousedown",{bubbles:!0,clientX:e,clientY:t}))}function it({x:e,y:t}){document.body.dispatchEvent(new MouseEvent("mouseup",{bubbles:!0,clientX:e,clientY:t}))}async function ct(e,t){st(e,t),await y(m(30,80)),at(t),await y(m(30,80)),it(t)}function Ie(e,t){function n(o,r,a){let s=["mouseover","mousedown","mouseup","click"],i={clientX:r,clientY:a,bubbles:!0};for(let c=0;c<s.length;c++){let l=new MouseEvent(s[c],i);o.dispatchEvent(l)}}e.forEach(o=>{n(t,o.x,o.y)})}async function lt(e){for(let t=0;t<e.length-1;t++)await ct(e[t],e[t+1])}function ut(e,t,n){let r=[n?Y(n):{x:t?m(420,530):m(10,100),y:t?m(200,300):m(5,200)}];for(let a=0;a<e.length;a++){let s=Y(e[a]);r.push(s)}return r}async function j(e,t=null){let n=ut(e,!1,t);await lt(n)}var B="",$="",T="",N=0,U=null,R=!1,J=!1;function W(){return document.querySelectorAll(".task-grid > .task-image > .image-wrapper > .image").length===9||document.querySelectorAll(".task-grid .task-image .wrapper .image").length===9}function I(){return document.querySelector(".task-answers")!==null}function Z(){return document.querySelector("canvas")!==null}function ft(){var t;let e=((t=document.querySelector("div.check"))==null?void 0:t.style.display)==="block";return e&&(N=0,!J&&chrome.runtime.sendMessage({action:"solved"}),J=!0),e}function pt(){let e=document.querySelector(".display-error");return String(e==null?void 0:e.style.opacity)==="1"}function dt(){var e;(e=document.querySelector("#checkbox"))==null||e.click()}function _(e){let t=e==null?void 0:e.style.background;return t==null?void 0:t.slice(t.indexOf("http"),t.indexOf('")'))}function mt(){let e=null;return e&&(window.clearInterval(e),e=null),new Promise(t=>{let n=[],o=[];W()?o=Array.from(document.querySelectorAll(".task-image")):I()&&(o=Array.from(document.querySelectorAll(".task-answers .challenge-answer"))),e=window.setInterval(()=>{for(let a of o){let s=a==null?void 0:a.querySelector("div.image"),i=_(s);n.push(i)}n.every(a=>!!a)?(window.clearInterval(e),t(n)):n=[]},500)})}async function ht(){let e=[];if(W()){let t=await mt(),n=t.length;for(let o=0;o<n;o++)try{let r=await X(t[o]);e.push(r.slice(r.indexOf(";base64,")+8))}catch(r){console.log(r)}}else if(I()){let t=document.querySelector(".challenge-task > .task-image > .image-wrapper > .image"),n=_(t),o=await X(n);e.unshift(o.slice(o.indexOf(";base64,")+8))}else if(Z()){let t=await Oe();e.push(t)}return e}function gt(){var t;if(I())return T;if($)return $;let e=document.querySelector(".prompt-text");return(t=e==null?void 0:e.innerText)!=null?t:e.innerHTML}async function yt(){let e=await ht(),t=gt();return{queries:e,question:t}}function ee(){let e=document.querySelector(".button-submit");e==null||e.click(),j([e],U),U=null,R=!1}async function vt(e){var a;let t=e.objects,n=Array.from(document.querySelectorAll(".task-image")),o=t.length,r=[];for(let s=0;s<o;s++)t[s]&&(await y(100),(a=n[s])==null||a.click(),r.push(n[s]));await j(r),U=r[r.length-1],await y(500),ee()}async function bt(e){let t=e==null?void 0:e.tags,n=t[0],o=Array.from(document.querySelectorAll(".answer-text")),r=[];function a(s){["mouseover","mousedown","mouseup","click"].forEach(i=>{let c=new MouseEvent(i,{bubbles:!0,cancelable:!1});s.dispatchEvent(c)})}for(let s=1;s<t.length;s++)t[s]===n&&(await y(100),a(o[s-1]),r.push(o[s-1]));await j(r),U=r[r.length-1],await y(500),ee()}async function Ct(e){var p;let t=(p=e==null?void 0:e.box)!=null?p:[],n=[],o=document.querySelector("canvas"),a=document.querySelector(".challenge-prompt").getBoundingClientRect(),i=document.querySelector(".bounding-box-example").getBoundingClientRect(),c=0,l=0;for(let u=0;u<t.length;u++)u%2!==0?c+=t[u]:l+=t[u];l=l/2,c=c/2+(a.height+i.height),n.push({x:l,y:c}),Ie(n,o),await y(500),ee()}async function Oe(){let e=document.querySelector("canvas");if(!e)return null;let[t,n]=[e.width,e.height],r=e.getContext("2d",{willReadFrequently:!0}).getImageData(0,0,t,n);if(Array.from(r.data).every((x,A)=>A%4===3||x===0))return console.log("The original canvas has no valid content"),null;let s=parseInt(e.style.width,10),i=parseInt(e.style.height,10);if(s<=0||i<=0)return console.log("Desired width and height should be positive numbers"),null;let c=Math.min(s/t,i/n),[l,p]=[t*c,n*c],u=document.querySelector(".bounding-box-example"),h=u==null?void 0:u.style.top.replace("px",""),g=u==null?void 0:u.style.height.replace("px",""),v=Number(h)+Number(g),b=document.createElement("canvas");Object.assign(b,{width:l,height:p}),b.getContext("2d").drawImage(e,0,v,t,n-v,0,0,l,p-v);let P=b.toDataURL("image/jpeg",.4);return P.slice(P.indexOf(";base64,")+8)}function te(){return document.querySelector("#checkbox")!==null}function ne(){ft()||(dt(),J=!1)}function oe(){return document.querySelector(".challenge")!==null}function re(e=1e4){return new Promise(t=>{let n=Date.now(),o=Array.from(document.querySelectorAll(".task-image")),r=Array.from(document.querySelectorAll(".task-answers > .answer-example > .image-wrapper"));document.querySelector("canvas")!==null&&t(!0);let s=o.length!==0?o:r,i=[];s.length===0&&t(!1);let c=null;c&&window.clearInterval(c),c=window.setInterval(()=>{Date.now()-n>e&&t(!1);for(let p of s){let u=p==null?void 0:p.querySelector("div.image"),h=_(u);i.push(h)}i.every(p=>!!p)?(window.clearInterval(c),t(!0)):(i=[],window.clearInterval(c),t(!1))},100)})}function xt(){let e=document.querySelector(".refresh");e==null||e.click()}async function se(e){if(e<N)return!1;let t=document.querySelector("h2.prompt-text");if(!(t==null?void 0:t.innerText))return console.log("task text error--"),!1;let o=[];if(W()){let a=Array.from(document.querySelectorAll(".task-image"));if(a.length!==9)return console.log("grid cells error--"),!1;for(let s of a){let i=s==null?void 0:s.querySelector("div.image"),c=_(i);o.push(c)}}else if(I()){let a=_(document.querySelector(".task-image .image"));if(!a)return console.log("multi bg error--"),!1;o.push(a)}else if(Z()){let a=await Oe();if(!a||R){console.log("canvas error--");let s=JSON.stringify(o);return B!==s&&(R=!1),!1}else o.push(a),R=!0}else return!1;let r=JSON.stringify(o);return B===r?!1:(B=r,!0)}async function ae(){pt()&&N++;let e=await yt(),t={action:"solver",captchaType:"hCaptcha",params:e};chrome.runtime.sendMessage(t).then(n=>{var o,r,a;if(!(n!=null&&n.response)||((o=n==null?void 0:n.response)==null?void 0:o.error)){_e.includes((r=n==null?void 0:n.response)==null?void 0:r.error)&&xt(),B="",N++,R=!1;return}wt((a=n.response.response)==null?void 0:a.solution)})}function wt(e){W()?vt(e):I()?bt(e):Z()&&Ct(e)}function Pe(e){var t;try{let n=JSON.parse(e);$=(t=n==null?void 0:n.requester_question)==null?void 0:t.en,typeof(n==null?void 0:n.requester_restricted_answer_set)=="object"&&(T="",Object.keys(n.requester_restricted_answer_set).forEach(r=>{T+=n.requester_restricted_answer_set[r].en+"$"}),T=T.slice(0,T.length-1))}catch{console.log("Get question failed")}}var ie=document.createElement("script");ie.src=chrome.runtime.getURL("assets/inject/inject-hcaptcha.js");ie.dataset.from="hcaptcha";var Tt=document.head||document.documentElement;Tt.appendChild(ie);window.addEventListener("message",function(e){var t,n;(((t=e==null?void 0:e.data)==null?void 0:t.type)==="xhr"||((n=e==null?void 0:e.data)==null?void 0:n.type)==="fetch")&&Pe(e.data.data)});var K=!1;async function Mt(e){!e.useCapsolver||!e.enabledForHCaptcha||!e.apiKey||e.enabledForBlacklistControl&&e.isInBlackList||e.hCaptchaMode!=="click"||(await y(e.hCaptchaDelayTime),setInterval(async()=>{if(K||(te()&&ne(),!oe())||!await re())return;if(K=!0,!await se(e.hCaptchaRepeatTimes)){K=!1;return}await ae(),await y(2e3),K=!1},2500))}async function Lt(e){setInterval(async()=>{te()&&ne(),!(!oe()||!await re()||!await se(e.hCaptchaRepeatTimes))&&await ae()},2500)}var V=null;V&&window.clearInterval(V);V=window.setInterval(async()=>{let e=await S.getAll();!e.isInit||(e.manualSolving?chrome.runtime.onMessage.addListener(t=>{t.command==="execute"&&Lt(e)}):Mt(e),window.clearInterval(V))},100);var Ae=e=>{};var ce=chrome.runtime.connect({name:"contentScript"}),He=!1;ce.onDisconnect.addListener(()=>{He=!0});var qe=new L({listen(e){ce.onMessage.addListener(e)},send(e){He||(ce.postMessage(e),window.postMessage({...e,from:"bex-content-script"},"*"))}});function Et(e){let t=document.createElement("script");t.src=e,t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)}document instanceof HTMLDocument&&Et(chrome.runtime.getURL("dom.js"));Ee(qe,"bex-dom");Ae(qe);})();
